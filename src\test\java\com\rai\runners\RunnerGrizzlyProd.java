package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Grizzly", glue = { "com.rai.steps" }, tags = "@GrizzlyCouponHome_Prod or @GrizzlyCouponSort_Prod or @GrizzlyCouponHomeTabs_Prod or @GrizzlyCouponvalidateRestrictions_Prod or "
		+ "@GrizzlyValidateRedeemNotNow_Prod or @GrizzlyValidatefavouriteStore_Prod or @GrizzlyValidateErrorMessageNotnearbyStore_Prod or @GrizzlyMapViewCouponRedemtion_Prod or "
		+ "@NavigateGrizzlyMobilesiteHamburgerMenu_Prod or @GrizzlyLogoutfromHamburgerMenu_Prod or @GrizzlyValidateHamburgerMenu_Prod or "
		+ "@GrizzlyAccessToBrowserAndDeviceLocation_Prod or "
		+ "@GrizzlySGWValidations_Prod or "
		+ "@GrizzlyStoreListView_Prod or @GrizzlyStoreListSearchByZip_Prod or @GrizzlyStoreDetailsMapView_Prod or @GrizzlyStoreMapViewbyZip_Prod",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

// VeloValidateErrorMessageNotnearbyStore_QA
public class RunnerGrizzlyProd extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
