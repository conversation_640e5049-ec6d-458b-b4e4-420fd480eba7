Feature: Newport SPA - Location Services
As a RJR user, I shall be validating Newport Location Services Functionality

@NewportValidateLocationServices
  Scenario Outline: Validate Location Services And the navigation to Newport Coupon Page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I click on the Understood button location service
       And I click on the Learn how 
    And I click on the continue button
    And I Navigate to Chrome App Loc
    And I Navigate to Website Loc
    And I Naviagte to Clearing cache
    Then I click the back to the Newport page
      
      
      
      
      
      
      
      
       @NewportValidateLocation_QA
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                     | Username                         | Password  | Loc                                            | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      