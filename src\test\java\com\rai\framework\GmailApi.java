package com.rai.framework;

import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.extensions.java6.auth.oauth2.AuthorizationCodeInstalledApp;
import com.google.api.client.extensions.jetty.auth.oauth2.LocalServerReceiver;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.FileDataStoreFactory;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.GmailScopes;
import com.google.api.services.gmail.model.ListMessagesResponse;
import com.google.api.services.gmail.model.Message;
import com.google.api.services.gmail.model.MessagePartHeader;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Properties;
import java.util.logging.Logger;
import java.util.logging.Level;

import jakarta.mail.MessagingException;
import jakarta.mail.Multipart;
import jakarta.mail.Part;
import jakarta.mail.Session;
import jakarta.mail.internet.MimeMessage;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

/**
 * Enhanced Gmail API client for retrieving and processing emails
 * Compatible with Java 21 and modern practices
 */
public class GmailApi {
    private static final Logger logger = Logger.getLogger(GmailApi.class.getName());
    
    private static final String APPLICATION_NAME = "Gmail API Java Client";
    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    private static final String TOKENS_DIRECTORY_PATH = Paths.get(System.getProperty("user.dir"), 
            "src", "test", "resources", "Tokenscheck").toString();
    
    private static final List<String> SCOPES = Collections.singletonList(GmailScopes.GMAIL_READONLY);
    private static final String CREDENTIALS_FILE_PATH = Paths.get(System.getProperty("user.dir"), 
            "src", "test", "resources", "GoogleKey", "rjrautomationtestKey.json").toString();
    
    private Gmail service;
    
    /**
     * Constructor that initializes the Gmail service
     */
    public GmailApi() throws Exception {
        this.service = initializeGmailService();
    }
    
    /**
     * Initialize Gmail service with proper error handling
     */
    private Gmail initializeGmailService() throws Exception {
        try {
            final NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
            return new Gmail.Builder(httpTransport, JSON_FACTORY, getCredentials(httpTransport))
                    .setApplicationName(APPLICATION_NAME)
                    .build();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to initialize Gmail service", e);
            throw new Exception("Failed to initialize Gmail service: " + e.getMessage(), e);
        }
    }
    
    /**
     * Creates an authorized Credential object
     */
    private static Credential getCredentials(final NetHttpTransport httpTransport) throws IOException {
        try (FileInputStream in = new FileInputStream(new File(CREDENTIALS_FILE_PATH))) {
            GoogleClientSecrets clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new InputStreamReader(in));
            
            GoogleAuthorizationCodeFlow flow = new GoogleAuthorizationCodeFlow.Builder(
                    httpTransport, JSON_FACTORY, clientSecrets, SCOPES)
                    .setDataStoreFactory(new FileDataStoreFactory(new File(TOKENS_DIRECTORY_PATH)))
                    .setAccessType("offline")
                    .build();
            
            LocalServerReceiver receiver = new LocalServerReceiver.Builder().setPort(8888).build();
            return new AuthorizationCodeInstalledApp(flow, receiver).authorize("user");
        } catch (IOException e) {
            logger.log(Level.SEVERE, "Failed to load credentials", e);
            throw new IOException("Failed to load credentials from: " + CREDENTIALS_FILE_PATH, e);
        }
    }
    
    /**
     * Extract text content from email parts with improved handling
     */
    private static String extractTextFromPart(Part part) throws MessagingException, IOException {
        if (part.isMimeType("text/plain")) {
            return (String) part.getContent();
        }
        
        if (part.isMimeType("text/html")) {
            String htmlContent = (String) part.getContent();
            // Convert HTML to plain text while preserving links
            Document doc = Jsoup.parse(htmlContent);
            return doc.text();
        }
        
        if (part.isMimeType("multipart/alternative")) {
            Multipart multipart = (Multipart) part.getContent();
            String plainText = null;
            String htmlText = null;
            
            for (int i = 0; i < multipart.getCount(); i++) {
                Part bodyPart = multipart.getBodyPart(i);
                if (bodyPart.isMimeType("text/plain")) {
                    plainText = extractTextFromPart(bodyPart);
                } else if (bodyPart.isMimeType("text/html")) {
                    htmlText = extractTextFromPart(bodyPart);
                }
            }
            
            // Prefer HTML content if available, otherwise use plain text
            return htmlText != null ? htmlText : plainText;
        }
        
        if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();
            for (int i = 0; i < multipart.getCount(); i++) {
                String content = extractTextFromPart(multipart.getBodyPart(i));
                if (content != null && !content.trim().isEmpty()) {
                    return content;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Get email content from message ID with improved error handling
     */
    public String getEmailContent(String messageId) throws Exception {
        try {
            Message message = service.users().messages().get("me", messageId).setFormat("raw").execute();
            byte[] emailBytes = org.apache.commons.codec.binary.Base64.decodeBase64(message.getRaw());
            
            Properties props = new Properties();
            Session session = Session.getDefaultInstance(props, null);
            MimeMessage email = new MimeMessage(session, new ByteArrayInputStream(emailBytes));
            
            return extractTextFromPart(email);
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to get email content for message ID: " + messageId, e);
            throw new Exception("Failed to retrieve email content: " + e.getMessage(), e);
        }
    }
    
    /**
     * Get the latest email matching a subject
     */
    public Optional<Message> getLatestEmailBySubject(String subject) {
        try {
            String query = "subject:\"" + subject + "\"";
            ListMessagesResponse response = service.users().messages().list("me")
                    .setQ(query)
                    .setMaxResults(1L) // Get only the latest email
                    .execute();
            
            List<Message> messages = response.getMessages();
            if (messages != null && !messages.isEmpty()) {
                return Optional.of(messages.get(0));
            }
            
            return Optional.empty();
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to get latest email by subject: " + subject, e);
            return Optional.empty();
        }
    }
    
    /**
     * Get the latest email from inbox
     */
    public Optional<Message> getLatestEmail() {
        try {
            ListMessagesResponse response = service.users().messages().list("me")
                    .setMaxResults(1L)
                    .execute();
            
            List<Message> messages = response.getMessages();
            if (messages != null && !messages.isEmpty()) {
                return Optional.of(messages.get(0));
            }
            
            return Optional.empty();
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to get latest email", e);
            return Optional.empty();
        }
    }
    
    /**
     * Get detailed message information including headers
     */
    public Optional<MessageDetails> getMessageDetails(String messageId) {
        try {
            Message message = service.users().messages().get("me", messageId).execute();
            
            String subject = "";
            String from = "";
            String date = "";
            
            if (message.getPayload() != null && message.getPayload().getHeaders() != null) {
                for (MessagePartHeader header : message.getPayload().getHeaders()) {
                    switch (header.getName().toLowerCase()) {
                        case "subject" -> subject = header.getValue();
                        case "from" -> from = header.getValue();
                        case "date" -> date = header.getValue();
                    }
                }
            }
            
            // Convert internal date to readable format
            String formattedDate = formatInternalDate(message.getInternalDate());
            
            return Optional.of(new MessageDetails(messageId, subject, from, date, formattedDate, message.getSnippet()));
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to get message details for ID: " + messageId, e);
            return Optional.empty();
        }
    }
    
    /**
     * Check if email exists with given subject
     */
    public boolean isEmailExists(String subject) {
        return getLatestEmailBySubject(subject).isPresent();
    }
    
    /**
     * Extract links from email content, specifically looking for password reset links
     */
    public List<String> extractLinksFromEmail(String messageId, String linkText) throws Exception {
        List<String> links = new ArrayList<>();
        
        try {
            String emailContent = getEmailContent(messageId);
            if (emailContent == null) {
                return links;
            }
            
            Document doc = Jsoup.parse(emailContent);
            Elements linkElements = doc.select("a[href]");
            
            for (var element : linkElements) {
                String text = element.text().toLowerCase();
                String href = element.attr("href");
                
                if (linkText == null || text.contains(linkText.toLowerCase())) {
                    links.add(href);
                }
            }
            
            return links;
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to extract links from email: " + messageId, e);
            throw new Exception("Failed to extract links: " + e.getMessage(), e);
        }
    }
    
    /**
     * Get password reset link from latest email with given subject
     */
    public Optional<String> getPasswordResetLink(String subject) {
        try {
            Optional<Message> latestEmail = getLatestEmailBySubject(subject);
            if (latestEmail.isEmpty()) {
                logger.info("No email found with subject: " + subject);
                return Optional.empty();
            }
            
            List<String> links = extractLinksFromEmail(latestEmail.get().getId(), "click here");
            if (!links.isEmpty()) {
                return Optional.of(links.get(0));
            }
            
            // If no "click here" link found, try to find any link that looks like a reset link
            links = extractLinksFromEmail(latestEmail.get().getId(), null);
            for (String link : links) {
                if (link.contains("reset") || link.contains("password") || link.contains("recover")) {
                    return Optional.of(link);
                }
            }
            
            return Optional.empty();
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to get password reset link for subject: " + subject, e);
            return Optional.empty();
        }
    }
    
    /**
     * Format internal date to readable format
     */
    private String formatInternalDate(Long internalDate) {
        if (internalDate == null) {
            return "";
        }
        
        LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(internalDate), 
                ZoneId.systemDefault()
        );
        
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * Record class to hold message details
     */
    public record MessageDetails(
            String id,
            String subject,
            String from,
            String date,
            String formattedDate,
            String snippet
    ) {}
    
    /**
     * Example usage and testing
     */
    public static void main(String[] args) {
        try {
            GmailApi gmailApi = new GmailApi();
            
            // Get latest email
            Optional<Message> latestEmail = gmailApi.getLatestEmail();
            if (latestEmail.isPresent()) {
                System.out.println("Latest email ID: " + latestEmail.get().getId());
                
                Optional<MessageDetails> details = gmailApi.getMessageDetails(latestEmail.get().getId());
                details.ifPresent(d -> {
                    System.out.println("Subject: " + d.subject());
                    System.out.println("From: " + d.from());
                    System.out.println("Date: " + d.formattedDate());
                    System.out.println("Snippet: " + d.snippet());
                });
            }
            
            // Check for specific email and get reset link
            String testSubject = "Testing List Response";
            Optional<String> resetLink = gmailApi.getPasswordResetLink(testSubject);
            if (resetLink.isPresent()) {
                System.out.println("Password reset link: " + resetLink.get());
            } else {
                System.out.println("No password reset link found for subject: " + testSubject);
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error in main method", e);
        }
    }
}