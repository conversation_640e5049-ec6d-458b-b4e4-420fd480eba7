Feature: Vuse RBDS - Hamburger Menu
As a RJR user, I shall be validating Vuse Coupon Hamburger Functionality

@VuseValidateHamburgerMenu
  Scenario Outline: Validate Hamburger Menu And the navigation to Vuse Coupon Page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I validate the elements in Hamburger Menu
      And I click on coupons link
     Then I validate I'm on the Coupons Home Page  
  
  @VuseValidateHamburgerMenu_QA
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://aem-stage.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Vuse  | Murphy               | 741592   | https://aem-stage.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Vuse  | Sheetz               | 595324   | https://aem-stage.vusevapor.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Vuse  | Speedway             | 529181   | https://aem-stage.vusevapor.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @VuseValidateHamburgerMenu_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://login.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Vuse  | Murphy               | 741592   | https://login.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Vuse  | Sheetz               | 595324   | https://login.vusevapor.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Vuse  | Speedway             | 529181   | https://login.vusevapor.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @VuseLogoutfromHamburgerMenu
  Scenario Outline: Validate that user can logout from the Vuse Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I click on logout link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @VuseLogoutfromHamburgerMenu_QA    
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://aem-stage.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Vuse  | Murphy               | 741592   | https://aem-stage.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Vuse  | Sheetz               | 595324   | https://aem-stage.vusevapor.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Vuse  | Speedway             | 529181   | https://aem-stage.vusevapor.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @VuseLogoutfromHamburgerMenu_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://login.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Vuse  | Murphy               | 741592   | https://login.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Vuse  | Sheetz               | 595324   | https://login.vusevapor.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Vuse  | Speedway             | 529181   | https://login.vusevapor.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @NavigateVuseMobilesiteHamburgerMenu 
  Scenario Outline: Validate that user can navigate to Vuse brand mobile site from the Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
      And I click on the Understood button
      And I navigate to Hamburger menu
      And I click on brand link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @NavigateVuseMobilesiteHamburgerMenu_QA      
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://aem-stage.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Vuse  | Murphy               | 741592   | https://aem-stage.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Vuse  | Sheetz               | 595324   | https://aem-stage.vusevapor.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Vuse  | Speedway             | 529181   | https://aem-stage.vusevapor.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @NavigateVuseMobilesiteHamburgerMenu_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://login.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Vuse  | Murphy               | 741592   | https://login.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Vuse  | Sheetz               | 595324   | https://login.vusevapor.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Vuse  | Speedway             | 529181   | https://login.vusevapor.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  
