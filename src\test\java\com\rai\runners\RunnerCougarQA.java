package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Cougar", glue = { "com.rai.steps" }, tags = "@CougarValidateContentPostRedemption_QA or "
		+ "@CougarCouponHome_QAstage or @CougarCouponSort_QAStage or  @CougarCouponvalidateRestrictions_QAStage or "
		+ "@CougarRedeemAtAny0.1MileStore_QA or @CougarValidateRedeemNotNow_QA or @CougarValidatefavouriteStore_QA or @CougarValidateErrorMessageNotnearbyStore_QA or @CougarMapViewCouponRedemtion_QA or "
		+ "@CougarValidateHamburgerMenu_QA or @CougarLogoutHamburgerMenu_QA or @CougarNavigateMobilesiteHamburgerMenu_QA or "
		+ "@CougarSGWValidations_QA or "
		+ "@CougarStoreListView_QA or @CougarStoreListSearchByZip_QA or @CougarStoreDetailsMapView_QA or @CougarStoreMapViewbyZip_QA",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

// VeloValidateErrorMessageNotnearbyStore_QA
public class RunnerCougarQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
