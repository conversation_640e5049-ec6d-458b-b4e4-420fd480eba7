Feature: Cougar RBDS - Coupon Home
As an ATC user, I should be able to successfully validate the coupon home page for the RBDS Flow for the Brand <Brand> and the Retailer <Retailer>

@CougarValidateCouponHome
  Scenario Outline: Validate the RBDS Coupon Home Page and availability of the coupons for the retailer <Retailer> with RBDS code <RBDScode>
      Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I validate the Coupons Home Page
      
@CougarCouponHome_QAstage        	
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   					|Env	|
     	| Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL> 	| Password1 |  623 East Jackson Boulevard, Elkhart, IN, USA			   				| QA	|
      #| Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 448 E MCCAIN BLVD, North Little Rock, AR 72117	|	QA	|
      #| Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>		   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405  		|	QA	|

@CougarCouponHome_Prod
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   					| Env	 |
     	| Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	| <EMAIL> 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA				   				| Prod |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 448 E MCCAIN BLVD, North Little Rock, AR 72117	| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	| <EMAIL>		   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405  		| Prod |

@CougarCouponSort
   Scenario Outline: Validate that the user is able to sort the coupons by value and expiration date for the Brand <Brand> and the Retailer <Retailer>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
     Then I validate the default view of the coupons sorted by value
      And I sort the coupons by expiration date and validate the coupons
  
@CougarCouponSort_QAStage
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   					|Env	|
     	| Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL> 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA			   				| QA	|
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 448 E MCCAIN BLVD, North Little Rock, AR 72117	|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>		   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405  		|	QA	|
 
@CougarCouponSort_Prod
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   					|Env	 |
     	| Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	| <EMAIL> 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA			   				| Prod |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 448 E MCCAIN BLVD, North Little Rock, AR 72117	| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	| <EMAIL>		   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405  		| Prod |
 
@CougarCouponvalidateRestrictions      
   Scenario Outline: Validate the user is displayed with error message when he tries to access the cougardips coupon from the restricted states location for the Brand <Brand> and the Retailer <Retailer>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate the error popup stating that coupons are not available for restricted location for the brand <Brand>

@CougarCouponvalidateRestrictions_QAStage 
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                              	|Env	|
     	| Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL> 	| Password1 | 2 Ferry St, Newark, NJ 07105, USA	| QA	|
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 2 Ferry St, Newark, NJ 07105, USA	|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>		   			| Password1 | 2 Ferry St, Newark, NJ 07105, USA	|	QA	|

@CougarCouponvalidateRestrictions_Prod
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              			| Password  | Loc                              	|Env	 |
     	| Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	| <EMAIL> 	| Password1 | 2 Ferry St, Newark, NJ 07105, USA	| Prod |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 2 Ferry St, Newark, NJ 07105, USA	| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	| <EMAIL>		   			| Password1 | 2 Ferry St, Newark, NJ 07105, USA	| Prod |


 @Cougar_CopyrightTextinLoginPage
Scenario Outline: Validate the user is able to view the Copyright text in Cougar login page 
	 Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
	 And I scroll down and validate copyright text <copyrightText> for <Brand>
	 
	 
	 @Cougar_CopyrightTextinLoginPage_QA
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | COUGAR | 7-Eleven Corporation | 572896   | https://aem-stage.cougardips.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | COUGAR | Murphy               | 741592   | https://aem-stage.cougardips.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | COUGAR | Sheetz               | 595324   | https://aem-stage.cougardips.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | COUGAR | Speedway             | 529181   | https://aem-stage.cougardips.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
		
	 
	 @Cougar_CopyrightTextinLoginPage_Prod
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | COUGAR | 7-Eleven Corporation | 572896   | https://www.cougardips.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | COUGAR | Murphy               | 741592   | https://www.cougardips.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | COUGAR | Sheetz               | 595324   | https://www.cougardips.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | COUGAR | Speedway             | 529181   | https://www.cougardips.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
	