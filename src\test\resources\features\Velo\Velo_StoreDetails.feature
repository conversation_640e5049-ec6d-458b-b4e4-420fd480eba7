Feature: Velo RBDS - Store Details
As a RJR user, The user will be able to view the camel store by list and Mapview

@VeloStoreListView
  Scenario Outline: Validate that user is able to select a coupons to choose a store and view the store details upto 20 stores for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
     Then I click on loadmore button and validate the list
  
  @VeloStoreListView_QA         	
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                          | Env | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204          | QA  | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213 | QA  | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106    | QA  | 
      | VELO  | Speedway             | 529181   | https://aem-stage.velo.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284        | QA  | 
  
  @VeloStoreListView_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env  | 
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204            | PROD | 
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213   | PROD | 
      | Velo  | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Velo  | Speedway             | 529181   | https://www.velo.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @VeloStoreListSearchByZip  	
  Scenario Outline: Validate the search By Zip and view the available stores in Store list page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I do a Search By zip with invalid zip code
     Then I validate the error message for invalid zipcode
      And I do a Search By zip with valid zip code <ZipCode>
      And I select a store from store list page
     Then I validate the error message on coupon redemption
  
  @VeloStoreListSearchByZip_QA 
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                          | Env | ZipCode | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204          | QA  | 60404   | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213 | QA  | 60404   | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106    | QA  | 27101   | 
      | VELO  | Speedway             | 529181   | https://aem-stage.velo.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284        | QA  | 60404   | 
  
  @VeloStoreListSearchByZip_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                   | Username               | Password  | Loc                                            | Env  | ZipCode |
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204            | PROD | 60404   |
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213   | PROD | 60404   |
      | Velo  | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 27101   |
      | Velo  | Speedway             | 529181   | https://www.velo.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 60404   |
  
  @VeloStoreDetailsMapView      
  Scenario Outline: Validate the user is able to view the store details through the map view for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I select a store from map view
     Then I validate the user is on Redeem now page
  
  @VeloStoreDetailsMapView_QA
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                          | Env | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204          | QA  | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213 | QA  | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106    | QA  | 
      | VELO  | Speedway             | 529181   | https://aem-stage.velo.com/?RBDSCode=529181 |  <EMAIL>            | Password1 | 401 N Main St, Kernersville, NC 27284        | QA  | 
  
  @VeloStoreDetailsMapView_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env  | 
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204            | PROD | 
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213   | PROD | 
      | Velo  | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Velo  | Speedway             | 529181   | https://www.velo.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @VeloStoreMapViewbyZip
  Scenario Outline: Validate the Search By Zip and view the available stores in Store Map view page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I do a Search By zip with invalid zip code
     Then I validate the error message for invalid zipcode
      And I do a Search By zip with valid zip code <ZipCode>
      And I select a store from map view
      And I validate the error message on coupon redemption
  
  @VeloStoreMapViewbyZip_QA
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                         | Username                         | Password  | Loc                                          | Env | ZipCode | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204          | QA  | 60404   | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213 | QA  | 60404   | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106    | QA  | 27101   | 
      | VELO  | Speedway             | 529181   | https://aem-stage.velo.com/?RBDSCode=529181 | <EMAIL>            | Password1 | 401 N Main St, Kernersville, NC 27284        | QA  | 60404   | 
  
  @VeloStoreMapViewbyZip_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                   | Username               | Password  | Loc                                            | Env  | ZipCode |
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204            | PROD | 60404   |
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213   | PROD | 60404   |
      | Velo  | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 27101   |
      | Velo  | Speedway             | 529181   | https://www.velo.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 60404   |
