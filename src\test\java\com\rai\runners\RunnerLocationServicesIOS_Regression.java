package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/", glue = { "com.rai.steps" }, tags = "@americanspiritIOSLocatonInstruction_QAstage or @americanspiritIOSShieldIcon_QAstage or @CamelIOSLocatonInstruction_QAstage or @CamelIOSShieldIcon_QAstage or @CamelSnusIOSLocatonInstruction_QAstage or @CamelSnusIOSShieldIcon_QAstage or @CougarIOSLocatonInstruction_QAstage or @CougarIOSShieldIcon_QAstage or @GrizzlyIOSLocatonInstruction_QAstage or @GrizzlyIOSShieldIcon_QAstage or @KodaikIOSLocatonInstruction_QAstage or @KodaikIOSShieldIcon_QAstage or @LevigarrettIOSLocatonInstruction_QAstage or @LevigarrettIOSShieldIcon_QAstage or @LuckystrikeIOSLocatonInstruction_QAstage or @LuckystrikeIOSShieldIcon_QAstage or @NewportIOSLocatonInstruction_QAstage or @NewportIOSShieldIcon_QAstage or @PallmallIOSLocatonInstruction_QAstage or @PallmalIOSShieldIcon_QAstage or @VeloIOSLocatonInstruction_QAstage or @VeloIOSShieldIcon_QAstage or @VuseIOSLocatonInstruction_QAstage or @VuseIOSShieldIcon_QAstage",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerLocationServicesIOS_Regression extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
