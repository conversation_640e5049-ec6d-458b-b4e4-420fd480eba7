package com.rai.pages;

import org.openqa.selenium.By;

public class ContentPageObjects {
	
	//Content Page
		public static By imageTick								= By.xpath("//img[@id='frmContentScreen_ContentScreen_imgTick']");
		public static By contentScreenFirstNme 					= By.id("frmContentScreen_ContentScreen_rcTxtFirstName");
		public static By offeravailabilitymessage 				= By.id("frmContentScreen_ContentScreen_lblOfferAvaliabilityDec");
		public static By lnkbacktocoupon						= By.id("frmContentScreen_ContentScreen_lblBckToCoupons");

		public static By imgTile1								= By.xpath("//div[@id='frmContentScreen_ContentScreen_flxTile1']");
		//public static By imgTile1								= By.xpath("//div[@id='frmContentScreen_ContentScreen_imgContentOne']");
		public static By redeemTile1		=	By.xpath("//*[@id='frmContentScreen_ContentScreen_flxTile1']");
		public static By redeemTile2		=	By.xpath("//*[@id='frmContentScreen_ContentScreen_flxTile2']");

		public static By imgTile2								= By.xpath("//input[@id='frmContentScreen_ContentScreen_btnTile2']");	
		public static By warninginfo							= By.xpath("//*[@class=\"kcell sknCSlblWarning\"]");
		public static By closeTobaccoSurveyPopUp 				= By.xpath("//button[contains(@class, 'tobacco')]");
//		public static By naspopuptile1 = By.xpath("//*[@id=\"blue-yellow-modal\"]//*[@class=\"cmp-modal-disruptor__close icon-close]");
		public static By naspopup = By.xpath("//button[@class='cmp-modal-disruptor__close icon-close']");
		public static By murphytilemsg = By.xpath("//div[@id='frmContentScreen_ContentScreen_lblTile2SubText']");
       public static By camelpopclose                       = By.xpath("//button[contains(@class,'cmp-tobacco-preferences-update-reminder__close icon-close')][1]");

       public static By Tile2								= By.xpath("//div[@id='frmContentScreen_ContentScreen_flxTile2']");	
		
}
