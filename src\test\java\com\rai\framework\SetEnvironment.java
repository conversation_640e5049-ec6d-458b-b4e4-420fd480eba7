package com.rai.framework;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.ConcurrentModificationException;
import java.util.Map;
import java.util.stream.Stream;

import org.apache.commons.lang3.SystemUtils;

public class SetEnvironment {

	/**
	 * Sets an environment variable FOR THE CURRENT RUN OF THE JVM
	 * Does not actually modify the system's environment variables,
	 *  but rather only the copy of the variables that java has taken,
	 *  and hence should only be used for testing purposes!
	 * @param key The Name of the variable to set
	 * @param value The value of the variable to set
	 * @throws ClassNotFoundException 
	 * @throws SecurityException 
	 * @throws NoSuchMethodException 
	 * @throws InvocationTargetException 
	 * @throws IllegalArgumentException 
	 * @throws IllegalAccessException 
	 */
	@SuppressWarnings({ "unchecked"})
	public static <K,V> void setenv(final String key, final String value) throws ClassNotFoundException, NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
		try {
			final Class<?> processEnvironmentClass = Class.forName("java.lang.ProcessEnvironment");
			final Field theEnvironmentField = processEnvironmentClass.getDeclaredField("theEnvironment");
			final boolean environmentAccessibility = theEnvironmentField.canAccess(null);
			theEnvironmentField.setAccessible(true);
			final Map<K,V> env = (Map<K, V>) theEnvironmentField.get(null);
			if (SystemUtils.IS_OS_WINDOWS) {
				// This is all that is needed on windows running java jdk 1.8.0_92
				if (value == null) {
					env.remove(key);
				} else {
					env.put((K) key, (V) value);
				}
			} else {
				final Class<K> variableClass = (Class<K>) Class.forName("java.lang.ProcessEnvironment$Variable");
				final Method convertToVariable = variableClass.getMethod("valueOf", String.class);
				final boolean conversionVariableAccessibility = convertToVariable.canAccess(null);
				convertToVariable.setAccessible(true);
				final Class<V> valueClass = (Class<V>) Class.forName("java.lang.ProcessEnvironment$Value");
				final Method convertToValue = valueClass.getMethod("valueOf", String.class);
				final boolean conversionValueAccessibility = convertToValue.canAccess(null);
				convertToValue.setAccessible(true);
				if (value == null) {
					env.remove(convertToVariable.invoke(null, key));
				} else {
					env.put((K) convertToVariable.invoke(null, key), (V) convertToValue.invoke(null, value));
					convertToValue.setAccessible(conversionValueAccessibility);
					convertToVariable.setAccessible(conversionVariableAccessibility);
				}
			}
			theEnvironmentField.setAccessible(environmentAccessibility);
			final Field theCaseInsensitiveEnvironmentField = processEnvironmentClass.getDeclaredField("theCaseInsensitiveEnvironment");
			final boolean insensitiveAccessibility = theCaseInsensitiveEnvironmentField.canAccess(null);
			theCaseInsensitiveEnvironmentField.setAccessible(true);
			final Map<String, String> cienv = (Map<String, String>) theCaseInsensitiveEnvironmentField.get(null);
			if (value == null) {
				cienv.remove(key);
			} else {
				cienv.put(key, value);
			}
			theCaseInsensitiveEnvironmentField.setAccessible(insensitiveAccessibility);
	   
		} catch (final NoSuchFieldException e) {
			final Map<String, String> env = System.getenv();
			Stream.of(Collections.class.getDeclaredClasses())
					.filter(c1 -> "java.util.Collections$UnmodifiableMap".equals(c1.getName()))
					.map(c1 -> {
						try {
							return c1.getDeclaredField("m");
						} catch (final NoSuchFieldException e1) {
							throw new IllegalStateException("Failed setting environment variable <"+key+"> to <"+value+"> when locating in-class memory map of environment", e1);
						}
					})
					.forEach(field -> {
						try {
						final boolean fieldAccessibility = field.canAccess(null);
							field.setAccessible(true);
							// we obtain the environment
							final Map<String, String> map = (Map<String, String>) field.get(env);
							if (value == null) {
								map.remove(key);
							} else {
								map.put(key, value);
							}
							field.setAccessible(fieldAccessibility);
						} catch (final ConcurrentModificationException e1) {
							System.out.printf("Attempted to modify source map: "+field.getDeclaringClass()+"#"+field.getName(), e1);
						} catch (final IllegalAccessException e1) {
							throw new IllegalStateException("Failed setting environment variable <"+key+"> to <"+value+">. Unable to access field!", e1);
						}
					});
		}
		System.out.println("Set environment variable <"+key+"> to <"+value+">. Sanity Check: "+System.getenv(key));
	}
	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	protected static void setEnv(Map<String, String> newenv) throws Exception {
		  try {
			Class<?> processEnvironmentClass = Class.forName("java.lang.ProcessEnvironment");
			Field theEnvironmentField = processEnvironmentClass.getDeclaredField("theEnvironment");
			theEnvironmentField.setAccessible(true);
			Map<String, String> env = (Map<String, String>) theEnvironmentField.get(null);
			env.putAll(newenv);
			Field theCaseInsensitiveEnvironmentField = processEnvironmentClass.getDeclaredField("theCaseInsensitiveEnvironment");
			theCaseInsensitiveEnvironmentField.setAccessible(true);
			Map<String, String> cienv = (Map<String, String>)     theCaseInsensitiveEnvironmentField.get(null);
			cienv.putAll(newenv);
		  } catch (NoSuchFieldException e) {
			Class[] classes = Collections.class.getDeclaredClasses();
			Map<String, String> env = System.getenv();
			for(Class cl : classes) {
			  if("java.util.Collections$UnmodifiableMap".equals(cl.getName())) {
				Field field = cl.getDeclaredField("m");
				field.setAccessible(true);
				Object obj = field.get(env);
				Map<String, String> map = (Map<String, String>) obj;
				map.clear();
				map.putAll(newenv);
			  }
			}
		  }
		}
	
	@SuppressWarnings("unchecked")
	static Map<String, String> getModifiableEnvironment() throws Exception
	{
		Class<?> pe = Class.forName("java.lang.ProcessEnvironment");
		Method getenv = pe.getDeclaredMethod("getenv", String.class);
		getenv.setAccessible(true);
		Field props = pe.getDeclaredField("theCaseInsensitiveEnvironment");
		props.setAccessible(true);
		return (Map<String, String>) props.get(null);
	}
}
