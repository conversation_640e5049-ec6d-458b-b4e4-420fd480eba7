package com.rai.steps;

import java.util.Set;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.By;
import com.rai.framework.DriverManager;
import com.rai.pages.LocationServices;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.remote.SupportsContextSwitching;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;

public class LocationServicesPageStepDefs extends MasterSteps {

	static Logger log = LogManager.getLogger(LocationServicesPageStepDefs.class);
	AppiumDriver driver = DriverManager.getAppiumDriver();

	@Then("^I validate the Location services page$")
	public void i_validate_the_location_services_page() {
		try {
			// DriverManager.getReportiumClient().stepStart("Step:I validate the Location
			// services page");
			String locationServiceMsg = "IN ORDER TO REDEEM COUPONS, YOU NEED TO SELECT “ALLOW” TO GRANT ACCESS TO YOUR CURRENT LOCATION.";

			// isElementPresentVerification(LocationServices.btnUnderstood,"Location
			// Services Page - understood");
			// isElementPresentVerification(LocationServices.imgLocationPopUp,"Location
			// Services Page -Block Pop Up Image");
			isElementPresentVerification(LocationServices.lnkBack, "Location Services Page - Back Link");
			isElementPresentVerification(LocationServices.txtImportant, "Location Services Page - Important Text");
			// isElementPresentVerification(LocationServices.imgLocationPopUp,"Location
			// Services Page -Allow Pop Up Image");
			isElementPresentContainsText(LocationServices.txtLocationServicesMsg, locationServiceMsg,
					"Location Services Page - Message to select Allow button");
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate the location service page: ", e);
		}
	}

	@And("^I Validate Understood screen shield icon$")
	public void I_Validate_Understood_shield_icon() throws Throwable {
		try {

			System.out.println(((SupportsContextSwitching) driver).getContextHandles());
			System.out.println("Shield Icon");

			String Heading1 = "WHY WE NEED YOUR LOCATION";
			String Heading2 = "WHY IT IS SAFE";
			String info1 = "We ask for your location data to bring you the best experience. With your permission, enabling location services enables:";
			String info2 = "Location-specific coupon eligibility";
			String info3 = "Providing participating store locations in your area for offers and promotions";
			String info4 = "Confirming your device is within a 1000 ft radius of the store to successfully redeem coupons.";
			String info5 = "Location Permissions remain enabled only during this session and are not stored once you leave this site.";

			isElementPresentVerification(LocationServices.btnUnderstoodIOS, "btnUnderstood");
			clickIfElementPresent(LocationServices.LocationShieldicon, "Shieldicon");
			// isElementPresentVerification(LocationServices.LocationShieldiconlogo,"shieldlogo");
			isElementPresentContainsText(LocationServices.LocationShieldicontheading1, Heading1, "heading1");
			isElementPresentContainsText(LocationServices.LocationShieldicontheading2, Heading2, "heading2");

			isElementPresentContainsText(LocationServices.LocationShieldiconinfo1, info1, "info1");
			isElementPresentContainsText(LocationServices.LocationShieldiconinfo2, info2, "info2");
			isElementPresentContainsText(LocationServices.LocationShieldiconinfo3, info3, "info3");
			isElementPresentContainsText(LocationServices.LocationShieldiconinfo4, info4, "info4");
			isElementPresentContainsText(LocationServices.LocationShieldiconinfo5, info5, "info5");
			clickIfElementPresent(LocationServices.LocationShieldicondownarrow, "LocationShieldicondownarrow");
			isElementPresentVerification(LocationServices.btnUnderstoodIOS, "btnUnderstood");

		}

		catch (Exception e) {

			failTestScript("Click on the understood button failed", e);
		}
	}

	@And("^I click on the Understood button$")
	public void i_click_on_the_understood_button() {
		try {
			Thread.sleep(10000);
			// DriverManager.getReportiumClient().stepStart("Step:I validate the Location
			// services page");
			setDeviceLocation(location.get());
			// if(brand.get().contains("Vuse"))
			// {
			// waitUntilElementVisible(LocationServices.mobileCouponsVuse, 30);
			// clickIfElementPresent(LocationServices.mobileCouponsVuse, "Location Services
			// Page - Understood button");
			// waitUntilElementVisible(LocationServices.redeemVuse, 30);
			// clickIfElementPresent(LocationServices.redeemVuse, "Location Services Page -
			// Understood button");
			// }
			Thread.sleep(2000);
			waitUntilPageReadyStateComplete(60);
			Thread.sleep(3000);
			System.out.println("wait for 10 sec");
			// if (isElementPresent(By.id("com.android.chrome:id/positive_button")))
			// clickIfElementPresent(LocationServices.btnAllow, "Allow Device Location
			// Setting Button");
			Thread.sleep(10000);
			if (brand.get().contains("Grizzly")) {
				clickIfElementPresent(LocationServices.btnUnderstoodGrizzly,
						"Location Services Page - Understood button");
			} else {
				waitUntilElementVisible(LocationServices.btnUnderstood, 100);
				clickIfElementPresent(LocationServices.btnUnderstood, "Location Services Page - Understood button");
			}
			/*
			 * JavascriptExecutor js = (JavascriptExecutor) driver; js.executeScript(
			 * "document.querySelector('#frmLocInfoScreen_LocInfoScreen_btnContinue').click()"
			 * );
			 */
			Thread.sleep(10000);
			// if (driver instanceof AndroidDriver) {
			// switchToContext(driver, "NATIVE_APP");
			// clickIfElementPresent(LocationServices.androidLocationServicesPermissionAllow,
			// "Location services Permission - Allow Button");
			// Thread.sleep(20000);
			/*
			 * if(isElementPresent(LocationServices.alertAllowwhileusingappButton)) {
			 * switchToContext(driver, "NATIVE_APP");
			 * clickIfElementPresent(LocationServices.
			 * alertAllowwhileusingappButton,"Location services Permission - Allow Button");
			 * 
			 * switchToContext(driver, "CHROMIUM"); }
			 */
			// switchToContext(driver, "CHROMIUM");
			// DriverManager.getReportiumClient().stepEnd();
			// } else if (driver instanceof IOSDriver) {

			// clickusingTextOnScreen("COUPONS");
			// Thread.sleep(2000);

			/*
			 * switchToContext(driver, "NATIVE_APP");
			 * System.out.println(DriverManager.getAppiumDriver().getContext());
			 * waitUntilElementVisible(LocationServices.iOSLocationServicesPermissionAllow,
			 * 60); clickIfElementPresent(LocationServices.
			 * iOSLocationServicesPermissionAllow,"Location services Permission - Allow Button"
			 * ); switchToContext(driver, "WEBVIEW");
			 */
			// DriverManager.getReportiumClient().stepEnd();
			// }
			Thread.sleep(10000);

			System.out.println(tagname.get());
			// if(!tagname.get().contains("Restrictions"))
			// waitUntilElementVisible(CouponHomePageObjects.brandImgCouponHomeHeader, 30);
		} catch (Exception e) {
			failTestScript("Failed to clickon understand button: ", e);
		}
	}

	@And("^I toggle the device location service$")
	public void i_toggle_the_device_location_service() {
		((AndroidDriver) driver).toggleLocationServices();
	}

	@And("^I Validate Location Disabled screen and click continue$")
	public void I_Validate_Location_Disabled_screen() throws Throwable {
		try {

			System.out.println(((SupportsContextSwitching) driver).getContextHandles());
			System.out.println("screen0");
			Set<String> contextHandles = ((SupportsContextSwitching) driver).getContextHandles();
			String webviewContext = null;

			for (String contextHandle : contextHandles) {
				if (contextHandle.contains("WEBVIEW")) {
					webviewContext = contextHandle;
					break;
				}
			}

			if (webviewContext != null) {
				((SupportsContextSwitching) driver).context(webviewContext);
				String Locationmessge1 = "LOCATION SERVICES MUST BE ENABLED TO REDEEM MOBILE COUPONS.";
				String Locationmessge2 = "After the Location Services is enabled, select the button below.";

				isElementPresentVerification(LocationServices.LocationDisabledtitle, "Location Disabled");
				isElementPresentContainsText(LocationServices.LocationDisabledmessage1, Locationmessge1, "message1");
				isElementPresentVerification(LocationServices.LocationDisabledloactionsymbol, "symbol");
				isElementPresentVerification(LocationServices.LocationDisabledlearnhow, "Learnhow");
				isElementPresentContainsText(LocationServices.LocationDisabledmessage2, Locationmessge2, "message1");
				clickIfElementPresent(LocationServices.Locationcontinuebutton, "continuebutton");

			}
		}

		// waitUntilElementVisible(HomePageObjects.spa_Welcome,500);
		catch (Exception e) {

			failTestScript("Click on the understood button failed", e);
		}
	}

	@And("^I Validate Location Disabled screen2$")
	public void I_Validate_Location_Disabled_screen2() throws Throwable {
		try {

			System.out.println(((SupportsContextSwitching) driver).getContextHandles());
			System.out.println("screen2");
			isElementPresentVerification(LocationServices.LocationDisabledscreen2message1,
					"Location Disabled screen2 message");
			isElementPresentVerification(LocationServices.LocationDisabledscreen2tutotrialimage, "symbol");

			isElementPresentVerification(LocationServices.LocationDisabledscreen2tutotrialDes0, "Learnhow");

			isElementPresentVerification(LocationServices.LocationDisabledscreen2tutotrialDes1, "Learnhow");
			isElementPresentVerification(LocationServices.LocationDisabledscreen2tutotrialDes2, "Learnhow");
			isElementPresentVerification(LocationServices.LocationDisabledscreen2tutotrialDes3, "Learnhow");
			isElementPresentVerification(LocationServices.LocationDisabledscreen2message2, "message2");

			Thread.sleep(1000);
			clickIfElementPresent(LocationServices.LocationDisabledscreen2continue, "continuebutton");
			Thread.sleep(1000);

		}
		// waitUntilElementVisible(HomePageObjects.spa_Welcome,500);
		catch (Exception e) {

			failTestScript("Click on the understood button failed", e);
		}
	}

	@And("^I Validate Location Disabled screen3$")
	public void I_Validate_Location_Disabled_screen3() throws Throwable {
		try {

			System.out.println(((SupportsContextSwitching) driver).getContextHandles());
			System.out.println("screen3");
			isElementPresentVerification(LocationServices.LocationDisabledscreen3message1,
					"Location Disabled screen2 message");
			isElementPresentVerification(LocationServices.LocationDisabledscreen3tutotrialimage, "symbol");
			isElementPresentVerification(LocationServices.LocationDisabledscreen3tutotrialDes0,
					"LocationDisabledscreen3tutotrialDes0");
			isElementPresentVerification(LocationServices.LocationDisabledscreen3tutotrialDes1,
					"LocationDisabledscreen3tutotrialDes1");
			isElementPresentVerification(LocationServices.LocationDisabledscreen3tutotrialDes2,
					"LocationDisabledscreen3tutotrialDes2");
			isElementPresentVerification(LocationServices.LocationDisabledscreen3tutotrialDes3,
					"LocationDisabledscreen3tutotrialDes3");
			isElementPresentVerification(LocationServices.LocationDisabledscreen3tutotrialDes4,
					"LocationDisabledscreen3tutotrialDes4");
			isElementPresentVerification(LocationServices.LocationDisabledscreen3tutotrialDes5,
					"LocationDisabledscreen3tutotrialDes5");

			isElementPresentVerification(LocationServices.LocationDisabledscreen3message2, "message2");

			Thread.sleep(2000);
			clickIfElementPresent(LocationServices.LocationDisabledscreen3continue, "continuebutton");
			Thread.sleep(2000);

		}

		// waitUntilElementVisible(HomePageObjects.spa_Welcome,500);
		catch (Exception e) {

			failTestScript("Click on the understood button failed", e);
		}
	}

	@And("^I Validate Location Disabled screen4$")
	public void I_Validate_Location_Disabled_screen4() throws Throwable {
		try {

			System.out.println(((SupportsContextSwitching) driver).getContextHandles());
			System.out.println("screen4");
			isElementPresentVerification(LocationServices.LocationDisabledscreen4message1,
					"Location Disabled screen2 message");
			isElementPresentVerification(LocationServices.LocationDisabledscreen4tutotrialimage, "symbol");
			isElementPresentVerification(LocationServices.LocationDisabledscreen4tutotrialDes0,
					"LocationDisabledscreen4tutotrialDes0");
			isElementPresentVerification(LocationServices.LocationDisabledscreen4tutotrialDes1,
					"LocationDisabledscreen4tutotrialDes1");
			isElementPresentVerification(LocationServices.LocationDisabledscreen4tutotrialDes2,
					"LocationDisabledscreen4tutotrialDes2");
			isElementPresentVerification(LocationServices.LocationDisabledscreen4tutotrialDes3,
					"LocationDisabledscreen4tutotrialDes3");

			isElementPresentVerification(LocationServices.LocationDisabledscreen4message2, "message2");

			Thread.sleep(2000);
			clickIfElementPresent(LocationServices.LocationDisabledscreen4continue, "continuebutton");
			Thread.sleep(2000);

		}

		// waitUntilElementVisible(HomePageObjects.spa_Welcome,500);
		catch (Exception e) {

			failTestScript("Click on the understood button failed", e);
		}
	}

	@And("^I Validate Location Disabled screen5$")
	public void I_Validate_Location_Disabled_screen5() throws Throwable {
		try {

			System.out.println(((SupportsContextSwitching) driver).getContextHandles());
			System.out.println("screen5");
			isElementPresentVerification(LocationServices.LocationDisabledscreen5message1,
					"Location Disabled screen2 message");
			isElementPresentVerification(LocationServices.LocationDisabledscreen5tutotrialimage, "symbol");
			isElementPresentVerification(LocationServices.LocationDisabledscreen5tutotrialDes0,
					"LocationDisabledscreen4tutotrialDes0");
			isElementPresentVerification(LocationServices.LocationDisabledscreen5tutotrialDes1,
					"LocationDisabledscreen4tutotrialDes1");
			isElementPresentVerification(LocationServices.LocationDisabledscreen5tutotrialDes2,
					"LocationDisabledscreen4tutotrialDes2");
			isElementPresentVerification(LocationServices.LocationDisabledscreen5tutotrialDes3,
					"LocationDisabledscreen4tutotrialDes3");
			isElementPresentVerification(LocationServices.LocationDisabledscreen5tutotrialDes4,
					"LocationDisabledscreen4tutotrialDes1");
			isElementPresentVerification(LocationServices.LocationDisabledscreen5tutotrialDes5,
					"LocationDisabledscreen4tutotrialDes2");
			isElementPresentVerification(LocationServices.LocationDisabledscreen5tutotrialDes6,
					"LocationDisabledscreen4tutotrialDes3");

			isElementPresentVerification(LocationServices.LocationDisabledscreen5message2, "message2");

			Thread.sleep(2000);
			clickIfElementPresent(LocationServices.LocationDisabledscreen5continue, "continuebutton");
			Thread.sleep(2000);
		}

		catch (Exception e) {

			failTestScript("Click on the understood button failed", e);
		}
	}

	@And("^I click on the Understood button location service$")
	public void I_click_on_the_Understood_button_location_service() throws InterruptedException {

		try {

			waitUntilElementVisible(LocationServices.btnUnderstood, 100);
			clickIfElementPresent(LocationServices.btnUnderstood, "Location Services Page - Understood button");
			Thread.sleep(6000);

			blockPopUp();

		} catch (Exception e) {
			failTestScript("Click on the BLOCK button failed", e);
		}

	}

	@And("I click on the Learn how")
	public void i_click_on_the_Learn_how() throws InterruptedException {

		try {

			// waitUntilElementVisible(LocationServices.btnUnderstood, 100);
			// clickIfElementPresent(LocationServices.btnUnderstood, "Location Services Page
			// - Understood button");
			// Thread.sleep(6000);
			//
			//
			//
			// blockPopUp();
			{

				Thread.sleep(5000);
				waitUntilElementVisible(LocationServices.btncontinue, 100);
				clickIfElementPresent(LocationServices.btncontinue, "Location Services Page - Understood button");
			}
		}

		catch (Exception e) {

			failTestScript("Click on the continue button failed", e);
		}

	}

	@And("I click on the continue button")
	public void i_click_on_the_continue_button() throws InterruptedException {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.bttncontinue, 100);
			clickIfElementPresent(LocationServices.bttncontinue, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@And("I Navigate to Chrome App Loc")
	public void i_Navigate_to_Chrome_App_Loc() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btncontinue1, 100);
			clickIfElementPresent(LocationServices.btncontinue1, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@And("I Navigate to Website Loc")
	public void i_Navigate_to_Website_Loc() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btncontinue2, 100);
			clickIfElementPresent(LocationServices.btncontinue2, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@And("I Naviagte to Clearing cache")
	public void i_Naviagte_to_Clearing_cache() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btncontinue3, 100);
			clickIfElementPresent(LocationServices.btncontinue3, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@Then("I click the back to the camel page")
	public void i_click_the_back_to_the_camel_page() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btnbrand, 100);
			clickIfElementPresent(LocationServices.btnbrand, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@Then("I click the back to the cougar page")
	public void i_click_the_back_to_the_cougar_page() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btnbrand, 100);
			clickIfElementPresent(LocationServices.btnbrand, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@Then("I click the back to the Grizzly page")
	public void i_click_the_back_to_the_Grizzly_page() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btnbrand, 100);
			clickIfElementPresent(LocationServices.btnbrand, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@Then("I click the back to the Kodaik page")
	public void i_click_the_back_to_the_Kodaik_page() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btnbrand, 100);
			clickIfElementPresent(LocationServices.btnbrand, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@Then("I click the back to the Levigarrett page")
	public void i_click_the_back_to_the_Levigarrett_page() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btnbrand, 100);
			clickIfElementPresent(LocationServices.btnbrand, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@Then("I click the back to the camelsnus page")
	public void i_click_the_back_to_the_camelsnus_page() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btnbrand, 100);
			clickIfElementPresent(LocationServices.btnbrand, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@Then("I click the back to the Luckystrike page")
	public void i_click_the_back_to_the_Luckystrike_page() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btnbrand, 100);
			clickIfElementPresent(LocationServices.btnbrand, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@Then("I click the back to the Newport page")
	public void i_click_the_back_to_the_Newport_page() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btnbrand, 100);
			clickIfElementPresent(LocationServices.btnbrand, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@Then("I click the back to the Pallmall page")
	public void i_click_the_back_to_the_Pallmall_page() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btnbrand, 100);
			clickIfElementPresent(LocationServices.btnbrand, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@Then("I click the back to the velo page")
	public void i_click_the_back_to_the_velo_page() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btnbrand, 100);
			clickIfElementPresent(LocationServices.btnbrand, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@Then("I click the back to the vuse page")
	public void i_click_the_back_to_the_vuse_page() {

		try {
			Thread.sleep(10000);
			waitUntilElementVisible(LocationServices.btnbrand, 100);
			clickIfElementPresent(LocationServices.btnbrand, "Location Services Page - Understood button");
		} catch (Exception e) {
			failTestScript("Click on the continue button failed", e);
		}

	}

	@And("^I Validate Location Disabled screen6tryagain & Back to AEMscreen functionality$")
	public void I_Validate_Location_Disabled_screen6tryagain_functionality() throws Throwable {
		try {

			System.out.println(((SupportsContextSwitching) driver).getContextHandles());
			System.out.println("tryagain functionality");

			isElementPresentVerification(LocationServices.LocationDisabledscreen6tryagain, "tryagain");
			// WebElement
			// e=driver.findElement(By.xpath("//*[@id='frmLocDisabledContact_flxTryAgainButton']"));
			driver.findElement(By.xpath("//*[@id=\"frmLocDisabledContact_btnTryAgain\"]")).click();

			// clickIfElementPresent(LocationServices.LocationDisabledscreen6tryagain,
			// "tryagain");
			// clickIfElementPresent(LocationServices.LocationDisabledscreen6tryagain,
			// "tryagain");
			//
			Thread.sleep(3000);
			waitUntilElementVisible(LocationServices.LocationDisabledunderstood, 30);
			isElementPresentVerification(LocationServices.LocationDisabledunderstood, "Location screen");
			// clickIfElementPresent(LocationServices.LocationDisabledunderstood,
			// "understood");
			// clickIfElementPresent(LocationServices.Locationcontinuebutton, "tryagain");
			//
			//
			// clickIfElementPresent(LocationServices.LocationDisabledscreen2continue,
			// "tryagain");
			// clickIfElementPresent(LocationServices.LocationDisabledscreen3continue,
			// "tryagain");
			// clickIfElementPresent(LocationServices.LocationDisabledscreen4continue,
			// "tryagain");
			// clickIfElementPresent(LocationServices.LocationDisabledscreen5continue,
			// "tryagain");
			// System.out.println((DriverManager.getAppiumDriver()).getContext()+" <-app
			// context");
			// Thread.sleep(2000);
			// WebElement e1=
			// driver.findElement(By.cssSelector("#frmLocDisabledContact_btnBack"));
			// System.out.println(e1.getText());
			// // System.out.println(e1.getAttribute("id"));
			// e1.click();
			// Actions a=new Actions(driver);
			// a.moveToElement(e1).click().perform();
			// Thread.sleep(2000);

			// isElementPresentVerification(HomePageObjects.frmLocDisabledContact_btnBack,
			// "Back Button");

			// driver.findElement(By.xpath("//input[@id='frmLocDisabledContact_btnBack']")).click();
			// driver.findElement(By.xpath("//input[@id='frmLocDisabledContact_btnBack']")).click();
			// Thread.sleep(2000);
			// System.out.println("clicked");
			// isElementPresentVerification(HomePageObjects.hamburgerMenu,"VAlidate aem
			// screen displayed or not");
			////

		}

		catch (Exception e) {

			failTestScript("Click on the understood button failed", e);
		}
	}

	@SuppressWarnings("rawtypes")
	@And("^I click on the Understood button for IOS$")
	public void i_click_on_the_understood_button_IOS() throws Throwable {
		try {
			Thread.sleep(2000);
			waitUntilPageReadyStateComplete(60);
			Thread.sleep(2000);
			// driver.navigate().to("");
			waitUntilElementVisible(LocationServices.btnUnderstoodIOS, 50);
			clickIfElementPresent(LocationServices.btnUnderstoodIOS, "Location Services Page - Understood button");
			Thread.sleep(6000);
			((IOSDriver) DriverManager.getAppiumDriver()).getContextHandles();
			if (driver instanceof IOSDriver) {
				switchToContext(driver, "NATIVE_APP");
				if (isElementPresent(LocationServices.iOSLocationServicesPermissiondontAllow)) {

					clickIfElementPresent(LocationServices.iOSLocationServicesPermissiondontAllow,
							"Location services Permission - Allow Button");

				}

			}

			// waitUntilElementVisible(HomePageObjects.spa_Welcome,500);
		} catch (Exception e) {

			failTestScript("Click on the understood button failed", e);
		}
	}

}
