package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

//@CucumberOptions(features="src/test/resources/features/", glue= { "com.rai.steps" }, tags={"@LevigarrettfavouriteStore_QA"}, monochrome = true, plugin=

@CucumberOptions(features="src/test/resources/features/", glue= { "com.rai.steps" }, tags="@LevigarrettValidateHamburgerMenu_Prod or "
		+ "@LevigarrettLogoutfromHamburgerMenu_Prod or @NavigateLevigarrettMobilesiteHamburgerMenu_Prod or  @LevigarrettStoreListView_Prod or "
		+ "@LevigarrettStoreListSearchByZip_Prod or @LevigarrettStoreDetailsMapView_Prod or @LevigarrettStoreMapViewbyZip_Prod", monochrome = true, plugin=
{ "pretty", "pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html", "json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerLevigarrett_Prod extends AbstractTestNGCucumberTests {
	
	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */
	
	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
	

}
