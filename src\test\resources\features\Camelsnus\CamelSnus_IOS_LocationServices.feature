@CamelSnusIOSLocatonInstruction
Feature: CamelSnus SPA -Location Screen
As a RJR user, I Validate the Camel Location setting instruction screen

@CamelSnusIOSLocatonInstruction
  Scenario Outline: Validate the Camel Location setting instruction screen
     
   
    Given I launch Setting and select Ask first option in Location
      And I'm on the login page IOS for <Brand> with login <URL>
     When I login with valid user id <Username> and password <Password> for <Brand>
       And I navigate to CamelSnus Offers Page
      And I click on redeem now button
      #And I set the valid device location to <Loc>
      And I click on the Understood button for IOS
      And I Validate Location Disabled screen and click continue
      And I Validate Location Disabled screen2
       And I Validate Location Disabled screen3
       And I Validate Location Disabled screen4
       And I Validate Location Disabled screen5
       And I Validate Location Disabled screen6tryagain & Back to AEMscreen functionality
@CamelSnusIOSLocatonInstruction_QAstage        	
Examples: 
      | Brand  | URL                                          | Username                 | Password  | Loc                                   |
      | CAMELSNUS  | https://aem-stage.camelsnus.com/		              | <EMAIL>       | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 

@CamelSnusIOSLocatonInstruction_Prod
Examples: 
      | Brand  | URL                            | Username                                 | Password  | Loc                                   |
      | CAMELSNUS  |  https://www.camelsnus.com 	 		      | <EMAIL>   | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 
 
 
 @CamelSnusIOSShieldIcon
  Scenario Outline: Validate the Camelsnus Location setting instruction screen
  
     Given I'm on the login page for <Brand> with login <URL> 
     When I login with valid user id <Username> and password <Password> for <Brand>
      And I navigate to CamelSnus Offers Page
      And I click on redeem now button
      #And I set the valid device location to <Loc>
      #And I click on the Understood button for IOS
      And I Validate Understood screen shield icon
      
      
@CamelSnusIOSShieldIcon_QAstage        	
Examples: 
      | Brand  | URL                                          | Username                 | Password  | Loc                                   |
      | CAMELSNUS  |  https://aem-stage.camelsnus.com/			   | <EMAIL>       | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 

@CamelSnusIOSShieldIcon_Prod
Examples: 
      | Brand  | URL                            | Username                                 | Password  | Loc                                   |
      | CAMELSNUS  | https://www.camelsnus.com 	      | <EMAIL>   | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 
 