@LevigarrettHamburgerMenu
Feature: Levi<PERSON>rett RBDS - Hamburger Menu
As a RJR user, I shall be validating Levigarrett Coupon Hamburger Functionality

@LevigarrettValidateHamburgerMenu
  Scenario Outline: Validate Hamburger Menu And the navigation to Levigarrett Coupon Page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I validate the elements in Hamburger Menu
      And I click on coupons link
     Then I validate I'm on the Coupons Home Page  
  
  @LevigarrettValidateHamburgerMenu_QA
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                   | Username                         | Password  | Loc                                            | Env | 
      | Levigarrett | 7-Eleven Corporation | 572896   | https://aem-stage.levigarrett.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Levigarrett | Murphy               | 741592   | https://aem-stage.levigarrett.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Levigarrett | Sheetz               | 595324   | https://aem-stage.levigarrett.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Levigarrett | Speedway             | 529181   | https://aem-stage.levigarrett.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @LevigarrettValidateHamburgerMenu_Prod
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                             | Username                | Password  | Loc                                            | Env  | 
      | Levigarrett | 7-Eleven Corporation | 572896   | https://www.levigarrett.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Levigarrett | Murphy               | 741592   | https://www.levigarrett.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Levigarrett | Sheetz               | 595324   | https://www.levigarrett.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Levigarrett | Speedway             | 529181   | https://www.levigarrett.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @LevigarrettLogoutfromHamburgerMenu
  Scenario Outline: Validate that user can logout from the Levigarrett Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I click on logout link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @LevigarrettLogoutfromHamburgerMenu_QA    
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                   | Username                         | Password  | Loc                                            | Env | 
      | Levigarrett | 7-Eleven Corporation | 572896   | https://aem-stage.levigarrett.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Levigarrett | Murphy               | 741592   | https://aem-stage.levigarrett.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Levigarrett | Sheetz               | 595324   | https://aem-stage.levigarrett.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Levigarrett | Speedway             | 529181   | https://aem-stage.levigarrett.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @LevigarrettLogoutfromHamburgerMenu_Prod
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                             | Username                | Password  | Loc                                            | Env  | 
      | Levigarrett | 7-Eleven Corporation | 572896   | https://www.levigarrett.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Levigarrett | Murphy               | 741592   | https://www.levigarrett.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Levigarrett | Sheetz               | 595324   | https://www.levigarrett.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Levigarrett | Speedway             | 529181   | https://www.levigarrett.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @NavigateLevigarrettMobilesiteHamburgerMenu 
  Scenario Outline: Validate that user can navigate to Levigarrett brand mobile site from the Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
      And I click on the Understood button
      And I navigate to Hamburger menu
      And I click on brand link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @NavigateLevigarrettMobilesiteHamburgerMenu_QA      
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                   | Username                         | Password  | Loc                                            | Env | 
      | Levigarrett | 7-Eleven Corporation | 572896   | https://aem-stage.levigarrett.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Levigarrett | Murphy               | 741592   | https://aem-stage.levigarrett.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Levigarrett | Sheetz               | 595324   | https://aem-stage.levigarrett.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Levigarrett | Speedway             | 529181   | https://aem-stage.levigarrett.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @NavigateLevigarrettMobilesiteHamburgerMenu_Prod
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                             | Username                | Password  | Loc                                            | Env  | 
      | Levigarrett | 7-Eleven Corporation | 572896   | https://www.levigarrett.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Levigarrett | Murphy               | 741592   | https://www.levigarrett.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Levigarrett | Sheetz               | 595324   | https://www.levigarrett.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Levigarrett | Speedway             | 529181   | https://www.levigarrett.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  
