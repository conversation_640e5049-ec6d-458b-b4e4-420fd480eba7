Feature: <PERSON><PERSON><PERSON>t RBDS -Android Location Services
As a RJR user, I shall be validating Levigarrett Location Services

@LevigarrettValidateLocationServices
  Scenario Outline: Validate Location Services And the navigation to Levigarrett Coupon Page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
    #  And I click on the Understood button
     And I click on the Understood button location service
        And I click on the Learn how 
    And I click on the continue button
    And I Navigate to Chrome App Loc
    And I Navigate to Website Loc
    And I Naviagte to Clearing cache
    Then I click the back to the Levigarrett page
      
      
      
      
      
      
      
      
       @LevigarrettValidateLocation_QA
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                   | Username                         | Password  | Loc                                            | Env | 
      | Levigarrett | 7-Eleven Corporation | 572896   | https://aem-stage.levigarrett.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
  