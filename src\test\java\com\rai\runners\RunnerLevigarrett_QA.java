package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

//@CucumberOptions(features="src/test/resources/features/", glue= { "com.rai.steps" }, tags={"@CamelCouponHome_QAstage"}, monochrome = true, plugin=
@CucumberOptions(features="src/test/resources/features/", glue= { "com.rai.steps" }, tags="@LevigarrettValidateHamburgerMenu_QA or "
		+ "@LevigarrettLogoutfromHamburgerMenu_QA or @NavigateLevigarrettMobilesiteHamburgerMenu_QA or  @LevigarrettStoreListView_QA or "
		+ "@LevigarrettStoreListSearchByZip_QA or @LevigarrettStoreMapViewbyZip_QA or @LevigarrettValidateLocation_QA or @Levigarrett_CopyrightTextinLoginPage_QA", monochrome = true, plugin=
{ "pretty", "pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html", "json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

//LevigarrettValidateHamburgerMenu_QA,LevigarrettLogoutfromHamburgerMenu_QA,NavigateLevigarrettMobilesiteHamburgerMenu_QA,LevigarrettStoreListView_QA
//LevigarrettStoreListSearchByZip_QA,LevigarrettStoreMapViewbyZip_QA,LevigarrettValidateLocation_QA,Levigarrett_CopyrightTextinLoginPage_QA
public class RunnerLevigarrett_QA extends AbstractTestNGCucumberTests {
	
	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */
	
	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
	

}
