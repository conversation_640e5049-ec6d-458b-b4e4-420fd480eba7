Feature: Newport SPA - Hamburger Menu
As a RJR user, I shall be validating Newport Coupon Hamburger Functionality

@NewportValidateHamburgerMenu
  Scenario Outline: Validate Hamburger Menu And the navigation to Newport Coupon Page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I validate the elements in Hamburger Menu
      And I click on coupons link
     Then I validate I'm on the Coupons Home Page  
  
  @NewportValidateHamburgerMenu_QA
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                     | Username                         | Password  | Loc                                            | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Newport | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @NewportValidateHamburgerMenu_Prod
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                               | Username                         | Password  | Loc                                            | Env  | 
      | Newport | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Newport | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Newport | Sheetz               | 595324   | https://www.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Newport | Speedway             | 529181   | https://www.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @NewportLogoutfromHamburgerMenu
  Scenario Outline: Validate that user can logout from the Newport Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I click on logout link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @NewportLogoutfromHamburgerMenu_QA    
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                     | Username                         | Password  | Loc                                            | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Newport | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @NewportLogoutfromHamburgerMenu_Prod
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                               | Username                         | Password  | Loc                                            | Env  | 
      | Newport | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Newport | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Newport | Sheetz               | 595324   | https://www.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Newport | Speedway             | 529181   | https://www.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @NavigateNewportMobilesiteHamburgerMenu 
  Scenario Outline: Validate that user can navigate to Newport brand mobile site from the Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
      And I click on the Understood button
      And I navigate to Hamburger menu
      And I click on brand link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @NavigateNewportMobilesiteHamburgerMenu_QA      
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                     | Username                         | Password  | Loc                                            | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Newport | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @NavigateNewportMobilesiteHamburgerMenu_Prod
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                               | Username                         | Password  | Loc                                            | Env  | 
      | Newport | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Newport | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Newport | Sheetz               | 595324   | https://www.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Newport | Speedway             | 529181   | https://www.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  