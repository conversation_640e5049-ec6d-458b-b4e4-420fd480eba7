package com.rai.framework;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.Authenticator;
import java.net.PasswordAuthentication;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import com.google.cloud.vision.v1.AnnotateImageRequest;
import com.google.cloud.vision.v1.AnnotateImageResponse;
import com.google.cloud.vision.v1.BatchAnnotateImagesResponse;
import com.google.cloud.vision.v1.EntityAnnotation;
import com.google.cloud.vision.v1.Feature;
import com.google.cloud.vision.v1.Feature.Type;
import com.google.cloud.vision.v1.Image;
import com.google.cloud.vision.v1.ImageAnnotatorClient;
import com.google.protobuf.ByteString;

public class DetectTextGoogleAPI {

	public static void main(String args[]) throws IOException, Exception {
		detectText(System.getProperty("user.dir")
				+ "\\test-output\\screenshots\\0bfd19c8-111f-41eb-a8b2-36ca7ee1efdd\\0bfd19c8-111f-41eb-a8b2-36ca7ee1efdd.png");
	}

	public static String detectText(String filePath)
			throws FileNotFoundException, IOException, ClassNotFoundException, NoSuchMethodException, SecurityException,
			IllegalAccessException, IllegalArgumentException, InvocationTargetException {
		// setProxyAndEnvironement();
		System.out.println("Filepath: " + filePath);
		SetEnvironment.setenv("GOOGLE_APPLICATION_CREDENTIALS", System.getProperty("user.dir")
				+ "/src/test/resources/GoogleKey/lively-machine-273003-26dcc2797534.json");
		List<AnnotateImageRequest> requests = new ArrayList<AnnotateImageRequest>();
		String resultText = null;
		ByteString imgBytes = ByteString.readFrom(new FileInputStream(filePath));
		Image img = Image.newBuilder().setContent(imgBytes).build();
		Feature feat = Feature.newBuilder().setType(Type.TEXT_DETECTION).build();
		AnnotateImageRequest request = AnnotateImageRequest.newBuilder().addFeatures(feat).setImage(img).build();
		requests.add(request);
		try (ImageAnnotatorClient client = ImageAnnotatorClient.create()) {
			BatchAnnotateImagesResponse response = client.batchAnnotateImages(requests);
			List<AnnotateImageResponse> responses = response.getResponsesList();
			for (AnnotateImageResponse res : responses) {
				if (res.hasError()) {
					System.out.printf("Error: %s\n", res.getError().getMessage());
					return res.getError().getMessage();
				}
				List<EntityAnnotation> text = res.getTextAnnotationsList();
				System.out.println("Detected Text: " + text.get(0).getDescription());
				resultText = text.get(0).getDescription();
			}
		}
		return resultText;
	}

	public static void setProxyAndEnvironement() {
		Properties mobileProperties = Settings.getInstance();
		String proxyHost = mobileProperties.getProperty("ProxyHost");
		String proxyPort = mobileProperties.getProperty("ProxyPort");
		final String username = mobileProperties.getProperty("ProxyUsername");
		final String password = mobileProperties.getProperty("ProxyPassword");
		try {
			SetEnvironment.setenv("GOOGLE_APPLICATION_CREDENTIALS", System.getProperty("user.dir")
					+ "/src/test/resources/GoogleKey/lively-machine-273003-26dcc2797534.json");
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.setProperty("https.proxyHost", proxyHost);
		System.setProperty("https.proxyPort", proxyPort);
		Authenticator.setDefault(new Authenticator() {
			@Override
			public PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(username, password.toCharArray());
			}
		});
	}
}
