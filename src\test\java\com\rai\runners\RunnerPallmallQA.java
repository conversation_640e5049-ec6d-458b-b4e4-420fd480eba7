package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Pallmall", glue = { "com.rai.steps" }, tags = "@PallmallValidateContentPostRedemption_QA or "
		+ "@PallmallCouponHome_QAstage or @PallmallCouponSort_QAStage or  @PallmallCouponvalidateRestrictions_QAStage or "
		+ "@PallmallRedeemAtAny0.1MileStore_QA or @PallmallValidateRedeemNotNow_QA or @PallmallValidatefavouriteStore_QA or @PallmallValidateErrorMessageNotnearbyStore_QA or @PallmallMapViewCouponRedemtion_QA or "
		+ "@PallmallValidateHamburgerMenu_QA or @PallmallLogoutfromHamburgerMenu_QA or @NavigatePallmallMobilesiteHamburgerMenu_QA or "
		+ "@PallmallSGWValidations_QA or "
		+ "@PallmallStoreListView_QA or @PallmallStoreListSearchByZip_QA or @PallmallStoreDetailsMapView_QA or @PallmallStoreMapViewbyZip_QA", monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

// VeloValidateErrorMessageNotnearbyStore_QA
public class RunnerPallmallQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
