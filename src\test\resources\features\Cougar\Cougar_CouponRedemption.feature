Feature: Cougar RBDS- Coupon Redemption at different retailers using the RBDS url
As an ATC, I should be able to successfully use the RBDS url for a particular retailer and Redeem a coupon
    
@CougarRedeemAtAny0.1MileStore
  Scenario Outline: Validate the user is able to redeem the coupon from RBDS site when he is at a distance of .1 miles for the retailer <Retailer> with RBDS code <RBDScode> 
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
      When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
      And I select a coupon and click on choose a store button
      And I select a store from store list page
      And I click on the Redeem Now button
     Then I validate the timer running
      And I click on the I'm Done button

@CougarRedeemAtAny0.1MileStore_QA  
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   						|Env	|
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA			   					| QA	|
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>			| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				|	QA	|

@CougarValidateRedeemNotNow
	Scenario Outline: Validate that no coupon is redeemed when the user clicks on the Not Now button on the redemption page for the retailer <Retailer> with RBDS code <RBDScode>
			Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
     	  And I set the valid device location to <Loc>
     	 When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      	And I click on the Understood button
      	And I validate I'm on the Coupons Home Page
      	And I select a coupon and click on choose a store button
      	And I select a store from store list page
      	And I click on the Not Now button
 		   Then I validate I'm on the Coupons Home Page

@CougarValidateRedeemNotNow_QA 		    
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   						|Env	|
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA		   					| QA	|
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>				| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				|	QA	|
 
@CougarValidateRedeemNotNow_Prod
      Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   						|Env	 |
      | Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA			   					| Prod |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				| Prod |
 
@CougarValidatefavouriteStore
  Scenario Outline: Validate the favorite store selection in Redeem Now page and its update in Store list page  for the retailer <Retailer> with RBDS code <RBDScode>   
      Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>     
     	  And I set the valid device location to <Loc>
     	  When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      	And I click on the Understood button
       Then I validate I'm on the Coupons Home Page
      	And I select a coupon and click on choose a store button
      	And I select a store from store list page
      	And I mark the store as favorite
       Then I navigate to Store List and validate the favoritestore
      	
@CougarValidatefavouriteStore_QA      	
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   						|Env	|
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA			   					| QA	|
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>				| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				|	QA	|
 
@CougarValidatefavouriteStore_Prod
      Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   						|Env	 |
      | Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA			   					| Prod |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				| Prod |
 
@CougarValidateErrorMessageNotnearbyStore
  Scenario Outline: Validate the error message when user tries to redeem a coupon when is not nearby the store for the retailer <Retailer> with RBDS code <RBDScode>
      Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>       
     	  And I set the valid device location to <Loc>
     	 When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      	And I click on the Understood button
       Then I validate I'm on the Coupons Home Page
      	And I select a coupon and click on choose a store button
      	And I select a store from store list page
      #	 And I click on the Redeem Now button
     Then I validate the error message on coupon redemption

@CougarValidateErrorMessageNotnearbyStore_QA     
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                  		|Env	|
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 228 W High St, Elkhart, IN 46516	   		| QA	|
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>				| Password1 | 842 B Ave, El Dorado, AR 71730					|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 2332 Henshaw Rd, Inwood, WV 25428   		|	QA	|
   
@CougarValidateErrorMessageNotnearbyStore_Prod 
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   						|Env	 |
      | Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 228 W High St, Elkhart, IN 46516			   					| Prod |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 842 B Ave, El Dorado, AR 71730		| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 2332 Henshaw Rd, Inwood, WV 25428				| Prod |
      
@CougarMapViewCouponRedemtion     
   Scenario Outline: Validate the search By Zip and view the available stores in Store Map view page for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And  I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I select a store from map view
      #And I click on the Redeem Now button
     Then I validate the error message on coupon redemption
      
 @CougarMapViewCouponRedemtion_QA     
 Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                  		|Env	|
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 228 W High St, Elkhart, IN 46516	   		| QA	|
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 842 B Ave, El Dorado, AR 71730					|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>   			| Password1 | 2332 Henshaw Rd, Inwood, WV 25428   		|	QA	|
   
@CougarMapViewCouponRedemtion_Prod 
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                               | Env  |
      | Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	| <EMAIL>	| Password1 | 228 W High St, Elkhart, IN 46516	| Prod |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 842 B Ave, El Dorado, AR 71730		| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	| <EMAIL>	| Password1 | 2332 Henshaw Rd, Inwood, WV 25428	| Prod |
      