package com.rai.steps;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

public class Test {
	
	public static void main(String args[]) {
		LocalDateTime ldt = LocalDateTime.parse("12/31/99 11:59 PM",	DateTimeFormatter.ofPattern("MM/dd/yy h:mm a"));
		LocalDateTime ldt2 = LocalDateTime.parse("12/30/99 11:59 PM",	DateTimeFormatter.ofPattern("MM/dd/yy h:mm a"));
		DateTimeFormatter formmat1 = DateTimeFormatter.ofPattern("MM/dd/yy h:mm a", Locale.ENGLISH);
		System.out.println(ldt);
		// Output "2018-05-12T17:21:53.658"
		String formatter = formmat1.format(ldt);
		System.out.println(formatter);
		System.out.println(ldt2.isBefore(ldt));
		
	}

}
