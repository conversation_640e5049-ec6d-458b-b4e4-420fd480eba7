package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;
//@CucumberOptions(features = "src/test/resources/features/", glue = { "com.rai.steps" }, tags = {"@PallmallIOSLocatonInstruction_Prod"},  monochrome = true, plugin = { "pretty",

@CucumberOptions(features = "src/test/resources/features/", glue = { "com.rai.steps" }, tags = "@americanspiritIOSLocatonInstruction_Prod or @americanspiritIOSShieldIcon_Prod or @CamelIOSLocatonInstruction_Prod or @CamelIOSShieldIcon_Prod or @CamelSnusIOSLocatonInstruction_Prod or @CamelSnusIOSShieldIcon_Prod or @CougarIOSLocatonInstruction_Prod or @CougarIOSShieldIcon_Prod or @GrizzlyIOSLocatonInstruction_Prod or @GrizzlyIOSShieldIcon_Prod or @KodaikIOSLocatonInstruction_Prod or @KodaikIOSShieldIcon_Prod or @LevigarrettIOSLocatonInstruction_Prod or @LevigarrettIOSShieldIcon_Prod or @LuckystrikeIOSLocatonInstruction_Prod or @LuckystrikeIOSShieldIcon_Prod or @NewportIOSLocatonInstruction_Prod or @NewportIOSShieldIcon_Prod or @PallmallIOSLocatonInstruction_Prod or @PallmalIOSShieldIcon_Prod or @VeloIOSLocatonInstruction_Prod or @VeloIOSShieldIcon_Prod or @VuseIOSLocatonInstruction_Prod or @VuseIOSShieldIcon_Prod",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerLoactionservicesIOS_prod extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
