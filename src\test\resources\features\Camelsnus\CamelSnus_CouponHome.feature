Feature: Camelsnus RBDS - Coupon Home
As an ATC user, I should be able to successfully validate the coupon home page for the RBDS Flow for the Brand <Brand> and the Retailer <Retailer>

@CamelsnusValidateCouponHome
  Scenario Outline: Validate the RBDS Coupon Home Page and availability of the coupons for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> 
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I validate the Coupons Home Page
  
  @CamelsnusCouponHome_QAstage        	
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                              | Username                         | Password  | Loc                                       | Env | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://aem-stage.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021             | QA  | 
      | Camelsnus | Murphy               | 741592   | https://aem-stage.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 4931 E Bridge St, Brigton, CO 80601       | QA  | 
      | Camelsnus | Sheetz               | 595324   | https://aem-stage.camelsnus.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106 | QA  | 
      | Camelsnus | Speedway             | 529181   | https://aem-stage.camelsnus.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 1485 NC-66, Kernersville, NC 27284		    | QA  | 
  
  @CamelsnusCouponHome_Prod
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                        | Username                                  | Password  | Loc                                       | Env  | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://www.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021             | PROD | 
      | Camelsnus | Murphy               | 741592   | https://www.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 4931 E Bridge St, Brigton, CO 80601       | PROD | 
      | Camelsnus | Sheetz               | 595324   | https://www.camelsnus.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106 | PROD | 
      | Camelsnus | Speedway             | 529181   | https://www.camelsnus.com/?RBDSCode=529181 | <EMAIL> | Password1 | 1485 NC-66, Kernersville, NC 27284		     | PROD | 
  
  @CamelsnusCouponSort
  Scenario Outline: Validate that the user is able to sort the coupons by value and expiration date for the Brand <Brand> and the Retailer <Retailer>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
     Then I validate the default view of the coupons sorted by value
      And I sort the coupons by expiration date and validate the coupons
  
  @CamelsnusCouponSort_QAStage
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                              | Username                         | Password  | Loc                                       | Env | 
      #| Camelsnus | 7-Eleven Corporation | 572896   | https://aem-stage.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021             | QA  | 
      #| Camelsnus | Murphy               | 741592   | https://aem-stage.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 4931 E Bridge St, Brigton, CO 80601       | QA  | 
      #| Camelsnus | Sheetz               | 595324   | https://aem-stage.camelsnus.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106 | QA  | 
      | Camelsnus | Speedway             | 529181   | https://aem-stage.camelsnus.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 1485 NC-66, Kernersville, NC 27284        | QA  | 
  
  @CamelsnusCouponSort_Prod
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                        | Username                                  | Password  | Loc                                       | Env  | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://www.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021             | PROD | 
      | Camelsnus | Murphy               | 741592   | https://www.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 4931 E Bridge St, Brigton, CO 80601       | PROD | 
      | Camelsnus | Sheetz               | 595324   | https://www.camelsnus.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106 | PROD | 
      | Camelsnus | Speedway             | 529181   | https://www.camelsnus.com/?RBDSCode=529181 | <EMAIL> | Password1 | 1485 NC-66, Kernersville, NC 27284		     | PROD | 
  
  @CamelsnusCouponvalidateRestrictions      
  Scenario Outline: Validate the user is displayed with error message when he tries to access the camelsnus coupon from the restricted states location for the Brand <Brand> and the Retailer <Retailer>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate the error popup stating that coupons are not available for restricted location for the brand <Brand>
  
  @CamelsnusCouponvalidateRestrictions_QAStage 
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                              | Username                         | Password  | Loc                               | Env | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://aem-stage.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Camelsnus | Murphy               | 741592   | https://aem-stage.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Camelsnus | Sheetz               | 595324   | https://aem-stage.camelsnus.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Camelsnus | Speedway             | 529181   | https://aem-stage.camelsnus.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
  
  @CamelsnusCouponvalidateRestrictions_Prod
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                        | Username                                  | Password  | Loc                               | Env  | 
      #| Camelsnus | 7-Eleven Corporation | 572896   | https://www.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD | 
      | Camelsnus | Murphy               | 741592   | https://www.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD | 
      | Camelsnus | Sheetz               | 595324   | https://www.camelsnus.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD | 
      | Camelsnus | Speedway             | 529181   | https://www.camelsnus.com/?RBDSCode=529181 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD | 
  
   @CamelSnus_CopyrightTextinLoginPage
Scenario Outline: Validate the user is able to view the Copyright text in CamelSnus login page 
	 Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
	 And I scroll down and validate copyright text <copyrightText> for <Brand>
	 
	 
	 @CamelSnus_CopyrightTextinLoginPage_QA
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | CAMELSNUS | 7-Eleven Corporation | 572896   | https://aem-stage.camelsnus.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | CAMELSNUS | Murphy               | 741592   | https://aem-stage.camelsnus.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | CAMELSNUS | Sheetz               | 595324   | https://aem-stage.camelsnus.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | CAMELSNUS | Speedway             | 529181   | https://aem-stage.camelsnus.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
		
	 
	 @CamelSnus_CopyrightTextinLoginPage_Prod
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | CAMELSNUS | 7-Eleven Corporation | 572896   | https://www.camelsnus.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | CAMELSNUS | Murphy               | 741592   | https://www.camelsnus.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | CAMELSNUS | Sheetz               | 595324   | https://www.camelsnus.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | CAMELSNUS | Speedway             | 529181   | https://www.camelsnus.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
	