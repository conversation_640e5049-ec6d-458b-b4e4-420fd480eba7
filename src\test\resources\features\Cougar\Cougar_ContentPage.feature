Feature: Cougar RBDS - Content Page Validation
As a RJR user, I will be validating the content page post coupon redemption for the Cougar brand for all the valid retailers.

@CougarValidateContentPostRedemption
  Scenario Outline: Validate the user is able to view the cougar content page post redemption of a coupons and user has even more coupons left in RBDS flow for the brand <Brand> and the retailer <Retailer>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I select a coupon and click on choose a store button
      And I select a store from store list page
      And I click on the Redeem Now button
      And I click on the I'm Done button
      And I Validate the Content Page
      And I validate the tiles on content page for the retailer <Retailer> and for the brand <Brand> with expected Tile1 url <ExpectedTile1Url> and excected Tile2 url <ExpectedTile2Url>      And I click on back to the coupons and validate the list
      And I click on back to the coupons and validate the list
      
			@CougarValidateContentPostRedemption_QA         
			Examples: 

          | Brand  | Retailer						  | RBDScode | URL                                     					  | Username              						| Password  | Loc                                   					| ExpectedTile1Text | ExpectedTile2Text		 |Env |
#		     | Cougar | 7-Eleven Corporation | 572896	 | https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL> 	| Password1 |   623 East Jackson Boulevard, Elkhart, IN, USA|https://aem-stage.cougardips.com/secure/products.html | https://aem-stage.cougardips.com/secure/show-your-work.html						 |QA  |
		      | Cougar | Murphy							  | 741592 	 | https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 448 E MCCAIN BLVD, North Little Rock, AR 72117	|https://aem-stage.cougardips.com/secure/products.html | https://aem-stage.cougardips.com/secure/show-your-work.html	|QA|
#		     	| Cougar | Sheetz							  | 595324 	 | https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>		   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405			|https://aem-stage.cougardips.com/secure/products.html | https://aem-stage.cougardips.com/secure/show-your-work.html						 |QA|
     
     @CougarValidateContentPostRedemption_Prod
      Examples:
        	| Brand  | Retailer						  | RBDScode | URL                                     					  | Username              						| Password  | Loc                                   					| ExpectedTile1Text | ExpectedTile2Text		 |
		      | Cougar | 7-Eleven Corporation | 572896	 | https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL> 	| Password1 | 429 N MAIN ST, Elkhart, IN 46516								| FIERCE FLAVORS    | SHOW YOUR						 |
		      | Cougar | Murphy							  | 741592 	 | https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 448 E MCCAIN BLVD, North Little Rock, AR 72117	| FIERCE FLAVORS    | Looking for Deals, Rewards or a chance to Rev Up?|
		     	| Cougar | Sheetz							  | 595324 	 | https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>		   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405			| FIERCE FLAVORS    | SHOW YOUR						 |

