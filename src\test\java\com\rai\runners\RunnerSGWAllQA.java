package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;


//@CucumberOptions(features = "src/test/resources/features/", glue = { "com.rai.steps" }, tags = {" @VuseTriggertextValidations_QA"},

@CucumberOptions(features = "src/test/resources/features/", glue = { "com.rai.steps" }, tags = "@NASCIGSSGW1Validations_QA or @CamelSGW1Validations_QA or @CamelsnusSGWValidations_QA or @CougarSGW1Validations_QA or @GrizzlySGW1Validations_QA or @KodiakSGW1Validations_QA or @LuckystrikeSGW1Validations_QA or  @NewportSGW1Validations_QA or @PallmallSGW1Validations_QA or  @VeloSGW1Validations_QA or @VuseSGW1alidations_QA or @LevigarrettSGW_QA or @NASCIGSSGW1Validations_QA or @CamelTriggertextValidations_QA or  @CamelsnusSGWValidations_QA or @CougarTriggertextValidations_QA or @GrizzlySGW1Validations_QA or @KodiakTriggertextValidations_QA or @LevigarrettSGW_QA or @LuckystrikeSGW1Validations_QA or @NewportTriggertextValidations_QA or  @PallmallTriggertextValidations_QA or @VeloTriggertextValidations_QA or @VuseTriggertextValidations_QA ",
		 monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

//@NASCIGSSGWValidations_Prod or @@CamelSGWValidations_Prod or @CamelsnusSGWValidations_Prod or @CougarSGWValidations_Prod or @GrizzlySGWValidations_Prod or @KodiakSGWValidations_Prod or @LuckystrikeSGWValidations_Prod or @NewportSGWValidations_Prod or @PallmallSGWValidations_Prod
//@NASCIGSSGWValidations_QA or @CamelSGWValidations_QA or @CamelsnusSGWValidations_QA or @CougarSGWValidations_QA or @GrizzlySGWValidations_QA or @KodiakSGWValidations_QA or @LuckystrikeSGWValidations_QA or @NewportSGWValidations_QA or @PallmallSGWValidations_QA

public class RunnerSGWAllQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
