Feature: Kodiak-Android Location Services
As an RJR user I should be validating the Kodiak Location Services

@KodiakValidateLocationServices
Scenario Outline: Validate Location Services And the navigation to Kodiak Coupon Page for the retailer <Retailer> & the brand <Brand> on <Env> Environment
 	 Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
      When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Kodiak Offers Page
      And I click on Get Offer button
       And I click on the Understood button location service
        And I click on the Learn how 
    And I click on the continue button
    And I Navigate to Chrome App Loc
    And I Navigate to Website Loc
    And I Naviagte to Clearing cache
    Then I click the back to the Kodaik page
    
       @KodiakValidateLocation_QA
    Examples: 
      | Brand       | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801						| QA  | 
     