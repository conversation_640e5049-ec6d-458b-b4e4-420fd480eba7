package com.rai.steps;

import static org.testng.Assert.fail;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.testng.Assert;
import com.rai.framework.CucumberException;
import com.rai.framework.DriverManager;

import com.rai.pages.CouponHomePageObjects;
import com.rai.pages.HomePageObjects;
import com.rai.pages.StoreListViewPageObjects;

import io.appium.java_client.AppiumDriver;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;

public class CouponHomePageStepDefs extends MasterSteps {

	static Logger log = LogManager.getLogger(LocationServicesPageStepDefs.class);
	static AppiumDriver driver = DriverManager.getAppiumDriver();

	// static WebDriver driver1=DriverManager.getWebDriver();
	@And("^I validate I'm on the Coupons Home Page$")
	public void i_validate_im_on_the_coupons_home_page() {
		try {
			// DriverManager.getReportiumClient()
			// .stepStart("Step:The user will validate that he landed on the coupon Home
			// page");
			Thread.sleep(20000);
			// waitUntilElementVisible(CouponHomePageObjects.brandImgCouponHomeHeader, 30);
			// isElementPresentVerification(CouponHomePageObjects.brandImgCouponHomeHeader,
			// "Coupon Home Page - Brand Image Header");
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate the user is on the Coupon Home Page", e);
		}
	}

	@And("^I validate I'm on the Coupons Home Page for Newport$")
	public void i_validate_im_on_the_coupons_home_page_Newport() {
		try {
			// DriverManager.getReportiumClient()
			// .stepStart("Step:The user will validate that he landed on the coupon Home
			// page");
			if (isElementPresent(CouponHomePageObjects.personalizedCoupon)) {
				isElementPresentVerification(CouponHomePageObjects.personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
			}
			if (isElementPresent(CouponHomePageObjects.np_personalizedCoupon)) {
				isElementPresentVerification(CouponHomePageObjects.np_personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
			}
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate the user is on the Coupon Home Page", e);
		}
	}

	@And("^I validate I'm on the Kodiak Coupons Home Page$")
	public void i_validate_im_on_the_Kodiak_coupons_home_page() {
		try {
			// DriverManager.getReportiumClient()
			// .stepStart("Step:The user will validate that he landed on the coupon Home
			// page");
			waitUntilElementVisible(CouponHomePageObjects.KodiakpersonalizedCoupon, 20);
			isElementPresentVerification(CouponHomePageObjects.KodiakpersonalizedCoupon,
					"Coupon Home Page - Personalized Coupon tile");
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate the user is on Kodiak Coupon Home Page", e);
		}
	}

	@And("^I validate I'm on the Newport Coupons Home Page$")
	public void i_validate_im_on_the_Newport_coupons_home_page() {
		try {
			// DriverManager.getReportiumClient()
			// .stepStart("Step:The user will validate that he landed on the coupon Home
			// page");
			if (isElementPresent(CouponHomePageObjects.personalizedCoupon)) {
				isElementPresentVerification(CouponHomePageObjects.personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
			}
			if (isElementPresent(CouponHomePageObjects.np_personalizedCoupon)) {
				isElementPresentVerification(CouponHomePageObjects.np_personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
			}
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate the user is on Newport Coupon Home Page", e);
		}
	}

	@And("^I click on loadmore button and validate the list$")
	public void i_click_on_loadmore_button_and_validate_the_list() {
		try {
			// DriverManager.getReportiumClient()
			// .stepStart("Step: The user will navigate to Loadmore button and validate the
			// store list");
			waitUntilElementVisible(CouponHomePageObjects.btnLoadmore, 30);
			clickIfElementPresent(CouponHomePageObjects.btnLoadmore, "LoadMorebutton");
			List<WebElement> stores = DriverManager.getAppiumDriver()
					.findElements(By.xpath("//span[contains(@id ,'imgleftarrow')]"));
			System.out.println("Number of stores:" + stores.size());
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to  select a coupon & click on a choose a store", e);
		}
	}

	@Then("^I validate the error popup stating that coupons are not available for restricted location for the brand (.+)$")
	public void i_validate_the_error_popup_stating_that_coupons_are_not_available_for_restricted_location_for_the_brand(
			String brand) throws Throwable {
		try {
			brand = brand.toUpperCase().replaceAll("\\s", "");
			String errorMsg = "";
			// DriverManager.getReportiumClient().stepStart("Step: The user will verify
			// error pop_up for restricted area");
			if (brand.equalsIgnoreCase("velo"))
				errorMsg = "We are sorry, but " + brand
						+ " coupons (and similar offers) are not available in New York at this time.";
			else
				errorMsg = "We are sorry, but " + brand
						+ " coupons (and similar offers) are not available in New Jersey at this time.";

			if (brand.equalsIgnoreCase("velo")) {
				// waitUntilElementVisible(CouponHomePageObjects.restrictedStateErrorVelo, 30);
				// WebElement el =
				// driver.findElement(CouponHomePageObjects.restrictedStateErrorVelo);
				// Actions action = new Actions(driver);
				// action.moveToElement(el).build().perform();
				// Thread.sleep(3000);
				isElementPresentContainsText(CouponHomePageObjects.restrictedStateErrorVelo, errorMsg,
						"Coupon Page - Restricted State Error Message");
			} else {
				waitUntilElementVisible(CouponHomePageObjects.restrictedStateErrorVelo, 30);
				isElementPresentContainsText(CouponHomePageObjects.restrictedStateErrorVelo, errorMsg,
						"Coupon Page - Restricted State Error Message");
			}
			if (brand.contains("GRIZZLY"))
				clickIfElementPresent(CouponHomePageObjects.btnrestrictedStateErrorClose_Grizzly,
						"Grizzly Coupon Page - Restricted State error pop up close button");
			else if (brand.contains("Pallmall"))
				clickIfElementPresent(CouponHomePageObjects.btnrestrictedStateErrorClose_Pallmall,
						"Pallmall Coupon Page - Restricted State error pop up close button");
			else if (brand.equalsIgnoreCase("velo"))
				clickIfElementPresent(CouponHomePageObjects.btnrestrictedStateErrorClose_Velo,
						"Velo Coupon Page - Restricted State error pop up close button");
			else
				clickIfElementPresent(CouponHomePageObjects.btnrestrictedStateErrorClose_Velo,
						"Coupon Page - Restricted State error pop up close button");
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate the error pop up for restricted location:", e);
		}
	}

	@Then("^I validate the (.+) present in the Coupons page$")
	public void iValidateThePresentInTheCouponsPage(String coupontype) throws Throwable {
		if (coupontype.contains("Birthday"))
			isElementPresentVerification(CouponHomePageObjects.birthday_coupon, "Birthday Coupon");
		else if (coupontype.contains("Welcome"))
			isElementPresentVerification(CouponHomePageObjects.welcome_coupon, "Welcome Coupon");
		else if (coupontype.contains("All in One")) {
			if (!isElementPresent(CouponHomePageObjects.btn_ChooseStore)) {
				clickIfElementPresent(CouponHomePageObjects.personalizedCoupon, "Clicking on Coupon");
			}
			isElementPresentContainsText(CouponHomePageObjects.couponType, "INTRODUCING ", "All In One Coupon");
			isElementPresentContainsText(CouponHomePageObjects.couponInfo, "ONE BUNDLE. ONE GREAT PRICE.",
					"All in One Coupon Info");
			isElementPresentVerification(CouponHomePageObjects.personalizedCouponImage,
					"Coupon Home - Personalized Coupon Offer - Image");
			isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityTxt,
					"Coupon Home - Personalized Coupon Offer - Validity Text - This Coupon offer is valid for");
			isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityRemaining,
					"Coupon Home - Personalized Coupon Offer - Validity Remaining");
			isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityProgressBar,
					"Coupon Home - Personalized Coupon Offer - Validity Progress Bar");
			isElementPresentVerification(CouponHomePageObjects.btn_ChooseStore, "Choose A Store button");
			if (DriverManager.getAppiumDriver().findElement(CouponHomePageObjects.personalizedCouponOfferDesc).getText()
					.equals("OFF OF VUSE ALL-IN ONE KIT"))
				isElementPresentContainsText(CouponHomePageObjects.personalizedCouponOfferDesc,
						"OFF OF VUSE ALL-IN ONE KIT", "All in One Coupon Description");
			else
				isElementPresentContainsText(CouponHomePageObjects.personalizedCouponOfferDesc,
						"OFF VUSE ALL IN ONE  KIT", "All in One Coupon Description");
		}
	}

	@And("I select a coupon and click on choose a store button")
	public void i_select_a_coupon_and_click_on_choose_a_store_button() {
		// DriverManager.getReportiumClient().stepStart("Step:The user will select the
		// coupon and click on choose store");
		try {
			waitUntilPageReadyStateComplete(50);
			Thread.sleep(5000);
			if (isElementPresent(CouponHomePageObjects.btn_ChooseStore)) {
				waitUntilElementVisible(CouponHomePageObjects.btn_ChooseStore, 30);
				clickIfElementPresent(CouponHomePageObjects.btn_ChooseStore,
						"Coupon Home Page - Choose A Store button");
			}
			// waitUntilElementVisible(CouponHomePageObjects.brandImgCouponHomeHeader, 30);
			// isElementPresentVerification(CouponHomePageObjects.brandImgCouponHomeHeader,
			// "Validating Coupon Home Page");
			else if (isElementPresent(CouponHomePageObjects.btn_ChooseCoupon)) {
				waitUntilElementVisible(CouponHomePageObjects.btn_ChooseCoupon, 30);
				clickIfElementPresent(CouponHomePageObjects.btn_ChooseCoupon,
						"Coupon Home Page - Choose A Coupon button");
				waitUntilElementVisible(CouponHomePageObjects.btn_ChooseStore, 30);
				clickIfElementPresent(CouponHomePageObjects.btn_ChooseStore,
						"Coupon Home Page - Choose A Store button");
			} else if (isElementPresent(CouponHomePageObjects.personalizedCoupon)) {
				waitUntilElementVisible(CouponHomePageObjects.personalizedCoupon, 30);
				clickIfElementPresent(CouponHomePageObjects.personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
				waitUntilElementVisible(CouponHomePageObjects.btn_ChooseStore, 30);
				clickIfElementPresent(CouponHomePageObjects.btn_ChooseStore,
						"Coupon Home Page - Choose A Store button");
			} else {
				waitUntilElementVisible(CouponHomePageObjects.eitherOrCoupon, 30);
				clickIfElementPresent(CouponHomePageObjects.eitherOrCoupon,
						"Coupon Home Page - Either or Coupon tile");
				// waitUntilElementVisible(CouponHomePageObjects.btn_ChooseStore, 30);
				clickIfElementPresent(CouponHomePageObjects.btn_ChooseStore,
						"Coupon Home Page - Choose A Store button");
			}
			scrollToBottom(DriverManager.getAppiumDriver());
			waitUntilElementVisible(StoreListViewPageObjects.txt_Zipcode, 30);
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to click the Choose a Store button: :", e);
		}
	}

	@And("I select a coupon and click on choose a store button for Newport")
	public void i_select_a_coupon_and_click_on_choose_a_store_button_Newport() {
		// DriverManager.getReportiumClient().stepStart("Step:The user will select the
		// coupon and click on choose store");
		try {
			if (isElementPresent(CouponHomePageObjects.btn_ChooseStore)) {
				waitUntilElementVisible(CouponHomePageObjects.btn_ChooseStore, 30);
				clickIfElementPresent(CouponHomePageObjects.btn_ChooseStore,
						"Coupon Home Page - Choose A Store button");
			} else if (isElementPresent(CouponHomePageObjects.personalizedCoupon)) {
				clickIfElementPresent(CouponHomePageObjects.personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
				waitUntilElementVisible(CouponHomePageObjects.btn_ChooseStore, 30);
				clickIfElementPresent(CouponHomePageObjects.btn_ChooseStore,
						"Coupon Home Page - Choose A Store button");
			} else if (isElementPresent(CouponHomePageObjects.btn_NpChooseStore)) {
				waitUntilElementVisible(CouponHomePageObjects.btn_NpChooseStore, 30);
				clickIfElementPresent(CouponHomePageObjects.btn_NpChooseStore,
						"Coupon Home Page - Choose A Store button");
			} else if (isElementPresent(CouponHomePageObjects.eitherOrCoupon)) {
				clickIfElementPresent(CouponHomePageObjects.eitherOrCoupon,
						"Coupon Home Page - Either Or Coupon tile");
				waitUntilElementVisible(CouponHomePageObjects.btn_ChooseStore, 30);
				clickIfElementPresent(CouponHomePageObjects.btn_ChooseStore,
						"Coupon Home Page - Choose A Store button");
			} else {
				waitUntilElementVisible(CouponHomePageObjects.np_personalizedCoupon, 30);
				clickIfElementPresent(CouponHomePageObjects.np_personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
				waitUntilElementVisible(CouponHomePageObjects.btn_NpChooseStore, 30);
				clickIfElementPresent(CouponHomePageObjects.btn_NpChooseStore,
						"Coupon Home Page - Choose A Store button");
			}
			scrollToBottom(DriverManager.getAppiumDriver());
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to click the Choose a Store button:", e);
		}
	}

	@Then("^I validate the default view of the coupons sorted by value$")
	public void i_validate_the_default_view_of_the_coupons_sorted_by_value() {
		try {
			List<WebElement> couponValuesListEle = DriverManager.getAppiumDriver()
					.findElements(CouponHomePageObjects.couponValuesTxt);
			List<WebElement> couponDescs = DriverManager.getAppiumDriver()
					.findElements(CouponHomePageObjects.personalizedCouponOfferDesc);
			List<Double> couponValues = new ArrayList<Double>();
			System.out.println("desc " + couponValuesListEle.size());
			int descSize = couponValuesListEle.size();
			for (int i = 0; i < descSize - 1; i++) {
				highLightElement(couponValuesListEle.get(i), "green");
				System.out.println("Coupon Description: " + couponDescs.get(i).getText());
				if (!(couponDescs.get(i).getText().contains("SNUS") || couponDescs.get(i).getText().contains("VELO"))
						|| LoginPageStepDefs.getBrand().contains("Camelsnus")) {
					StringBuilder value = new StringBuilder(couponValuesListEle.get(i).getText());
					double couponValue = Double.parseDouble(value.deleteCharAt(0).toString());
					System.out.println(couponValue);
					couponValues.add(couponValue);
				}
			}
			boolean sortedCheck = true;
			System.out.println("Coupons displayed before sorting is : " + couponValues);
			for (int i = 0; i < couponValues.size() - 1; i++) {
				if (!((couponValues.get(i).equals(couponValues.get(i + 1))
						|| (couponValues.get(i) > couponValues.get(i + 1))))) {
					sortedCheck = false;
				}
				System.out.println("Sorted value 1: " + couponValues.get(i));
				System.out.println("Sorted value 2: " + couponValues.get(i + 1));
			}
			// DriverManager.getReportiumClient()
			// .stepStart("Validate that the coupons are defalut sorted based on the
			// Value");
			if (sortedCheck == false) {
				// DriverManager.getReportiumClient()
				// .reportiumAssert("The coupons are NOT defalut sorted based on Value: " +
				// couponValues, false);
				Assert.assertTrue(false, "The coupons are NOT defalut sorted based on Value: " + couponValues);
				// DriverManager.getReportiumClient().stepEnd();
				attachScreenshotForMobile(false);
				fail("The coupons are not  default sorted based on Value : " + couponValues);
			} else {
				// DriverManager.getReportiumClient().reportiumAssert(
				// "The coupons are defalut sorted based on Value successfully : " +
				// couponValues, true);
				// DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(true, "The coupons are default sorted based on Value successfully : " + couponValues);
				addStepLog("The coupons are defalut sorted based on Value successfully : " + couponValues);
				attachScreenshotForMobile(true);
			}
		} catch (Exception e) {
			failTestScript("Failed to validate default value of coupons:", e);
		}
	}

	@And("^I scroll down and validate that SGW is present for the relevant quarter Q1 (.+) Q2 (.+) Q3 (.+) Q4 (.+) with SGW element (.+)$")
	public void i_scroll_down_and_validate_that_sgw_is_present_for_the_relevant_quarter_q1_q2_q3_q4_with_sgw_element(
			String sgwq1, String sgwq2, String sgwq3, String sgwq4, String element) throws Throwable {
		// DriverManager.getReportiumClient().stepStart("Validate the SGW text");
		try {
			WebElement webele = null;
			waitFor(6000);
			// scrollToBottom(DriverManager.getAppiumDriver());
			// scrollDown();

			scrollToBottom(DriverManager.getAppiumDriver());
			scrollDown();
			scrollDownFromMid();
			Thread.sleep(1500);

			// if (!isElementPresent(By.xpath(element))) {
			// ((JavascriptExecutor) driver).executeScript("mobile: scrollGesture",
			// ImmutableMap.of(
			// "left", 100, "top", 200, "width", 200, "height", 600,
			// "direction", "down",
			// "percent", 50.0 ));
			// }
			// if (!isElementPresent(By.xpath(element))) {
			// ((JavascriptExecutor) driver).executeScript("mobile: scrollGesture",
			// ImmutableMap.of(
			// "left", 100, "top", 200, "width", 200, "height", 600,
			// "direction", "down",
			// "percent", 50.0 ));
			// }
			// if (!isElementPresent(By.xpath(element))) {
			// ((JavascriptExecutor) driver).executeScript("mobile: scrollGesture",
			// ImmutableMap.of(
			// "left", 100, "top", 200, "width", 200, "height", 600,
			// "direction", "down",
			// "percent", 50.0 ));
			// }
			System.out.println("***************************************flag 1****************************************");
			System.out.println("is SGW Element Present : " + isElementPresent(By.xpath(element)));
			System.out.println("***************************************flag 1****************************************");
			String expectedSGWtext = getExpectedSGWText(sgwq1, sgwq2, sgwq3, sgwq4);
			List<WebElement> elements = DriverManager.getAppiumDriver().findElements(By.xpath(element));
			for (WebElement ele : elements) {
				if (ele.isDisplayed()) {
					webele = ele;
					highLightElement(ele, "LawnGreen");
					// String actualImageText = extractImageTextUsingScreenShotOfElement(webele);
					// actualImageText = actualImageText.replace("\n", " ");
					// compareStringsContains(actualImageText, expectedSGWtext);
					scrollToTop(DriverManager.getAppiumDriver());
					// DriverManager.getReportiumClient().stepEnd();
					break;
				}
			}
			if (webele == null) {
				System.out.println("[INFO] Not able to find the SGW image on the page");
			}
		} catch (Exception e) {
			failTestScript("[INFO] The element- " + element + " is NOT present or text extracted is NOT correct", e);
		}
	}

	@Then("^I validate that SGW is present for the relevant quarter Q1 (.+) Q2 (.+) Q3 (.+) Q4 (.+) with SGW element(.+)triggertext(.+)text(.+)$")
	public void i_validate_that_sgw_is_present_for_the_relevant_quarter_q1_q2_q3_q4_with_sgw_element(String sgwq1,
			String sgwq2, String sgwq3, String sgwq4, String element, String Triggertext, String text)
			throws Throwable {
		try {
			String expectedSGWtext = getExpectedSGWText(sgwq1, sgwq2, sgwq3, sgwq4);
			System.out.println("Expected text " + expectedSGWtext);
			String expectedtriggertext = text;
			System.out.println("Expected text " + expectedtriggertext);
			waitFor(6000);
			if (LoginPageStepDefs.getBrand().equalsIgnoreCase("VUSE") && element.contains(" or ")) {
				scrollToBottom(DriverManager.getAppiumDriver());
				scrollDown();
				scrollDownFromMid();
				element = element.replace(" or ", " | ");
			}
			if (!LoginPageStepDefs.getBrand().equalsIgnoreCase("VUSE")
					&& !LoginPageStepDefs.getBrand().equalsIgnoreCase("VELO")) {
				scrollToBottom(DriverManager.getAppiumDriver());
				scrollDown();
				scrollDownFromMid();
			} else {
				scrollToBottom(DriverManager.getAppiumDriver());
				scrollDown();
				scrollDownFromMid();
				scrollDownFromMid();
				scrollDownFromMid();
				clickIfElementPresent(By.xpath(element), "SGW");
			}
			// perfectoScroll();
			waitFor(2000);
			highLightElement(driver.findElement(By.xpath(element)));
			String actualImageText = extractImageTextUsingScreenShotOfElement(By.xpath(element));
			System.out.println("Actual Text " + actualImageText);
			waitFor(1000);
			// actualImageText=actualImageText.replace("\n", " ");
			// compareStringsContains(actualImageText, expectedSGWtext);

			WebElement e = driver.findElement(By.xpath(Triggertext));

			// scrollToMobileElement(text);
			// scrollToBottom(driver);
			scrollToBottom(DriverManager.getAppiumDriver());
			scrollToElement(driver, e);

			String actualImagetriggertext = e.getText();
			System.out.println(e.getText());
			System.out.println("Actual Text " + actualImagetriggertext);

			// compareStringsContains(actualImagetriggertext, expectedtriggertext);

			System.out.println("success");
			scrollToTop(DriverManager.getAppiumDriver());
		} catch (Exception e) {
			failTestScript("Validating the SGW elements for the relevant Quarter failed", e);
		}
	}

	@Then("^I validate triggertext(.+)text(.+)$")
	public void i_validate_triggertext(String Triggertext, String text) {
		try {
			String expectedtext = text;

			waitFor(1000);

			scrollToBottom(DriverManager.getAppiumDriver());
			scrollDown();
			scrollDownFromMid();
			Thread.sleep(1500);
			// highLightElement(DriverManager.getAppiumDriver().findElement(By.xpath(Triggertext)));
			// String actualTriggertext;
			// if((LoginPageStepDefs.getBrand().equalsIgnoreCase("VELO"))||(LoginPageStepDefs.getBrand().contains("Vuse"))||(LoginPageStepDefs.getBrand().contains("Luckystrike")))
			// actualTriggertext = extractImageTextUsingScreenShot();
			// else
			// { Thread.sleep(1000);
			// WebElement ele1 =
			// DriverManager.getAppiumDriver().findElement(By.xpath(Triggertext));
			// Thread.sleep(1000);
			// highLightElement(ele1);
			// actualTriggertext =
			// extractImageTextUsingScreenShotOfElement(By.xpath(Triggertext));
			// }
			// waitFor(1000);
			// actualTriggertext = actualTriggertext.replace("\n", " ");
			// compareStringsContains(actualTriggertext, expectedtext);

			WebElement e = driver.findElement(By.xpath(Triggertext));
			String actual = e.getText();
			compareStringsContains(actual, expectedtext);
			scrollToTop(DriverManager.getAppiumDriver());

		} catch (Exception e) {
			failTestScript("Failed to extracting the text", e);
		}
	}

	@Then("^I validate coupontriggertext(.+)text(.+)$")
	public void i_validate_coupon_triggertext(String CouponElementTriggertext, String text) {
		try {
			String expectedtext = text;

			waitFor(1000);

			scrollDownFromMid();
			scrollDownFromMid();
			scrollDownFromMid();
			scrollDownFromMid();
			scrollToBottom(DriverManager.getAppiumDriver());
			scrollDown();

			Thread.sleep(1500);
			// highLightElement(DriverManager.getAppiumDriver().findElement(By.xpath(Triggertext)));
			// String actualTriggertext;
			// if((LoginPageStepDefs.getBrand().equalsIgnoreCase("VELO"))||(LoginPageStepDefs.getBrand().contains("Vuse"))||(LoginPageStepDefs.getBrand().contains("Luckystrike")))
			// actualTriggertext = extractImageTextUsingScreenShot();
			// else
			// { Thread.sleep(1000);
			// WebElement ele1 =
			// DriverManager.getAppiumDriver().findElement(By.xpath(Triggertext));
			// Thread.sleep(1000);
			// highLightElement(ele1);
			// actualTriggertext =
			// extractImageTextUsingScreenShotOfElement(By.xpath(Triggertext));
			// }
			// waitFor(1000);
			// actualTriggertext = actualTriggertext.replace("\n", " ");
			// compareStringsContains(actualTriggertext, expectedtext);

			// scrollToMobileElement("SNUS");
			WebElement e = driver.findElement(By.xpath(CouponElementTriggertext));
			scrollToElement(driver, e);
			String actual = e.getText();
			compareStringsContains(actual, expectedtext);
			scrollToTop(DriverManager.getAppiumDriver());

		} catch (Exception e) {
			failTestScript("Failed to extracting the text", e);
		}
	}

	@Then("^I validate storelisttriggertext(.+)text(.+)$")
	public void i_validate_storelisttriggertext_triggertext(String StorelistElementTriggertext, String text) {
		try {
			String expectedtext = text;

			waitFor(1000);

			scrollToBottom(DriverManager.getAppiumDriver());
			scrollDown();
			scrollDownFromMid();
			Thread.sleep(1500);
			// highLightElement(DriverManager.getAppiumDriver().findElement(By.xpath(Triggertext)));
			// String actualTriggertext;
			// if((LoginPageStepDefs.getBrand().equalsIgnoreCase("VELO"))||(LoginPageStepDefs.getBrand().contains("Vuse"))||(LoginPageStepDefs.getBrand().contains("Luckystrike")))
			// actualTriggertext = extractImageTextUsingScreenShot();
			// else
			// { Thread.sleep(1000);
			// WebElement ele1 =
			// DriverManager.getAppiumDriver().findElement(By.xpath(Triggertext));
			// Thread.sleep(1000);
			// highLightElement(ele1);
			// actualTriggertext =
			// extractImageTextUsingScreenShotOfElement(By.xpath(Triggertext));
			// }
			// waitFor(1000);
			// actualTriggertext = actualTriggertext.replace("\n", " ");
			// compareStringsContains(actualTriggertext, expectedtext);

			WebElement e = driver.findElement(By.xpath(StorelistElementTriggertext));
			String actual = e.getText();
			compareStringsContains(actual, expectedtext);
			scrollToTop(DriverManager.getAppiumDriver());

		} catch (Exception e) {
			failTestScript("Failed to extracting the text", e);
		}
	}

	@Then("^I validate StoreDetails(.+)text(.+)$")
	public void StoreDetails(String StoreDetails, String text) {
		try {
			String expectedtext = text;

			waitFor(1000);

			scrollToBottom(DriverManager.getAppiumDriver());
			scrollDown();
			scrollDownFromMid();
			Thread.sleep(1500);
			// highLightElement(DriverManager.getAppiumDriver().findElement(By.xpath(Triggertext)));
			// String actualTriggertext;
			// if((LoginPageStepDefs.getBrand().equalsIgnoreCase("VELO"))||(LoginPageStepDefs.getBrand().contains("Vuse"))||(LoginPageStepDefs.getBrand().contains("Luckystrike")))
			// actualTriggertext = extractImageTextUsingScreenShot();
			// else
			// { Thread.sleep(1000);
			// WebElement ele1 =
			// DriverManager.getAppiumDriver().findElement(By.xpath(Triggertext));
			// Thread.sleep(1000);
			// highLightElement(ele1);
			// actualTriggertext =
			// extractImageTextUsingScreenShotOfElement(By.xpath(Triggertext));
			// }
			// waitFor(1000);
			// actualTriggertext = actualTriggertext.replace("\n", " ");
			// compareStringsContains(actualTriggertext, expectedtext);

			WebElement e = driver.findElement(By.xpath(StoreDetails));
			String actual = e.getText();
			compareStringsContains(actual, expectedtext);
			scrollToTop(DriverManager.getAppiumDriver());

		} catch (Exception e) {
			failTestScript("Failed to extracting the text", e);
		}
	}

	@And("^I sort the coupons by expiration date and validate the coupons$")
	public void i_sort_the_coupons_by_expiration_date_and_validate_the_coupons() {
		try {
			List<WebElement> couponExpiry;
			List<WebElement> couponDescs;
			if (brand.get().contains("Grizzly")) {
				clickIfElementPresent(CouponHomePageObjects.btnSortOffersG, "Coupon Home Page - Sort Offers Button");
				couponExpiry = DriverManager.getAppiumDriver()
						.findElements(CouponHomePageObjects.personalizedCouponTileExpiryG);
				couponDescs = DriverManager.getAppiumDriver()
						.findElements(CouponHomePageObjects.personalizedCouponOfferDescG);
			} else {
				clickIfElementPresent(CouponHomePageObjects.btnSortOffers, "Coupon Home Page - Sort Offers Button");
				couponExpiry = DriverManager.getAppiumDriver()
						.findElements(CouponHomePageObjects.personalizedCouponTileExpiry);
			}
			couponDescs = DriverManager.getAppiumDriver()
					.findElements(CouponHomePageObjects.personalizedCouponOfferDesc);
			List<LocalDateTime> dates = new ArrayList<LocalDateTime>();
			System.out.println("desc: " + couponExpiry.size());
			for (int i = 0; i < couponExpiry.size(); i++) {
				highLightElement(couponExpiry.get(i), "green");
				System.out.println("Coupon Description: " + couponDescs.get(i).getText());
				if (!(couponDescs.get(i).getText().contains("SNUS") || couponDescs.get(i).getText().contains("VELO"))
						|| LoginPageStepDefs.getBrand().contains("Camelsnus")) {
					String[] expiryDte = couponExpiry.get(i).getText().split(" ");
					LocalDateTime ldt = LocalDateTime.parse(expiryDte[1] + " " + expiryDte[3] + " " + expiryDte[4],
							DateTimeFormatter.ofPattern("MM/dd/yy h:mm a"));
					dates.add(ldt);
				}

			}
			boolean expiryCheck = true;
			for (int i = 0; i < dates.size() - 1; i++) {
				System.out.println("Date1: " + dates.get(i));
				System.out.println("Date2: " + dates.get(i + 1));
				if (!(dates.get(i).equals(dates.get(i + 1)) || dates.get(i).isBefore(dates.get(i + 1)))) {
					expiryCheck = false;
				}
			}
			// DriverManager.getReportiumClient()
			// .stepStart("Validate that the coupons are sorted based on the expiry date");
			if (expiryCheck == false) {
				// DriverManager.getReportiumClient()
				// .reportiumAssert("The coupons are NOT sorted based on expiry Date: " + dates,
				// false);
				Assert.assertTrue(false, "The coupons are NOT sorted based on expiry Date: " + dates);
				// DriverManager.getReportiumClient().stepEnd();
				attachScreenshotForMobile(false);
				fail("The coupons are not sorted based on expiry date : " + dates);
			} else {
				// DriverManager.getReportiumClient()
				// .reportiumAssert("The coupons are sorted based on expiry Date successfully :
				// " + dates, true);
				// DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(true, "The coupons are sorted based on expiry Date successfully : " + dates);
				addStepLog("The coupons are sorted based on expiry Date successfully : " + dates);
				attachScreenshotForMobile(true);
			}
			System.out.println("Coupons displayed after sorting by expiry : " + dates);
		} catch (Exception e) {
			failTestScript("Failed to validate sort the coupon by value & expiry date:", e);
		}

	}

	@Then("I validate that the coupons are sorted by value")
	public void i_validate_that_the_coupons_are_sorted_by_value() {
		try {
			// DriverManager.getReportiumClient()
			// .stepStart("Validate that the coupons are sorted by value");
			List<WebElement> Couponvalue = DriverManager.getAppiumDriver()
					.findElements(By.id("flxPersonalizedCoupon_rctxOfferValue"));

			List<String> a1 = new ArrayList<String>();
			List<String> temp = new ArrayList<String>();

			for (int i = 0; i < Couponvalue.size(); i++) {
				String value = Couponvalue.get(i).getText();
				if (!value.equals("$")) {
					a1.add(value);
					temp.add(value);
				}
			}
			System.out.println("CouponPage-Default Coupon display order: " + a1);
			Collections.sort(a1, Collections.reverseOrder());

			if (temp.equals(a1) == true) {
				System.out.println(" Array List are equal");
				// DriverManager.getReportiumClient()
				// .reportiumAssert("Sort by Value:Coupons are sorted by descending value : " +
				// temp, true);
			} else {
				System.out.println(" Array List are not equal");
				// DriverManager.getReportiumClient()
				// .reportiumAssert("Sort by Value:Coupons are not sorted by descending value :
				// " + temp, false);
			}
			clickIfElementPresent(CouponHomePageObjects.btnSort, "Coupon Home Page - Sort by Value button");
			Thread.sleep(2000);

			List<WebElement> CouponExpiry = DriverManager.getAppiumDriver()
					.findElements(By.xpath("//div[@id='flxPersonalizedCoupon_lblexpires']"));
			List<String> a2 = new ArrayList<String>();
			List<String> temp2 = new ArrayList<String>();

			for (int j = 0; j < CouponExpiry.size(); j++) {
				String[] expiry1 = CouponExpiry.get(j).getText().split(" ");
				String formattedexpiry1 = expiry1[1].trim();

				a2.add(formattedexpiry1);
				temp2.add(formattedexpiry1);
			}
			System.out.println("CouponPage-SortBy CouponExpiry: " + a2);
			Collections.sort(a2);
			if (a2.equals(temp2) == true) {
				System.out.println(" Array List are equal");
				// DriverManager.getReportiumClient().reportiumAssert("Sort by Value:Coupons are
				// sorted by expiry : " + a2,
				// true);
				// DriverManager.getReportiumClient().stepEnd();
			} else {
				System.out.println(" Array List are not equal");
				// DriverManager.getReportiumClient().stepEnd();
				// DriverManager.getReportiumClient()
				// .reportiumAssert("Sort by Value:Coupons are not sorted by expiry : " + a2,
				// false);
				throw new CucumberException("Array List are not equal");
			}
		} catch (Exception e) {
			failTestScript("Failed to validate the coupons are sorted by value:", e);
		}
	}

	@And("^I validate the Coupons Home Page$")
	public void i_validate_the_coupons_home_page() {
		try {
			// DriverManager.getReportiumClient().stepStart("Step: I validate the Coupons
			// Home Page");
			if (brand.get().contains("Grizzly")) {
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponG,
						"Coupon Home Page - Personalized Coupon tile");
			} else {
				isElementPresentVerification(CouponHomePageObjects.personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
			}
			if (isElementPresent(CouponHomePageObjects.btn_ChooseStore)) {
				waitUntilElementVisible(CouponHomePageObjects.personalizedCoupon, 30);
				// clickIfElementPresent(CouponHomePage.personalizedCoupon,"Coupon Home Page -
				// Personalized Coupon tile");
				isElementPresentVerification(CouponHomePageObjects.brandImgCouponHomeHeader,
						"Brand icon - Header - Coupon Home Page");
				isElementPresentVerification(CouponHomePageObjects.couponCount, "Coupon Home - Coupon Count icon");
				isElementPresentVerification(CouponHomePageObjects.hamburgerMenu, "Coupon Home - Hamburger menu");
				if (brand.get().contains("Grizzly")) {
					isElementPresentVerification(CouponHomePageObjects.welcomFirstNameTxtG,
							"Coupon Home - Welcome First name text");
					isElementPresentContainsText(CouponHomePageObjects.lblSortByG, "Sort By:",
							"Coupon Home Page - Sort By label");
				} else {
					isElementPresentVerification(CouponHomePageObjects.welcomFirstNameTxt,
							"Coupon Home - Welcome First name text");
					isElementPresentContainsText(CouponHomePageObjects.lblSortBy, "Sort By:",
							"Coupon Home Page - Sort By label");
				}
				// img[contains(@class,'_couponsHome-sortBy-image')]
				isElementPresentVerification(CouponHomePageObjects.btnSortOffers,
						"Coupon Home Page - Sort Offers Button");
				if ((brand.get().contains("Grizzly"))) {
					isElementPresentVerification(CouponHomePageObjects.personalizedCouponImageG,
							"Coupon Home - Personalized Coupon Offer - Image");
				} else {
					isElementPresentVerification(CouponHomePageObjects.personalizedCouponImage,
							"Coupon Home - Personalized Coupon Offer - Image");
				}
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponTileExpiry,
						"Coupon Home - Personalized Coupon - Expiry");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponOfferValue,
						"Coupon Home  - Personalized Coupon Offer Value");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponOfferDesc,
						"Coupon Home  - Personalized Coupon Offer Description");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponLblGood,
						"Coupon Home - Personalized Coupon Offer label - Good On Any Style");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityTxt,
						"Coupon Home - Personalized Coupon Offer - Validity Text - This Coupon offer is valid for");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityRemaining,
						"Coupon Home - Personalized Coupon Offer - Validity Remaining");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityProgressBar,
						"Coupon Home - Personalized Coupon Offer - Validity Progress Bar");
				isElementPresentVerification(CouponHomePageObjects.btn_ChooseStore, "Choose A Store button");
				System.out.println(brand.get());
				if (!(brand.get().contains("VELO") || brand.get().contains("Vuse")))
					isElementPresentContainsText(CouponHomePageObjects.couponHomeLocationDisclaimer,
							"Offers can only be redeemed at participating stores.",
							"Coupon Home  - Location Disclaimer Text");
				isElementPresentContainsText(CouponHomePageObjects.lblCouponTerms, "COUPON TERMS",
						"Choose A Store button");
				isElementPresentContainsText(CouponHomePageObjects.msgCouponTerms, "VOID WHERE PROHIBITED",
						"Choose A Store button");
				String descCouponTerms1;
				System.out.println(env.get());
				if (LoginPageStepDefs.getBrand().contains("NASCIGS"))
					descCouponTerms1 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. Consumer may pay sales tax.";
				else
					descCouponTerms1 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. Consumer may pay sales tax.";
				isElementPresentContainsText(CouponHomePageObjects.descCouponTerms, descCouponTerms1,
						"Coupon Home - Coupon Terms description 1");
				isElementPresentContainsText(CouponHomePageObjects.lnkCouonTermsReadMore, "Read More",
						"Coupon Terms - Read More link");
				clickIfElementPresent(CouponHomePageObjects.lnkCouonTermsReadMore, "Coupon Terms - Read More link");
				String descCouponTerms2;
				String descCouponTerms3;
				String descCouponTerms4;
				String descCouponTerms5;
				if (LoginPageStepDefs.getBrand().contains("NASCIGS")) {
					descCouponTerms2 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice.";
					descCouponTerms3 = "Consumer must pay any sales tax.";
					descCouponTerms4 = "This coupon is good only for the brand specified. Coupon may be used only one time. Void if copied, exchanged or transferred before acceptance by retailer. Participation in this promotion is at the discretion of the retailer. All promotional costs paid by manufacturer. Limited to one coupon per offer specified on coupon and cannot be combined with any other offers, promotions or discounts.";
					descCouponTerms5 = "Reynolds Marketing Services Company will reimburse retailer the face value ($ OFF) normal retail price or normal retail price minus the dollar value on the coupon, whichever is applicable to this coupon offer, provided retailer and consumer have complied with the terms of this offer and our coupon redemption policy, incorporated by reference (copies available). Cash value 1/20 of 1¢. Good only at retail locations listed in the store locator.";

				} else {
					descCouponTerms2 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. ";
					descCouponTerms3 = "Consumer must pay any sales tax.";
					descCouponTerms4 = "This coupon is good only for the brand specified. Coupon may be used only one time. Void if copied, exchanged or transferred before acceptance by retailer. Participation in this promotion is at the discretion of the retailer. All promotional costs paid by manufacturer. Limited to one coupon per offer specified on coupon and cannot be combined with any other offers, promotions or discounts.";
					descCouponTerms5 = "Reynolds Marketing Services Company will reimburse retailer the face value ($ OFF) normal retail price or normal retail price minus the dollar value on the coupon, whichever is applicable to this coupon offer, provided retailer and consumer have complied with the terms of this offer and our coupon redemption policy, incorporated by reference (copies available). Cash value 1/20 of 1¢. Good only at retail locations listed in the store locator.";
				}
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms2,
						"Coupon Home - Coupon Terms description 2");
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms3,
						"Coupon Home - Coupon Terms description 3");
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms4,
						"Coupon Home - Coupon Terms description 4");
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms5,
						"Coupon Home - Coupon Terms description 5");
				isElementPresentContainsText(CouponHomePageObjects.lnkCouponTermsReadLess, "Read Less",
						"Coupon Terms - Read Less link");
				// DriverManager.getReportiumClient().stepEnd();
			} else {
				if (brand.get().contains("Grizzly")) {
					isElementPresentVerification(CouponHomePageObjects.personalizedCouponG,
							"Coupon Home Page - Personalized Coupon tile");
				} else {
					isElementPresentVerification(CouponHomePageObjects.personalizedCoupon,
							"Coupon Home Page - Personalized Coupon tile");
				}
				isElementPresentVerification(CouponHomePageObjects.brandImgCouponHomeHeader,
						"Brand icon - Header - Coupon Home Page");
				// isElementPresentVerification(CouponHomePageObjects.couponCount, "Coupon Home
				// - Coupon Count icon");
				isElementPresentVerification(CouponHomePageObjects.hamburgerMenu, "Coupon Home - Hamburger menu");
				if (brand.get().contains("Grizzly")) {
					isElementPresentVerification(CouponHomePageObjects.welcomFirstNameTxtG,
							"Coupon Home - Welcome First name text");
				} else {
					isElementPresentVerification(CouponHomePageObjects.welcomFirstNameTxt,
							"Coupon Home - Welcome First name text");
				}
				if ((brand.get().contains("Pallmall")) || (brand.get().equalsIgnoreCase("Camel"))) {
					isElementPresentContainsText(CouponHomePageObjects.lblSortBy, "Sort By:",
							"Coupon Home Page - Sort By label");
				} else if ((brand.get().contains("Grizzly"))) {
					isElementPresentContainsText(CouponHomePageObjects.lblSortByG, "Sort By:",
							"Coupon Home Page - Sort By label");
					isElementPresentVerification(CouponHomePageObjects.personalizedCouponImageG,
							"Coupon Home - Personalized Coupon Offer - Image");
					// Thread.sleep(1000);
					// isElementPresentVerification(CouponHomePageObjects.personalizedCouponTileExpiryG,
					// "Coupon Home - Personalized Coupon - Expiry");
					isElementPresentVerification(CouponHomePageObjects.personalizedCouponOfferValueG,
							"Coupon Home  - Personalized Coupon Offer Value");
					isElementPresentVerification(CouponHomePageObjects.personalizedCouponLblGoodG,
							"Coupon Home - Personalized Coupon Offer label - Good On Any Style");
				} else {
					isElementPresentContainsText(CouponHomePageObjects.lblSortBy, "Sort By:",
							"Coupon Home Page - Sort By label");
					isElementPresentVerification(CouponHomePageObjects.btnSortOffers,
							"Coupon Home Page - Sort Offers Button");
					isElementPresentVerification(CouponHomePageObjects.personalizedCouponImage,
							"Coupon Home - Personalized Coupon Offer - Image");
					// isElementPresentVerification(CouponHomePageObjects.personalizedCouponTileExpiry,
					// "Coupon Home - Personalized Coupon - Expiry");
					isElementPresentVerification(CouponHomePageObjects.personalizedCouponOfferValue,
							"Coupon Home  - Personalized Coupon Offer Value");
					isElementPresentVerification(CouponHomePageObjects.personalizedCouponOfferDesc,
							"Coupon Home  - Personalized Coupon Offer Description");
					isElementPresentVerification(CouponHomePageObjects.personalizedCouponLblGood,
							"Coupon Home - Personalized Coupon Offer label - Good On Any Style");
				}

				// isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityTxt,
				// "Coupon Home - Personalized Coupon Offer - Validity Text - This Coupon offer
				// is valid for");
				// isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityRemaining,
				// "Coupon Home - Personalized Coupon Offer - Validity Remaining");
				// isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityProgressBar,
				// "Coupon Home - Personalized Coupon Offer - Validity Progress Bar");
				// isElementPresentVerification(CouponHomePageObjects.btn_ChooseStore, "Choose A
				// Store button");
				System.out.println(brand.get());
				if (!((brand.get().contains("VELO"))) || ((brand.get().contains("Vuse"))))
					isElementPresentContainsText(CouponHomePageObjects.couponHomeLocationDisclaimer,
							"Offers can only be redeemed at participating stores.",
							"Coupon Home  - Location Disclaimer Text");
				isElementPresentContainsText(CouponHomePageObjects.lblCouponTerms, "COUPON TERMS",
						"Choose A Store button");
				isElementPresentContainsText(CouponHomePageObjects.msgCouponTerms, "VOID WHERE PROHIBITED",
						"Choose A Store button");
				String descCouponTerms1;
				System.out.println(env.get());
				if (LoginPageStepDefs.getBrand().contains("NASCIGS"))
					descCouponTerms1 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. Consumer may pay sales tax.";
				else
					descCouponTerms1 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. Consumer may pay sales tax.";
				isElementPresentContainsText(CouponHomePageObjects.descCouponTerms, descCouponTerms1,
						"Coupon Home - Coupon Terms description 1");
				isElementPresentContainsText(CouponHomePageObjects.lnkCouonTermsReadMore, "Read More",
						"Coupon Terms - Read More link");
				clickIfElementPresent(CouponHomePageObjects.lnkCouonTermsReadMore, "Coupon Terms - Read More link");
				Thread.sleep(5000);
				String descCouponTerms2;
				String descCouponTerms3;
				String descCouponTerms4;
				String descCouponTerms5;
				if (LoginPageStepDefs.getBrand().contains("NASCIGS")) {
					descCouponTerms2 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. ";
					descCouponTerms3 = "Consumer must pay any sales tax.";
					descCouponTerms4 = "This coupon is good only for the brand specified. Coupon may be used only one time. Void if copied, exchanged or transferred before acceptance by retailer. Participation in this promotion is at the discretion of the retailer. All promotional costs paid by manufacturer. Limited to one coupon per offer specified on coupon and cannot be combined with any other offers, promotions or discounts.";
					descCouponTerms5 = "Reynolds Marketing Services Company will reimburse retailer the face value ($ OFF) normal retail price or normal retail price minus the dollar value on the coupon, whichever is applicable to this coupon offer, provided retailer and consumer have complied with the terms of this offer and our coupon redemption policy, incorporated by reference (copies available). Cash value 1/20 of 1¢. Good only at retail locations listed in the store locator.";

				} else {
					descCouponTerms2 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. Consumer must pay any sales tax. This coupon is good only for the brand specified. Coupon may be used only one time. Void if copied, exchanged or transferred before acceptance by retailer. Participation in this promotion is at the discretion of the retailer. All promotional costs paid by manufacturer. Limited to one coupon per offer specified on coupon and cannot be combined with any other offers, promotions or discounts. Reynolds Marketing Services Company will reimburse retailer the face value ($ OFF) normal retail price or normal retail price minus the dollar value on the coupon, whichever is applicable to this coupon offer, provided retailer and consumer have complied with the terms of this offer and our coupon redemption policy, incorporated by reference (copies available). Cash value 1/20 of 1¢. Good only at retail locations listed in the store locator.";
					descCouponTerms3 = "Consumer must pay any sales tax.";
					descCouponTerms4 = "This coupon is good only for the brand specified. Coupon may be used only one time. Void if copied, exchanged or transferred before acceptance by retailer. Participation in this promotion is at the discretion of the retailer. All promotional costs paid by manufacturer. Limited to one coupon per offer specified on coupon and cannot be combined with any other offers, promotions or discounts.";
					descCouponTerms5 = "Reynolds Marketing Services Company will reimburse retailer the face value ($ OFF) normal retail price or normal retail price minus the dollar value on the coupon, whichever is applicable to this coupon offer, provided retailer and consumer have complied with the terms of this offer and our coupon redemption policy, incorporated by reference (copies available). Cash value 1/20 of 1¢. Good only at retail locations listed in the store locator.";
				}
				// isElementPresentVerification(CouponHomePageObjects.descFullCouponTerms,"Coupon
				// Home - Coupon Terms description 2");
				// isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms,
				// descCouponTerms3,
				// "Coupon Home - Coupon Terms description 3");
				// isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms,
				// descCouponTerms4,
				// "Coupon Home - Coupon Terms description 4");
				// isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms,
				// descCouponTerms5,
				// "Coupon Home - Coupon Terms description 5");
				isElementPresent(CouponHomePageObjects.lnkCouponTermsReadLess,
						"Coupon Terms - Read Less link");
				// DriverManager.getReportiumClient().stepEnd();
			}
		} catch (Exception e) {
			failTestScript("Failed to validate the elements of Coupon Home Page:", e);
		}
	}

	private void isElementPresent(By lnkCouponTermsReadLess, String string) {
		// TODO Auto-generated method stub

	}

	private void isElementPresent(String descCouponTerms2, String string) {
		// TODO Auto-generated method stub

	}

	@And("^I validate the Velo or Vuse Coupons Home Page$")
	public void iValidateTheVeloOrVuseCouponsHomePage() throws Throwable {
		try {
			// DriverManager.getReportiumClient().stepStart("Step: I validate the Coupons
			// Home Page");
			isElementPresentVerification(CouponHomePageObjects.personalizedCoupon,
					"Coupon Home Page - Personalized Coupon tile");
			if (isElementPresent(CouponHomePageObjects.btn_ChooseStore)) {
				waitUntilElementVisible(CouponHomePageObjects.personalizedCoupon, 30);
				// clickIfElementPresent(CouponHomePage.personalizedCoupon,"Coupon Home Page -
				// Personalized Coupon tile");
				isElementPresentVerification(CouponHomePageObjects.brandImgCouponHomeHeader,
						"Brand icon - Header - Coupon Home Page");
				isElementPresentVerification(CouponHomePageObjects.couponCount, "Coupon Home - Coupon Count icon");
				isElementPresentVerification(CouponHomePageObjects.hamburgerMenu, "Coupon Home - Hamburger menu");
				isElementPresentVerification(CouponHomePageObjects.welcomFirstNameTxt,
						"Coupon Home - Welcome First name text");
				isElementPresentContainsText(CouponHomePageObjects.lblSortBy, "SORT BY:",
						"Coupon Home Page - Sort By label");
				isElementPresentVerification(CouponHomePageObjects.btnSortOffers,
						"Coupon Home Page - Sort Offers Button");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponImage,
						"Coupon Home - Personalized Coupon Offer - Image");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponTileExpiry,
						"Coupon Home - Personalized Coupon - Expiry");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponOfferValue,
						"Coupon Home  - Personalized Coupon Offer Value");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponOfferDesc,
						"Coupon Home  - Personalized Coupon Offer Description");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponLblGood,
						"Coupon Home - Personalized Coupon Offer label - Good On Any Style");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityTxt,
						"Coupon Home - Personalized Coupon Offer - Validity Text - This Coupon offer is valid for");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityRemaining,
						"Coupon Home - Personalized Coupon Offer - Validity Remaining");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityProgressBar,
						"Coupon Home - Personalized Coupon Offer - Validity Progress Bar");
				isElementPresentVerification(CouponHomePageObjects.btn_ChooseStore, "Choose A Store button");
				System.out.println(brand.get());
				isElementPresentContainsText(CouponHomePageObjects.lblCouponTerms, "COUPON TERMS",
						"Choose A Store button");
				isElementPresentContainsText(CouponHomePageObjects.msgCouponTerms, "VOID WHERE PROHIBITED",
						"Choose A Store button");
				String descCouponTerms1;
				System.out.println(env.get());
				if (LoginPageStepDefs.getBrand().contains("NASCIGS"))
					descCouponTerms1 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. Consumer may pay sales tax.";
				else
					descCouponTerms1 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. Consumer may pay sales tax.";
				isElementPresentContainsText(CouponHomePageObjects.descCouponTerms, descCouponTerms1,
						"Coupon Home - Coupon Terms description 1");
				isElementPresentContainsText(CouponHomePageObjects.lnkCouonTermsReadMore, "Read More",
						"Coupon Terms - Read More link");
				clickIfElementPresent(CouponHomePageObjects.lnkCouonTermsReadMore, "Coupon Terms - Read More link");
				String descCouponTerms2;
				String descCouponTerms3;
				String descCouponTerms4;
				String descCouponTerms5;
				if (LoginPageStepDefs.getBrand().contains("NASCIGS")) {
					descCouponTerms2 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. ";
					descCouponTerms3 = "Consumer must pay any sales tax.";
					descCouponTerms4 = "This coupon is good only for the brand specified. Coupon may be used only one time. Void if copied, exchanged or transferred before acceptance by retailer. Participation in this promotion is at the discretion of the retailer. All promotional costs paid by manufacturer. Limited to one coupon per offer specified on coupon and cannot be combined with any other offers, promotions or discounts.";
					descCouponTerms5 = "Reynolds Marketing Services Company will reimburse retailer the face value ($ OFF) normal retail price or normal retail price minus the dollar value on the coupon, whichever is applicable to this coupon offer, provided retailer and consumer have complied with the terms of this offer and our coupon redemption policy, incorporated by reference (copies available). Cash value 1/20 of 1¢. Good only at retail locations listed in the store locator.";

				} else {
					descCouponTerms2 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. ";
					descCouponTerms3 = "Consumer must pay any sales tax.";
					descCouponTerms4 = "This coupon is good only for the brand specified. Coupon may be used only one time. Void if copied, exchanged or transferred before acceptance by retailer. Participation in this promotion is at the discretion of the retailer. All promotional costs paid by manufacturer. Limited to one coupon per offer specified on coupon and cannot be combined with any other offers, promotions or discounts.";
					descCouponTerms5 = "Reynolds Marketing Services Company will reimburse retailer the face value ($ OFF) normal retail price or normal retail price minus the dollar value on the coupon, whichever is applicable to this coupon offer, provided retailer and consumer have complied with the terms of this offer and our coupon redemption policy, incorporated by reference (copies available). Cash value 1/20 of 1¢. Good only at retail locations listed in the store locator.";
				}
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms2,
						"Coupon Home - Coupon Terms description 2");
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms3,
						"Coupon Home - Coupon Terms description 3");
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms4,
						"Coupon Home - Coupon Terms description 4");
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms5,
						"Coupon Home - Coupon Terms description 5");
				isElementPresentContainsText(CouponHomePageObjects.lnkCouponTermsReadLess, "Read Less",
						"Coupon Terms - Read Less link");
				// DriverManager.getReportiumClient().stepEnd();
			} else {
				waitUntilElementVisible(CouponHomePageObjects.personalizedCoupon, 10);
				clickIfElementPresent(CouponHomePageObjects.personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
				isElementPresentVerification(CouponHomePageObjects.brandImgCouponHomeHeader,
						"Brand icon - Header - Coupon Home Page");
				isElementPresentVerification(CouponHomePageObjects.couponCount, "Coupon Home - Coupon Count icon");
				isElementPresentVerification(CouponHomePageObjects.hamburgerMenu, "Coupon Home - Hamburger menu");
				isElementPresentVerification(CouponHomePageObjects.welcomFirstNameTxt,
						"Coupon Home - Welcome First name text");
				isElementPresentContainsText(CouponHomePageObjects.lblSortBy, "SORT BY:",
						"Coupon Home Page - Sort By label");
				isElementPresentVerification(CouponHomePageObjects.btnSortOffers,
						"Coupon Home Page - Sort Offers Button");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponImage,
						"Coupon Home - Personalized Coupon Offer - Image");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponTileExpiry,
						"Coupon Home - Personalized Coupon - Expiry");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponOfferValue,
						"Coupon Home  - Personalized Coupon Offer Value");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponOfferDesc,
						"Coupon Home  - Personalized Coupon Offer Description");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponLblGood,
						"Coupon Home - Personalized Coupon Offer label - Good On Any Style");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityTxt,
						"Coupon Home - Personalized Coupon Offer - Validity Text - This Coupon offer is valid for");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityRemaining,
						"Coupon Home - Personalized Coupon Offer - Validity Remaining");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityProgressBar,
						"Coupon Home - Personalized Coupon Offer - Validity Progress Bar");
				isElementPresentVerification(CouponHomePageObjects.btn_ChooseStore, "Choose A Store button");
				System.out.println(brand.get());
				isElementPresentContainsText(CouponHomePageObjects.lblCouponTerms, "COUPON TERMS",
						"Choose A Store button");
				isElementPresentContainsText(CouponHomePageObjects.msgCouponTerms, "VOID WHERE PROHIBITED",
						"Choose A Store button");
				String descCouponTerms1;
				System.out.println(env.get());
				if (LoginPageStepDefs.getBrand().contains("NASCIGS"))
					descCouponTerms1 = "Offer restricted to eligible age 21+ tobacco consumers. Limited-time offer. Offer is subject to termination or change without notice. VOID WHERE PROHIBITED. Consumer may pay sales tax.";
				else
					descCouponTerms1 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. Consumer may pay sales tax.";
				isElementPresentContainsText(CouponHomePageObjects.descCouponTerms, descCouponTerms1,
						"Coupon Home - Coupon Terms description 1");
				isElementPresentContainsText(CouponHomePageObjects.lnkCouonTermsReadMore, "Read More",
						"Coupon Terms - Read More link");
				clickIfElementPresent(CouponHomePageObjects.lnkCouonTermsReadMore, "Coupon Terms - Read More link");
				String descCouponTerms2;
				String descCouponTerms3;
				String descCouponTerms4;
				String descCouponTerms5;
				if (LoginPageStepDefs.getBrand().contains("NASCIGS")) {
					descCouponTerms2 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice.";
					descCouponTerms3 = "Consumer must pay any sales tax.";
					descCouponTerms4 = "This coupon is good only for the brand specified. Coupon may be used only one time. Void if copied, exchanged or transferred before acceptance by retailer. Participation in this promotion is at the discretion of the retailer. All promotional costs paid by manufacturer. Limited to one coupon per offer specified on coupon and cannot be combined with any other offers, promotions or discounts.";
					descCouponTerms5 = "Reynolds Marketing Services Company will reimburse retailer the face value ($ OFF) normal retail price or normal retail price minus the dollar value on the coupon, whichever is applicable to this coupon offer, provided retailer and consumer have complied with the terms of this offer and our coupon redemption policy, incorporated by reference (copies available). Cash value 1/20 of 1¢. Good only at retail locations listed in the store locator.";

				} else {
					descCouponTerms2 = "Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. ";
					descCouponTerms3 = "Consumer must pay any sales tax.";
					descCouponTerms4 = "This coupon is good only for the brand specified. Coupon may be used only one time. Void if copied, exchanged or transferred before acceptance by retailer. Participation in this promotion is at the discretion of the retailer. All promotional costs paid by manufacturer. Limited to one coupon per offer specified on coupon and cannot be combined with any other offers, promotions or discounts.";
					descCouponTerms5 = "Reynolds Marketing Services Company will reimburse retailer the face value ($ OFF) normal retail price or normal retail price minus the dollar value on the coupon, whichever is applicable to this coupon offer, provided retailer and consumer have complied with the terms of this offer and our coupon redemption policy, incorporated by reference (copies available). Cash value 1/20 of 1¢. Good only at retail locations listed in the store locator.";
				}
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms2,
						"Coupon Home - Coupon Terms description 2");
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms3,
						"Coupon Home - Coupon Terms description 3");
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms4,
						"Coupon Home - Coupon Terms description 4");
				isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms5,
						"Coupon Home - Coupon Terms description 5");
				isElementPresentContainsText(CouponHomePageObjects.lnkCouponTermsReadLess, "Read Less",
						"Coupon Terms - Read Less link");
				// DriverManager.getReportiumClient().stepEnd();
			}
		} catch (Exception e) {
			failTestScript("Failed to validate the elements of Coupon Home Page:", e);
		}
	}

	public String getExpectedSGWText(String sgwq1, String sgwq2, String sgwq3, String sgwq4) {
		String Expected_SGWtext = "";
		Date date = new Date();
		LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		int quarter = localDate.get(IsoFields.QUARTER_OF_YEAR);
		switch (quarter) {
			case 1:
				Expected_SGWtext = sgwq1;
				break;
			case 2:
				Expected_SGWtext = sgwq2;
				break;
			case 3:
				Expected_SGWtext = sgwq3;
				break;
			case 4:
				Expected_SGWtext = sgwq4;
				break;
		}
		return Expected_SGWtext;
	}

	@And("I select a Newport coupon and click on choose a store button")
	public void i_select_a_Newport_coupon_and_click_on_choose_a_store_button() {
		// DriverManager.getReportiumClient().stepStart("Step:The user will select the
		// coupon and click on choose store");
		try {
			if (isElementPresent(CouponHomePageObjects.btn_NpChooseStore)) {

				scrollDownFromMid();
				clickIfElementPresent(CouponHomePageObjects.btn_NpChooseStore,
						"Coupon Home Page - Choose A Store button");
			} else {
				clickIfElementPresent(CouponHomePageObjects.personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
				clickIfElementPresent(CouponHomePageObjects.btn_NpChooseStore,
						"Coupon Home Page - Choose A Store button");
			}
			scrollToBottom(DriverManager.getAppiumDriver());
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to click the Choose a Store button:", e);
		}
	}

	@And("^I validate the Coupons Home Page Tabs$")
	public void i_validate_the_coupons_home_page_tab() throws Throwable {
		try {

			isElementPresentVerification(CouponHomePageObjects.GrizzMoistTab,
					"Grizzly Tab - Header - Coupon Home Page");
			clickIfElementPresent(CouponHomePageObjects.GrizzSnussTab, "Grizzly Tab - Header - Coupon Home Page");
			clickIfElementPresent(CouponHomePageObjects.GrizzNicotineTab, "Grizzly Tab - Header - Coupon Home Page");
			isElementPresentVerification(CouponHomePageObjects.GrizzNicotineTabSGW,
					"Grizzly Tab - Header - Coupon Home Page");

		} catch (Exception e) {
			failTestScript("Validating the Coupons Home Page failed", e);
		}
	}

	@And("^I validate the Newport Coupons Home Page$")
	public void i_validate_the_Newport_coupons_home_page() {
		try {
			// DriverManager.getReportiumClient().stepStart("Step:The user will validate the
			// Newport Coupon Home Page");
			isElementPresentVerification(CouponHomePageObjects.brandImgCouponHomeHeader,
					"Brand icon - Header - Coupon Home Page");
			isElementPresentVerification(CouponHomePageObjects.couponCount, "Coupon Home - Coupon Count icon");
			isElementPresentVerification(CouponHomePageObjects.hamburgerMenu, "Coupon Home - Hamburger menu");
			isElementPresentVerification(CouponHomePageObjects.welcomFirstNameTxt,
					"Coupon Home - Welcome First name text");
			isElementPresentContainsText(CouponHomePageObjects.lblSortBy, "Sort By:",
					"Coupon Home Page - Sort By label");
			isElementPresentVerification(CouponHomePageObjects.btnSortOffers, "Coupon Home Page - Sort Offers Button");
			if (isElementPresent(CouponHomePageObjects.personalizedCoupon)) {
				if (!isElementPresent(CouponHomePageObjects.btn_ChooseStore)) {
					clickIfElementPresent(CouponHomePageObjects.personalizedCoupon,
							"Coupon Home Page - Personalized Coupon tile");
				}
				isElementPresentVerification(CouponHomePageObjects.personalizedCoupon,
						"Coupon Home - Personalized Coupon tile");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponOfferValue,
						"Coupon Home - Personalized Coupon Offer Value");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponOfferDesc,
						"Coupon Home - Personalized Coupon Offer Description");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponLblGood,
						"Coupon Home - Personalized Coupon Label");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponImage,
						"Coupon Home - Personalized Coupon Image");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityTxt,
						"Coupon Home - Personalized Coupon Validty Text - This Coupon offer is valid for ");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityRemaining,
						"Coupon Home - Personalized Coupon Offer - Validity Remaining");
				isElementPresentVerification(CouponHomePageObjects.personalizedCouponValidityProgressBar,
						"Coupon Home - Personalized Coupon Offer - Validity Progress Bar");
				isElementPresentVerification(CouponHomePageObjects.btn_ChooseStore, "Choose A Store button");
			}
			if (isElementPresent(CouponHomePageObjects.np_personalizedCoupon)) {
				if (!isElementPresent(CouponHomePageObjects.btn_NpChooseStore)) {
					clickIfElementPresent(CouponHomePageObjects.np_personalizedCoupon,
							"Coupon Home Page - Personalized Coupon tile");
				}
				isElementPresentVerification(CouponHomePageObjects.np_personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
				isElementPresentVerification(CouponHomePageObjects.np_personalizedCouponOfferValue,
						"Coupon Home - Personalized Coupon Offer Value");
				isElementPresentVerification(CouponHomePageObjects.np_personalizedCouponOfferDesc,
						"Coupon Home  - Personalized Coupon Offer Description");
				isElementPresentVerification(CouponHomePageObjects.np_personalizedCouponValidityTxt,
						"Coupon Home - Personalized Coupon Offer - Validity Text - This Coupon offer is valid for");
				isElementPresentVerification(CouponHomePageObjects.np_personalizedCouponValidityRemaining,
						"Coupon Home - Personalized Coupon Offer - Validity Remaining");
				isElementPresentVerification(CouponHomePageObjects.np_personalizedCouponValidityProgressBar,
						"Coupon Home - Personalized Coupon Offer - Validity Progress Bar");
				isElementPresentVerification(CouponHomePageObjects.btn_NpChooseStore, "Choose A Store button");

			}
			isElementPresentContainsText(CouponHomePageObjects.couponHomeLocationDisclaimer,
					"Offers can only be redeemed at participating stores.", "Coupon Home  - Location Disclaimer Text");

			isElementPresentContainsText(CouponHomePageObjects.lblCouponTerms, "COUPON TERMS", "Choose A Store button");
			isElementPresentContainsText(CouponHomePageObjects.msgCouponTerms, "VOID WHERE PROHIBITED",
					"Choose A Store button");
			String descCouponTerms1 = "Offer restricted to eligible age 21+ tobacco consumers.  Limited-time offer.  Quantities are limited and offer is subject to termination or change without notice.  VOID WHERE PROHIBITED.  Consumer must pay sales tax.";
			isElementPresentContainsText(CouponHomePageObjects.descCouponTerms, descCouponTerms1,
					"Coupon Home - Coupon Terms description 1");
			isElementPresentContainsText(CouponHomePageObjects.lnkCouonTermsReadMore, "Read More",
					"Coupon Terms - Read More link");
			clickIfElementPresent(CouponHomePageObjects.lnkCouonTermsReadMore, "Coupon Terms - Read More link");
			String descCouponTerms2 = "TERMS OF COUPON OFFER:  This coupon is good only for brand and retail locations specified.  Coupon may be used only one time. Void if copied, exchanged or transferred before acceptance by retailer.  Participation in this promotion is at the discretion of the retailer.  All promotional costs paid by manufacturer.  Limited to one coupon per offer specified on coupon and cannot be combined with any other offers, promotions, or discounts.";
			String descCouponTerms3 = "RAI Trade Marketing Services Company will reimburse retailer the face value ($ OFF), normal retail price or normal retail price minus the dollar value on the coupon, whichever is applicable to this coupon offer, provided retailer and consumer have complied with the terms of this offer and our coupon redemption policy, incorporated by reference (copies available).  Cash value 1/20 of 1¢.  Good only at retail locations listed in the store locator.";
			isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms1,
					"Coupon Home - Coupon Terms description 1");
			isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms2,
					"Coupon Home - Coupon Terms description 2");
			isElementPresentContainsText(CouponHomePageObjects.descFullCouponTerms, descCouponTerms3,
					"Coupon Home - Coupon Terms description 3");
			isElementPresentContainsText(CouponHomePageObjects.lnkCouponTermsReadLess, "Read Less",
					"Coupon Terms - Read More link");
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate Coupon home page due to an exception:", e);
		}

	}

	@And("^I click on Logout link$")
	public void i_click_on_logout_link() {
		try {
			// DriverManager.getReportiumClient().stepStart("Step: the user will click on
			// the logout button");
			clickIfElementPresent(CouponHomePageObjects.lnkprofilelogout, "Profile-Logout");
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			System.out.println("Exception while clicking on Logout link: " + e.getMessage());
			failTestScript("Failed to click on Logout link:", e);
		}

	}

	@And("^I navigate to Hamburger menu$")
	public void i_navigate_to_hamburger_menu() throws Throwable {
		try {
			// DriverManager.getReportiumClient().stepStart("Step: The user will navigate to
			// Hamburger menu from Coupon Home Menu");
			waitUntilElementVisible(HomePageObjects.hmenucoupon, 20);
			clickIfElementPresent(HomePageObjects.hmenucoupon, "Home Page - Hamburger Menu");
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			e.printStackTrace();
			// DriverManager.getReportiumClient().reportiumAssert("Exception while
			// navigating to Hamburger Menu",false);
			// DriverManager.getReportiumClient().stepEnd();
			Assert.assertTrue(false, "Navigating to Hamburger Menu");
			attachScreenshotForMobile(false);
			fail("Navigating to Hamburger Menu");
		}
	}

	@And("^I validate the elements in Hamburger Menu$")
	public void i_validate_the_elements_in_camel_hamburger_menu() throws Throwable {
		try {
			// DriverManager.getReportiumClient().stepStart("Step:The user validate the
			// elements in Camel Hamburger Menu");
			isElementPresentVerification(HomePageObjects.hamCouponsLink, "Coupons Link");
			isElementPresentVerification(HomePageObjects.hamBrandLink, "Brand Link");
			isElementPresentVerification(HomePageObjects.hamLogoutLink, "Logout link");
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate the elements in Hamburger Menu: ", e);

		}

	}

	@And("^I click on coupons link$")
	public void i_click_on_camel_coupons_link() throws Throwable {
		try {
			// DriverManager.getReportiumClient().stepStart("Step:The user will click on
			// Camel coupons Link");
			clickIfElementPresent(HomePageObjects.hamCouponsLink, "Coupons link");
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate the default view of coupons ", e);
		}
	}

	@And("^I click on logout link in Hamburger menu$")
	public void i_click_on_logout_link_in_camel_hamburger_menu() throws Throwable {
		try {
			// DriverManager.getReportiumClient().stepStart("Step:The user will click on
			// logout link in camel Hamburger Menu");
			clickIfElementPresent(HomePageObjects.hamLogoutLink, "Logout link-Hamburger Menu");
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate logout link in Hamburger menu ", e);
		}
	}

	@And("^I click on brand link in Hamburger menu$")
	public void i_click_on_brand_link_in_camel_hamburger_menu() throws Throwable {
		try {
			// DriverManager.getReportiumClient().stepStart("Step:The user will click on
			// logout link in camel Hamburger Menu");
			clickIfElementPresent(HomePageObjects.hamBrandLink, "Logout link-Hamburger Menu");
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate logout link in Hamburger menu ", e);
		}
	}

	@Then("^I validate I'm on the brand home page for the brand (.+)$")
	public void i_validate_im_on_the_brand_home_page_for_the_brand(String brandname) {
		try {
			// DriverManager.getReportiumClient().stepStart("Step: I validate I'm on the
			// brand home page for the brand " + brandname);
			brandname = brandname.toLowerCase();
			// if
			// (DriverManager.getAppiumDriver().getCurrentUrl().contains("https://aem-stage."
			// + brandname + ".com/secure.html"))
			// DriverManager.getReportiumClient().reportiumAssert("User is successfully
			// navigated to Home Page on Logout of RBDS", true);
			// else
			// DriverManager.getReportiumClient().reportiumAssert("User is successfully
			// navigated to Home Page on Logout of RBDS", true);
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate user is navigated to the home page ", e);
		}

	}

	@Then("^I validate the Cross product coupon in (.+) coupon home page$")
	public void i_validate_the_cross_product_coupon_in__coupon_home_page(String brand) throws Throwable {
		try {
			if (brand.equals("Camel")) {
				isElementPresentVerification(CouponHomePageObjects.CamelCrossBrandCSnus, "Coupons Link");
				isElementPresentVerification(CouponHomePageObjects.CamelCrossBrandCSnus1, "Coupons Link");
			}
		} catch (Exception e) {
			failTestScript("Validating the cross product failed", e);
		}
	}

}
