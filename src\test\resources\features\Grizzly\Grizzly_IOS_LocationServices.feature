@GrizzlyIOSLocatonInstruction
Feature:  RBDS -Location Screen
As a RJR user, I Validate the  Location setting instruction screen

@GrizzlyIOSLocatonInstructionQA
  Scenario Outline: Validate the Location setting instruction screen
     
   
    Given I launch Setting and select Ask first option in Location
      And I'm on the login page IOS for <Brand> with login <URL>
     When I login with valid user id <Username> and password <Password> for <Brand>
         And I navigate to Grizzly Offers Page
      And I click on Grizzly redeem button
      #And I set the valid device location to <Loc>
      And I click on the Understood button for IOS
      And I Validate Location Disabled screen and click continue
      And I Validate Location Disabled screen2
       And I Validate Location Disabled screen3
       And I Validate Location Disabled screen4
       And I Validate Location Disabled screen5
       And I Validate Location Disabled screen6tryagain & Back to AEMscreen functionality
@GrizzlyIOSLocatonInstruction_QAstage        	
Examples: 
      | Brand          | URL                                          | Username                 | Password  | Loc                                   |
      | GRIZZLY  | https://aem-stage.mygrizzly.com/		              | <EMAIL>       | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 

@GrizzlyIOSLocatonInstruction_Prod
Examples: 
      | Brand  | URL                            | Username                                 | Password  | Loc                                   |
      | GRIZZLY  |  https://www.mygrizzly.com 	 		      | <EMAIL>   | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 
 
 
 @GrizzlyIOSShieldIcon
  Scenario Outline: Validate the  Location setting shield Icon
  
     Given I'm on the login page for <Brand> with login <URL> 
     When I login with valid user id <Username> and password <Password> for <Brand>
       And I navigate to Grizzly Offers Page
      And I click on Grizzly redeem button
      #And I set the valid device location to <Loc>
      #And I click on the Understood button for IOS
      And I Validate Understood screen shield icon
      
      
@GrizzlyIOSShieldIcon_QAstage        	
Examples: 
      | Brand  | URL                                          | Username                 | Password  | Loc                                   |
      | GRIZZLY  |  https://aem-stage.mygrizzly.com/			   | <EMAIL>       | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 

@GrizzlyIOSShieldIcon_Prod
Examples: 
      | Brand  | URL                            | Username                                 | Password  | Loc                                   |
      | GRIZZLY  | https://www.mygrizzly.com 	      | <EMAIL>   | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 
 