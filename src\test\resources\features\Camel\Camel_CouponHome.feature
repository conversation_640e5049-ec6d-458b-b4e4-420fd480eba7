Feature: Camel RBDS - Coupon Home
As an ATC user, I should be able to successfully validate the coupon home page for the RBDS Flow for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment

@CamelValidateCouponHome_QAstage
  Scenario Outline: Validate the RBDS Coupon Home Page and availability of the coupons for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I navigate to Camel Offers Page
      And I click on redeem now button
      And I set the valid device location to <Loc>
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I validate the Coupons Home Page
  
  @CamelCouponHome_QAstage        	
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      #| Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL>  | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Camel | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Camel | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @CamelCouponHome_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env   | 
      | Camel | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD  | 
      | Camel | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD  | 
      | Camel | Sheetz               | 595324   | https://www.camel.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD  | 
      | Camel | Speedway             | 529181   | https://www.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD  | 
  
  @CamelCouponSort_QA
  Scenario Outline: Validate that the user is able to sort the coupons by value and expiration date for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
     Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
     And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Camel Offers Page
     And I click on redeem now button
     And I click on the Understood button
     And I validate I'm on the Coupons Home Page
     Then I validate the default view of the coupons sorted by value
      And I sort the coupons by expiration date and validate the coupons
  
  @CamelCouponSort_QAStage
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Camel | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      #| Camel | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @CamelCouponSort_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env   | 
      | Camel | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD  | 
      | Camel | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD  | 
      | Camel | Sheetz               | 595324   | https://www.camel.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD  | 
      | Camel | Speedway             | 529181   | https://www.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD  | 
  
  @CamelCouponvalidateRestrictions      
  Scenario Outline: Validate the user is displayed with error message when he tries to access the camel coupon from the restricted states location for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Camel Offers Page
     And I click on redeem now button
      And I click on the Understood button
     Then I validate the error popup stating that coupons are not available for restricted location for the brand <Brand>
  
  @CamelCouponvalidateRestrictions_QAStage 
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                               | Env | 
      | Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Camel | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Camel | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL>            | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
  
  @CamelCouponvalidateRestrictions_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                               | Env   | 
      | Camel | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
      | Camel | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
      | Camel | Sheetz               | 595324   | https://www.camel.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
      | Camel | Speedway             | 529181   | https://www.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
  
  @CamelWelcomeCoupon
  Scenario Outline: Validate the user is getting the <Coupontype> coupon for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid username <Username> and password <Password> for the brand <Brand> on env <Env> with RBDS code <RBDScode> for validating <Coupontype> Coupons
     And I click on the Understood button
     Then I validate the <Coupontype> present in the Coupons page
     
  @CamelWelcomeCoupon_QA
  Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username               | Password  | Loc                           | Env   | Coupontype |
      | Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | QA  | Welcome    |
      | Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | QA  | Birthday   |

@CamelWelcomeCoupon_Prod
  Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                           | Env   | Coupontype |
      | Camel | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | Welcome    |
      | Camel | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | Birthday   |

@CamelSnusOnCamelCrossBrand
  Scenario Outline: Validate the CamelSnus crossbranding on Camel Coupon Page - RBDS
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Camel Offers Page
     And I click on redeem now button
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
     #Then I validate that SGW is present for the relevant quarter Q1 <SGW_Q1> Q2 <SGW_Q2> Q3 <SGW_Q3> Q4 <SGW_Q4> with SGW element <Element>
     Then I validate the Cross product coupon in <Brand> coupon home page
		 	
  
  @CamelSnusOnCamelCrossBrand_QA       	
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env |SGW_Q1                                                      | SGW_Q2                                                      | SGW_Q3                                    | SGW_Q4                                                      | Element                                                 | 
      | Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: Smokeless tobacco is addictive. | WARNING: Smokeless tobacco is addictive. | //div[contains(@id, 'lblsgwheader')]/following::span[1] | 
      | Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  |  WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: Smokeless tobacco is addictive. | WARNING: Smokeless tobacco is addictive. | //div[contains(@id, 'lblsgwheader')]/following::span[1] |
      | Camel | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  |  WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: Smokeless tobacco is addictive. | WARNING: Smokeless tobacco is addictive. | //div[contains(@id, 'lblsgwheader')]/following::span[1] |
      | Camel | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL>            | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  |  WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: Smokeless tobacco is addictive. | WARNING: Smokeless tobacco is addictive. | //div[contains(@id, 'lblsgwheader')]/following::span[1] |
  
   @Camel_CopyrightTextinLoginPage
Scenario Outline: Validate the user is able to view the Copyright text in Camel login page 
	 Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
	 And I scroll down and validate copyright text <copyrightText> for <Brand>
	 
	 
	 @Camel_CopyrightTextinLoginPage_QA
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | CAMEL | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | CAMEL | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | CAMEL | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | CAMEL | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
		
	 
	 @Camel_CopyrightTextinLoginPage_Prod
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | CAMEL | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | CAMEL | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | CAMEL | Sheetz               | 595324   | https://www.camel.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | CAMEL | Speedway             | 529181   | https://www.camel.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
	
