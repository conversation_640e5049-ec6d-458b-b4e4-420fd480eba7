Feature: Camel RBDS - Hamburger Menu
As a RJR user, I shall be validating Camel Coupon Hamburger Functionality

@CamelValidateHamburgerMenu
  Scenario Outline: Validate Hamburger Menu And the navigation to Coupon Page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Camel Offers Page
      And I click on redeem now button
       And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I validate the elements in Hamburger Menu
      And I click on coupons link
     Then I validate I'm on the Coupons Home Page  
  
  @CamelValidateHamburgerMenu_QA
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Camel | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Camel | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 |  <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @CamelValidateHamburgerMenu_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
      | Camel | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Camel | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Camel | Sheetz               | 595324   | https://www.camel.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Camel | Speedway             | 529181   | https://www.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @CamelLogoutfromHamburgerMenu
  Scenario Outline: Validate that user can logout from the Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I navigate to Camel Offers Page
      And I click on redeem now button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I click on logout link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @CamelLogoutfromHamburgerMenu_QA    
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      #| Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Camel | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Camel | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL>            | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @CamelLogoutfromHamburgerMenu_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
      | Camel | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Camel | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Camel | Sheetz               | 595324   | https://www.camel.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Camel | Speedway             | 529181   | https://www.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @NavigateCamelMobilesiteHamburgerMenu 
  Scenario Outline: Validate that user can navigate to brand mobile site from the Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
       And I navigate to Camel Offers Page
      And I click on redeem now button
      And I click on the Understood button
      And I navigate to Hamburger menu
      And I click on brand link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @NavigateCamelMobilesiteHamburgerMenu_QA      
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Camel | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Camel | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @NavigateCamelMobilesiteHamburgerMenu_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
      | Camel | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Camel | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Camel | Sheetz               | 595324   | https://www.camel.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Camel | Speedway             | 529181   | https://www.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  
