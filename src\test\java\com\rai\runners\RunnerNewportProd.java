package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Newport", glue = { "com.rai.steps" }, tags = "@NewportCouponHome_Prod or @NewportCouponSort_Prod or @NewportCouponvalidateRestrictions_Prod or "
		+ "@NewportValidateRedeemNotNow_Prod or @NewportValidatefavouriteStore_Prod or @NewportValidateErrorMessageNotnearbyStore_Prod or @NewportMapViewCouponRedemtion_Prod or "
		//+ "@NewportCouponSortByValue_Prod or "
		+ "@NavigateNewportMobilesiteHamburgerMenu_Prod or @NewportLogoutfromHamburgerMenu_Prod or @NewportValidateHamburgerMenu_Prod or "
		//+ "@NewportAccessToBrowserAndDeviceLocation_Prod or "
		+ "@NewportSGWValidations_Prod or "
		+ "@NewportStoreListView_Prod or @NewportStoreListSearchByZip_Prod or @NewportStoreDetailsMapView_Prod or @NewportStoreMapViewbyZip_Prod", monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerNewportProd extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
