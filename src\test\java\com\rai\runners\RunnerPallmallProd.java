package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Pallmall", glue = { "com.rai.steps" }, tags = "@PallmallCouponHome_Prod or @PallmallCouponSort_Prod or @PallmallCouponvalidateRestrictions_Prod or "
		+ "@PallmallValidateRedeemNotNow_Prod or @PallmallValidatefavouriteStore_Prod or @PallmallValidateErrorMessageNotnearbyStore_Prod or @PallmallMapViewCouponRedemtion_Prod or "
		+ "@PallmallCouponSortByValue_Prod or "
		+ "@NavigatePallmallMobilesiteHamburgerMenu_Prod or @PallmallLogoutfromHamburgerMenu_Prod or @PallmallValidateHamburgerMenu_Prod or "
		+ "@PallmallAccessToBrowserAndDeviceLocation_Prod or "
		+ "@PallmallSGWValidations_Prod or "
		+ "@PallmallStoreListView_Prod or @PallmallStoreListSearchByZip_Prod or @PallmallStoreDetailsMapView_Prod or @PallmallStoreMapViewbyZip_Prod", monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

// VeloValidateErrorMessageNotnearbyStore_QA
public class RunnerPallmallProd extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
