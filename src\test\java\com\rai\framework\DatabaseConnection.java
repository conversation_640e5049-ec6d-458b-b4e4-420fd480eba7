package com.rai.framework;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Properties;


public class DatabaseConnection {
	
	private static Properties properties = Settings.getInstance();
	
	public static ResultSet getResultSet(String query) {
		try {
			Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
			String userName = properties.getProperty("DBUsername");
			String password = properties.getProperty("DBPassword");
			String databaseName = properties.getProperty("DatabaseName");
			String serverName = properties.getProperty("ServerName");
			String url = "jdbc:sqlserver://" + serverName + ":1433;databaseName=" + databaseName + ";encrypt=true;trustServerCertificate=true;";
			Connection con = DriverManager.getConnection(url, userName, password);
			Statement s1 = con.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
			ResultSet rs = s1.executeQuery(query);
			return rs;

		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	public static ResultSet getResultSetProd(String query) {
		try {
			Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
			String userName = properties.getProperty("ProdDBUsername");
			String password = properties.getProperty("ProdDBPassword");
			String databaseName = properties.getProperty("ProdDatabaseName");
			String serverName = properties.getProperty("ProdServerName");
			String url = "jdbc:sqlserver://"+serverName+":1433" + ";databaseName="+databaseName;
			Connection con = DriverManager.getConnection(url, userName, password);
			Statement s1 = con.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
			ResultSet rs = s1.executeQuery(query);
			return rs;

		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	
	public static synchronized String getDBColumnValue(ResultSet rs, String columnName) {
		String value = null;
		try {
			if (rs != null) {
				while (rs.next()) {
					value= rs.getString(columnName);
				}
			}
			return value;
		}catch(Exception e) {
			e.printStackTrace();
			return null;
		}
		
	}

}
