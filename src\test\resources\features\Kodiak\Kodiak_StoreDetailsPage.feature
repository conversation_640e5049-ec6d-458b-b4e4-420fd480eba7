Feature: Kodiak RBDS - Store Details
As a RJR user, The user will be able to view the cougar store list by list and Mapview
 
@KodiakStoreListView
  Scenario Outline: Validate that user is able to select a coupons to choose a store and view the store details upto 20 stores for the retailer <Retailer> & brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
     Then I click on loadmore button and validate the list

@KodiakStoreListView_QA         	
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           							| Env | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801						| QA  | 
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601| QA	|
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 11915 Heather Dr, Hagerstown, MD 21740 			| QA 	| 
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 4950 W Pershing Rd, Cicero, IL 60804       	| QA 	| 
  
@KodiakStoreListView_Prod
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						   | Env  | 
      #| Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801	           | Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601 | Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 11915 Heather Dr, Hagerstown, MD 21740	     | Prod | 
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 4950 W Pershing Rd, Cicero, IL 60804     	   | Prod | 
 
@KodiakStoreListSearchByZip  	
   Scenario Outline: Validate the search By Zip and view the available stores in Store list page for the <Brand> brand & the <Retailer> retailer on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And  I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I do a Search By zip with invalid zip code
     Then I validate the error message for invalid zipcode
      And I do a Search By zip with valid zip code <ZipCode>
      And I select a store from store list page
     Then I validate the error message on coupon redemption
     
@KodiakStoreListSearchByZip_QA  
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           							| Env | ZipCode |
      #| Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801						| QA  | 91801   |
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601| QA	| 80601   |
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 11915 Heather Dr, Hagerstown, MD 21740 			| QA 	| 21740   |
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 4950 W Pershing Rd, Cicero, IL 60804       	| QA 	| 60804   |
  
@KodiakStoreListSearchByZip_Prod
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						   | Env  | ZipCode |
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801	           | Prod | 91801   |
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601 | Prod | 80601   |
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 11915 Heather Dr, Hagerstown, MD 21740	     | Prod | 21740   |
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 4950 W Pershing Rd, Cicero, IL 60804     	   | Prod | 60804   |
 
@KodiakStoreDetailsMapView      
   Scenario Outline: Validate the user is able to view the store details in the map view for the <Brand> brand & the <Retailer> retailer on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc> with restricted state
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I select a store from map view
     Then I validate the user is on Redeem now page

@KodiakStoreDetailsMapView_QA
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           							| Env | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801						| QA  | 
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601| QA	|
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 11915 Heather Dr, Hagerstown, MD 21740 			| QA 	| 
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 4950 W Pershing Rd, Cicero, IL 60804       	| QA 	| 
  
@KodiakStoreDetailsMapView_Prod
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						   | Env  | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801	           | Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601 | Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 11915 Heather Dr, Hagerstown, MD 21740	     | Prod | 
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 4950 W Pershing Rd, Cicero, IL 60804     	   | Prod | 
    
@KodiakStoreMapViewbyZip
 	Scenario Outline: Validate the search By Zip and view the available stores in Store Map view page for the <Brand> brand & the <Retailer> retailer on <Env> Environment
     Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc> with restricted state
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I do a Search By zip with invalid zip code
     Then I validate the error message for invalid zipcode
      And I do a Search By zip with valid zip code <ZipCode>
      And I select a store from map view
      #And I click on the Redeem Now button
      And I validate the error message on coupon redemption
     
@KodiakStoreMapViewbyZip_QA
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           							| Env | ZipCode | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801						| QA  | 91801   |
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601| QA	| 80601   |
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 11915 Heather Dr, Hagerstown, MD 21740 			| QA 	| 21740   |
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 4950 W Pershing Rd, Cicero, IL 60804       	| QA 	| 60804   |
  
@KodiakStoreDetailsMapView_Prod
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						   | Env  | ZipCode |
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801	           | Prod | 91801   |
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601 | Prod | 80601   |
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 11915 Heather Dr, Hagerstown, MD 21740	     | Prod | 21740   |
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 4950 W Pershing Rd, Cicero, IL 60804     	   | Prod | 60804   |
 