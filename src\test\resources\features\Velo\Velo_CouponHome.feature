Feature: Velo RBDS - Coupon Home
As an ATC user, I should be able to successfully validate the coupon home page for the RBDS Flow for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment

@VeloValidateCouponHome
  Scenario Outline: Validate the RBDS Coupon Home Page and availability of the coupons for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I validate the Velo or Vuse Coupons Home Page
  
  @VeloCouponHome_QAstage        	
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | VELO  | Speedway             | 529181   | https://aem-stage.velo.com/?RBDSCode=529181 | <EMAIL>          | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @VeloCouponHome_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env   | 
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD  | 
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD  | 
      | Velo  | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD  | 
      | Velo  | Speedway             | 529181   | https://www.velo.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD  | 
  
  @VeloCouponSort
  Scenario Outline: Validate that the user is able to sort the coupons by value and expiration date for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
     Then I validate the default view of the coupons sorted by value
      And I sort the coupons by expiration date and validate the coupons
  
  @VeloCouponSort_QAStage
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | VELO  | Speedway             | 529181   | https://aem-stage.velo.com/?RBDSCode=529181 | <EMAIL>          | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @VeloCouponSort_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env   | 
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD  | 
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD  | 
      | Velo  | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD  | 
      | Velo  | Speedway             | 529181   | https://www.velo.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD  | 
  
  @VeloCouponvalidateRestrictions      
  Scenario Outline: Validate the user is displayed with error message when he tries to access the camel coupon from the restricted states location for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate the error popup stating that coupons are not available for restricted location for the brand <Brand>
  
  @VeloCouponvalidateRestrictions_QAStage 
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                               | Env | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | Washington Ave, Albany, NY 12224, USA | QA  | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 |Washington Ave, Albany, NY 12224, USA | QA  | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>           | Password1 | Washington Ave, Albany, NY 12224, USA | QA  | 
      | VELO  | Speedway             | 529181   | https://aem-stage.velo.com/?RBDSCode=529181 | <EMAIL>           | Password1 | Washington Ave, Albany, NY 12224, USA | QA  | 
  
  @VeloCouponvalidateRestrictions_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                               | Env   | 
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | Washington Ave, Albany, NY 12224, USA | PROD  | 
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | Washington Ave, Albany, NY 12224, USA | PROD  | 
      | Velo  | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 | <EMAIL> | Password1 | Washington Ave, Albany, NY 12224, USA | PROD  | 
      | Velo  | Speedway             | 529181   | https://www.velo.com/?RBDSCode=529181 | <EMAIL> | Password1 | Washington Ave, Albany, NY 12224, USA | PROD  | 
  
  @VeloWelcomeCoupon
  Scenario Outline: Validate the user is getting the <Coupontype> coupon for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid username <Username> and password <Password> for the brand <Brand> on env <Env> with RBDS code <RBDScode> for validating <Coupontype> Coupons
     And I click on the Understood button
     Then I validate the <Coupontype> present in the Coupons page
     
  @VeloWelcomeCoupon_QA
  Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username               | Password  | Loc                           | Env   | Coupontype |
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | Welcome    |
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | Birthday   |

@VeloWelcomeCoupon_Prod
  Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                           | Env   | Coupontype |
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | Welcome    |
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | Birthday   |
      
      
 
   @Velo_CopyrightTextinLoginPage
Scenario Outline: Validate the user is able to view the Copyright text in Velo login page 
	 Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
	 And I scroll down and validate copyright text <copyrightText> for <Brand>
	 
	 
	 @Velo_CopyrightTextinLoginPage_QA
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | VELO | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | VELO | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | VELO | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | VELO | Speedway             | 529181   | https://aem-stage.velo.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
		
	 
	 @Velo_CopyrightTextinLoginPage_Prod
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | VELO | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | VELO | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | VELO | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | VELO | Speedway             | 529181   | https://www.velo.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
	