package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Velo", glue = { "com.rai.steps" }, tags = "@VeloCouponHome_Prod or @VeloCouponSort_Prod or @VeloCouponvalidateRestrictions_Prod or "
		+ "@VeloValidateRedeemNotNow_Prod or @VeloValidatefavouriteStore_Prod or @VeloValidateErrorMessageNotnearbyStore_Prod or @VeloMapViewCouponRedemtion_Prod or "
		+ "@VeloCouponSortByValue_Prod or "
		+ "@NavigateVeloMobilesiteHamburgerMenu_Prod or @VeloLogoutfromHamburgerMenu_Prod or @VeloValidateHamburgerMenu_Prod or "
		+ "@VeloAccessToBrowserAndDeviceLocation_Prod or "
		+ "@VeloSGWValidations_Prod or "
		+ "@VeloStoreListView_Prod or @VeloStoreListSearchByZip_Prod or @VeloStoreDetailsMapView_Prod or @VeloStoreMapViewbyZip_Prod",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

// VeloValidateErrorMessageNotnearbyStore_QA
public class RunnerVeloProd extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
