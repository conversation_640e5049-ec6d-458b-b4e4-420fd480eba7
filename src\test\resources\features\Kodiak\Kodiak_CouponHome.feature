Feature: Kodiak RBDS - Coupon Home
As an ATC user, I should be able to successfully validate the coupon home page for the RBDS Flow for the Brand <Brand> and the Retailer <Retailer>

@KodiakValidateCouponHome
  Scenario Outline: Validate the RBDS Coupon Home Page and availability of the coupons for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I validate the Coupons Home Page
  
  @KodiakCouponHome_QAstage        	      
    Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           						| Env | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 805 W Las Tunas Dr San Gabriel, CA 91776	| QA  | 
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 4951 E Bridge St, Brighton, CO 80601			| QA	|
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 16420 National Pike Hagerstown, MD 21740 	| QA 	| 
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 3200 S Cicero Ave, Cicero, IL 60804       | QA 	| 
  
  @KodiakCouponHome_Prod
    Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						| Env  | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 805 W Las Tunas Dr San Gabriel, CA 91776	| Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 4951 E Bridge St, Brighton, CO 80601    	| Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 16420 National Pike Hagerstown, MD 21740	| Prod | 
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 3200 S Cicero Ave, Cicero, IL 60804     	| Prod | 
  
  @KodiakCouponSort
  Scenario Outline: Validate that the user is able to sort the coupons by value and expiration date for the Brand <Brand> and the Retailer <Retailer> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
     Then I validate the default view of the coupons sorted by value
      And I sort the coupons by expiration date and validate the coupons
  
  @KodiakCouponSort_QAStage
     Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           						| Env | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 805 W Las Tunas Dr San Gabriel, CA 91776	| QA  | 
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 4951 E Bridge St, Brighton, CO 80601			| QA	|
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 16420 National Pike Hagerstown, MD 21740 	| QA 	| 
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 3200 S Cicero Ave, Cicero, IL 60804       | QA 	| 
  
  @KodiakCouponSort_Prod
    Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						| Env  | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 805 W Las Tunas Dr San Gabriel, CA 91776	| Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 4951 E Bridge St, Brighton, CO 80601    	| Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 16420 National Pike Hagerstown, MD 21740	| Prod | 
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 3200 S Cicero Ave, Cicero, IL 60804     	| Prod | 
 
  @KodiakCouponvalidateRestrictions      
  Scenario Outline: Validate the user is displayed with error message when he tries to access the kodiakspirit coupon from the restricted states location for the Brand <Brand> and the Retailer <Retailer> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
       And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
     Then I validate the error popup stating that coupons are not available for restricted location for the brand <Brand>
  
  @KodiakCouponvalidateRestrictions_QAStage 
    Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          				| Username                         | Password  | Loc                               | Env | 
      | Kodiak  | 7-Eleven Corporation | 572896   | https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Kodiak 	| Murphy               | 741592   | https://aem-stage.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Kodiak 	| Sheetz               | 595324   | https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      #| Kodiak 	| Speedway             | 529181   | https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
  
  @KodiakCouponvalidateRestrictions_Prod
    Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           		| Env  | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 2 Ferry St, Newark, NJ 07105, USA	| Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 2 Ferry St, Newark, NJ 07105, USA | Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 2 Ferry St, Newark, NJ 07105, USA	| Prod | 
      #| Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 2 Ferry St, Newark, NJ 07105, USA | Prod | 
 
 @Kodiak_CopyrightTextinLoginPage
Scenario Outline: Validate the user is able to view the Copyright text in Kodiak login page 
	 Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
	 And I scroll down and validate copyright text <copyrightText> for <Brand>
	 
	 
	 @Kodiak_CopyrightTextinLoginPage_QA
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | KODIAK | 7-Eleven Corporation | 572896   | https://aem-stage.kodiakspirit.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | KODIAK | Murphy               | 741592   | https://aem-stage.kodiakspirit.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | KODIAK | Sheetz               | 595324   | https://aem-stage.kodiakspirit.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | KODIAK | Speedway             | 529181   | https://aem-stage.kodiakspirit.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
		
	 
	 @Kodiak_CopyrightTextinLoginPage_Prod
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | KODIAK | 7-Eleven Corporation | 572896   | https://www.kodiakspirit.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | KODIAK | Murphy               | 741592   | https://www.kodiakspirit.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | KODIAK | Sheetz               | 595324   | https://www.kodiakspirit.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | KODIAK | Speedway             | 529181   | https://www.kodiakspirit.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
	