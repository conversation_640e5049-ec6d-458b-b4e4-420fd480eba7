package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Velo", glue = { "com.rai.steps" }, tags = "@VeloValidateContentPostRedemption_QA or "
		+ "@VeloCouponHome_QAstage or @VeloCouponSort_QAStage or  @VeloCouponvalidateRestrictions_QAStage or "
		+ "@VeloRedeemAtAny0.1MileStore_QA or @VeloValidateRedeemNotNow_QA or @VeloValidatefavouriteStore_QA or @VeloValidateErrorMessageNotnearbyStore_QA or @VeloMapViewCouponRedemtion_QA or "
		+ "@VeloValidateHamburgerMenu_QA or @VeloLogoutfromHamburgerMenu_QA or @NavigateVeloMobilesiteHamburgerMenu_QA or "
		+ "@VeloSGWValidations_QA or "
		+ "@VeloStoreListView_QA or @VeloStoreListSearchByZip_QA or @VeloStoreDetailsMapView_QA or @VeloStoreMapViewbyZip_QA",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

// VeloValidateErrorMessageNotnearbyStore_QA
public class RunnerVeloQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
