Feature: Velo RBDS - Content Page Validation
As a RJR user, I will be validating the content page post coupon redemption for the Velo brand for all the valid retailers.

@VeloValidateContentPostRedemption
  Scenario Outline: Validate the user is able to view the content page post redemption of a coupons and user has even more coupons left in RBDS flow for the brand <Brand> and the retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I select a coupon and click on choose a store button
      And I select a store from store list page
      And I click on the Redeem Now button
      And I click on the I'm Done button
      And I Validate the Content Page
      And I validate the tiles on content page for the retailer <Retailer> and for the brand <Brand> with expected Tile1 url <ExpectedTile1Url> and excected Tile2 url <ExpectedTile2Url>
      And I click on back to the coupons and validate the list
  
  @VeloValidateContentPostRedemption_QA         
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                        | Username                         | Password  | Loc                                             | ExpectedTile1Url  | ExpectedTile2Url   | Env | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL>| Password1 | 713 S Main St, King, NC 27021                  |https://aem-stage.velo.com/secure/sms.html | https://aem-stage.velo.com/secure/about.html | QA  | 
  #	  | VELO  | Murphy							 | 741592 	| https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL>	| Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127|https://aem-stage.velo.com/secure/sms.html | https://aem-stage.velo.com/secure/about.html | QA	 |
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      |https://aem-stage.velo.com/secure/sms.html | https://aem-stage.velo.com/secure/about.html | QA  | 
      | VELO  | Speedway             | 529181   | https://aem-stage.velo.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | https://aem-stage.velo.com/secure/sms.html | https://aem-stage.velo.com/secure/about.html | QA  | 

