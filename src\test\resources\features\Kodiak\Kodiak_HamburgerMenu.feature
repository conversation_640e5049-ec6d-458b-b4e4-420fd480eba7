Feature: Kodiak-Hamburger functionality
As an RJR user I should be validating the Kodiak Hamburger elements and navigation functionality

@KodiakValidateHamburgerMenu
Scenario Outline: Validate Hamburger Menu And the navigation to Kodiak Coupon Page for the retailer <Retailer> & the brand <Brand> on <Env> Environment
 		Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I validate the elements in Hamburger Menu
      And I click on coupons link
     Then I validate I'm on the Coupons Home Page

@KodiakValidateHamburgerMenu_QA     
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           							| Env | 
      #| Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801						| QA  | 
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601| QA	|
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 11915 Heather Dr, Hagerstown, MD 21740 			| QA 	| 
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 4950 W Pershing Rd, Cicero, IL 60804       	| QA 	| 
  
@KodiakValidateHamburgerMenu_Prod
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						| Env  | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 805 W Las Tunas Dr San Gabriel, CA 91776	| Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 4951 E Bridge St, Brighton, CO 80601    	| Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 16420 National Pike Hagerstown, MD 21740	| Prod | 
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 3200 S Cicero Ave, Cicero, IL 60804     	| Prod | 
    
@KodiakLogoutfromHamburgerMenu
Scenario Outline: Validate Hamburger Menu And the navigation to Kodiak Coupon Page for the retailer <Retailer> & the brand <Brand> on <Env> Environment
 		Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
      When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I click on logout link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>

@KodiakLogoutfromHamburgerMenu_QA      
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           							| Env | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801						| QA  | 
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601| QA	|
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 11915 Heather Dr, Hagerstown, MD 21740 			| QA 	| 
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 4950 W Pershing Rd, Cicero, IL 60804       	| QA 	| 
  
@KodiakLogoutfromHamburgerMenu_Prod
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						| Env  | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 805 W Las Tunas Dr San Gabriel, CA 91776	| Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 4951 E Bridge St, Brighton, CO 80601    	| Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 16420 National Pike Hagerstown, MD 21740	| Prod | 
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 3200 S Cicero Ave, Cicero, IL 60804     	| Prod | 
 
@KodiakNavigateMobilesiteHamburgerMenu 
Scenario Outline: Validate that user can navigate to Kodiak brand mobile site from the Hamburger menu for the retailer <Retailer> & the brand <Brand> on <Env> Environment
	 Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
      When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I navigate to Hamburger menu
      And I click on brand link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
      
@KodiakNavigateMobilesiteHamburgerMenu_QA     
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           							| Env | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801						| QA  | 
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601| QA	|
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 11915 Heather Dr, Hagerstown, MD 21740 			| QA 	| 
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 4950 W Pershing Rd, Cicero, IL 60804       	| QA 	| 
  
@KodiakNavigateMobilesiteHamburgerMenu_Prod
Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						| Env  | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 805 W Las Tunas Dr San Gabriel, CA 91776	| Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 4951 E Bridge St, Brighton, CO 80601    	| Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 16420 National Pike Hagerstown, MD 21740	| Prod | 
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 3200 S Cicero Ave, Cicero, IL 60804     	| Prod | 
 