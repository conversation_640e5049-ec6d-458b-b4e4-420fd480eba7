@CamelIOSLocatonInstruction
Feature: Camel SPA -Location Screen
As a RJR user, Validate the Camel Location setting instruction screen

@CamelIOSLocatonInstructions
  Scenario Outline: Validate the Camel Location setting instruction screen
     
   
    Given I launch Setting and select Ask first option in Location
      And I'm on the login page IOS for <Brand> with login <URL>
   # Given I'm on the login page IOS for <Brand> with login <URL>
     # And I set the valid device location to <Loc>
     # And I launch Setting and select Ask first option in Location
      
     When I login with valid user id <Username> and password <Password> for <Brand>
      And I navigate to Camel Offers Page
      And I click on claim now button
      #And I set the valid device location to <Loc>
      And I click on the Understood button for IOS
      And I Validate Location Disabled screen and click continue
      And I Validate Location Disabled screen2
       And I Validate Location Disabled screen3
       And I Validate Location Disabled screen4
       And I Validate Location Disabled screen5
       And I Validate Location Disabled screen6tryagain & Back to AEMscreen functionality
@CamelIOSLocatonInstruction_QAstage        	
Examples: 
      | Brand  | URL                                          | Username                 | Password  | Loc                                   |
      | CAMEL  | https://aem-stage.camel.com 		              | <EMAIL>       | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 

@CamelIOSLocatonInstruction_Prod
Examples: 
      | Brand  | URL                            | Username                                 | Password  | Loc                                   |
      | CAMEL  | https://www.camel.com 		      | <EMAIL>   | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 
 
 
 @CamelIOSShieldIcon
  Scenario Outline: Validate the Camel Location setting instruction screen
     
   
    #Given I launch Setting and select Ask first option in Location
      #And I'm on the login page IOS for <Brand> with login <URL>
   # Given I'm on the login page IOS for <Brand> with login <URL>
     # And I set the valid device location to <Loc>
     # And I launch Setting and select Ask first option in Location
   Given   I'm on the login page for <Brand> with login <URL> 
     When I login with valid user id <Username> and password <Password> for <Brand>
      And I navigate to Camel Offers Page
      And I click on claim now button
      #And I set the valid device location to <Loc>
      #And I click on the Understood button for IOS
      And I Validate Understood screen shield icon
      
      
      
      
      
@CamelIOSShieldIcon_QAstage        	
Examples: 
      | Brand  | URL                                          | Username                 | Password  | Loc                                   |
      | CAMEL  | https://aem-stage.camel.com 		              | <EMAIL>       | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 

@CamelIOSShieldIcon_Prod
Examples: 
      | Brand  | URL                            | Username                                 | Password  | Loc                                   |
      | CAMEL  | https://www.camel.com 		      | <EMAIL>   | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 
 