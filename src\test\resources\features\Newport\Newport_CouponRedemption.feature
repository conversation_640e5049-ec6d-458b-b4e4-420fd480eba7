Feature: Newport SPA - Successful Coupon Redemption 
As a RJR user, I should be able to successfully Redeem a coupon at different retailers
    
@NewportRedeemAtAny0.1MileStore
  Scenario Outline: Validate the user is able to redeem the coupon when he is at a distance of .1 miles from the store for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I select a coupon and click on choose a store button
      And I select a store from store list page
      And I click on the Redeem Now button
      And I validate the timer running
      And I click on the I'm Done button
      And I Validate the Content Page

 @NewportRedeemAtAny0.1MileStore_QA 
 Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          						| Username                         | Password  | Loc                                            | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Newport | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
          
@NewportValidateRedeemNotNow
	Scenario Outline: Validate that no coupon is redeemed when the user clicks on the Not Now button on the redemption page for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand>
			Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>    
     	  And I set the valid device location to <Loc>
     	 When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      	And I click on the Understood button
      	And I validate I'm on the Coupons Home Page
      	And I select a coupon and click on choose a store button
      	And I select a store from store list page
      	And I click on the Not Now button
 		   Then I validate I'm on the Coupons Home Page

@NewportValidateRedeemNotNow_QA 		    
 Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          						| Username                         | Password  | Loc                                            | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Newport | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
@NewportValidateRedeemNotNow_Prod
Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          			| Username                         | Password  | Loc                                            | Env  | 
      | Newport | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | Prod | 
      | Newport | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | Prod | 
      | Newport | Sheetz               | 595324   | https://www.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | Prod | 
      | Newport | Speedway             | 529181   | https://www.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | Prod | 
  
@NewportValidatefavouriteStore
  Scenario Outline:Validate the favorite store selection in Redeem Now page and its update in Store list page  for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand>   
      Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>    
     	  And I set the valid device location to <Loc>
     	 When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      	And I click on the Understood button
      	And I validate I'm on the Coupons Home Page
      	And I select a coupon and click on choose a store button
      	And I select a store from store list page
      	And I mark the store as favorite
       Then I navigate to Store List and validate the favoritestore
      	
@NewportValidatefavouriteStore_QA      	
Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          						| Username                         | Password  | Loc                                            | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 5034 Raven Rd, Winston-Salem, NC 27105         | QA  | 
      | Newport | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
@NewportValidatefavouriteStore_Prod
Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          			| Username                         | Password  | Loc                                            | Env  | 
      | Newport | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | Prod | 
      | Newport | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | Prod | 
      | Newport | Sheetz               | 595324   | https://www.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | Prod | 
      | Newport | Speedway             | 529181   | https://www.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | Prod | 
  
@NewportValidateErrorMessageNotnearbyStore
  Scenario Outline: Validate the error message when user tries to redeem a coupon when is not nearby the store
      Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>     
     	  And I set the valid device location to <Loc>
     	 When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand>
      	And I click on the Understood button
      	And I select a coupon and click on choose a store button
      	And I select a store from store list page
      	And I click on the Redeem Now button
      	Then I validate the error message on coupon redemption

 @NewportValidateErrorMessageNotnearbyStore_QA    
 Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          						| Username                         | Password  | Loc                                    | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 365 E Dalton Rd, King, NC 27021        | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3150 Gammon Ln, Clemmons, NC 27012 		| QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 3150 Gammon Ln, Clemmons, NC 27012     | QA  | 
      | Newport | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 108 Cluff Crossing RD Salem,NH 03079   | QA  | 
 
 @NewportValidateErrorMessageNotnearbyStore_Prod
 Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          			| Username                         | Password  | Loc                                    | Env  | 
      | Newport | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 84 Cross St, Salem, NH 03079           | Prod | 
      | Newport | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3150 Gammon Ln, Clemmons, NC 27012 		| Prod | 
      | Newport | Sheetz               | 595324   | https://www.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 3150 Gammon Ln, Clemmons, NC 27012     | Prod | 
      | Newport | Speedway             | 529181   | https://www.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 108 Cluff Crossing RD Salem,NH 03079   | Prod | 
 
@NewportMapViewCouponRedemtion     
   Scenario Outline: Validate the user is displayed with error message when he tries to access the coupon from the restricted states location for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc> with restricted state
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I select a store from map view
      And I click on the Redeem Now button
     Then I validate the error message on coupon redemption
      
 @NewportMapViewCouponRedemtion_QA     
 Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          						| Username                         | Password  | Loc                                            | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 365 E Dalton Rd, King, NC 27021                | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1940 Darwick Rd, Winston-Salem, NC 27127 			| QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2003 Reynolda Rd, Winston-Salem, NC 27106      | QA  | 
      | Newport | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 108 Cluff Crossing RD Salem,NH 03079          	| QA  | 
  
 @NewportMapViewCouponRedemtion_Prod
 Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          			| Username                         | Password  | Loc                                            | Env  | 
      | Newport | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 365 E Dalton Rd, King, NC 27021                | Prod | 
      | Newport | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1940 Darwick Rd, Winston-Salem, NC 27127 			| Prod | 
      | Newport | Sheetz               | 595324   | https://www.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2003 Reynolda Rd, Winston-Salem, NC 27106      | Prod | 
      | Newport | Speedway             | 529181   | https://www.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 108 Cluff Crossing RD Salem,NH 03079          	| Prod | 
  