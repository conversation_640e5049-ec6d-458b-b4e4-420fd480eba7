package com.rai.steps;

import static org.testng.Assert.fail;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import com.rai.framework.DriverManager;
import com.rai.pages.HomePageObjects;
import com.rai.pages.LocationServices;
import com.rai.pages.LoginPageObjects;

import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.remote.SupportsContextSwitching;
import io.cucumber.java.en.*;

public class LoginPageStepDefs extends MasterSteps {
	public String strEnv = "";
	static Logger log = LogManager.getLogger(LocationServicesPageStepDefs.class);
	AppiumDriver driver = DriverManager.getAppiumDriver();
	APIReusuableLibrary apiLibrary = new APIReusuableLibrary();
	private String url;	
	private static ThreadLocal<String> brand = new ThreadLocal<String>();
	private String password;	
	
	public synchronized void setURL(String url) {
		this.url = url;
	}

	public synchronized String getURL() {
		return url;
	}
	
	public static void setBrand(String brand) {
		LoginPageStepDefs.brand.set(brand);
	}

	public static String getBrand() {
		return LoginPageStepDefs.brand.get();
	}
	
	public synchronized void setPassword(String password) {
		this.password = password;
	}
	
	public synchronized String getPassword() {
		return password;
	}
	
//	public static void setLoc(String location) {
//		LoginPageStepDefs.location.set(location);
//	}
//
//	public static String getLoc() {
//		return LoginPageStepDefs.location.get();
//	}

	@Given("^I'm on the login page for (.+) with login (.+)$")
	public void im_on_the_login_page_for_with_login(String brand, String url) throws Throwable {
		try {
			LoginPageStepDefs.setBrand(brand);
			if (url.contains("stage")) 
				strEnv = "QA";			
			else
				strEnv = "PROD";
			DriverManager.getAppiumDriver().manage().deleteAllCookies();
			setURL(url);
			driver.get(url);
			
			  if (driver instanceof IOSDriver) { 
//			clearSafariCache();
//			  clearIOSPrivacyLocation(); 
//			  switchToContext((RemoteWebDriver)driver,"CHROMIUM"); 
			  driver.get(url); }
			
			//DriverManager.getReportiumClient().stepStart("Step:I validate that the mobile site is successfully launched and user is on login page");
			if (driver.findElement(LoginPageObjects.txtBoxUsername).isDisplayed()) {
				addStepLog("Login Page: The login page is successfully launched for the mobilesite " + brand);
				//DriverManager.getReportiumClient().reportiumAssert("Login Page: The login page is successfully launched for the mobilesite " + brand, true);
				//DriverManager.getReportiumClient().stepEnd();
				attachScreenshotForMobile(true);
			} else {
				fail("Login Page: The login page is NOT launched for the mobilesite " + brand);
//				//DriverManager.getReportiumClient().reportiumAssert(
//						"Login Page: The login page is NOT launched for the mobilesite " + brand, false);
				//DriverManager.getReportiumClient().stepEnd();
				attachScreenshotForMobile(false);
			}

		} catch (Exception e) {
			
			failTestScript("Login page loading failed", e);
		}

	}
	
	@And("^I navigate to Grizzly Offers Page$")
	public void i_navigate_to_grizzly_offers_page() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("I navigate to Grizzly Offers Page");
			if (driver instanceof AndroidDriver) {
				/*
				 * if(driver.findElement(HomePageObjects.btn_tobbaccopref).isDisplayed()) {
				 * clickIfElementPresent(HomePageObjects.btn_tobbaccopref,
				 * "Tobacco preferences icon"); }
				 */Thread.sleep(3000);
//				waitUntilElementVisible(HomePageObjects.hamburgerMenu, 20);
				clickIfElementPresent(HomePageObjects.hamburgerMenu, "Home Page - Hamburger Menu");
				Thread.sleep(3000);
//				waitUntilElementVisible(HomePageObjects.lnk_camelCoupon, 30);
				String temp = driver.getCurrentUrl();
				System.out.println(temp);
				String tempArr[] = temp.split(".html");
				temp = tempArr[0] + "/coupons.html";
				driver.get(temp);
				//DriverManager.getReportiumClient().stepEnd();
				Thread.sleep(3000);
			} else if (driver instanceof IOSDriver) {
				if (driver.getCurrentUrl().contains("stage")) {
					driver.get("https://aem-stage.mygrizzly.com/secure/coupons.html");
				} else {
					driver.get("https://www.camel.com/secure/coupons.html");
				}
			}
		} catch (Exception e) {
			
			failTestScript("Navigation to Grizzly offer page failed", e);
		}
	}


	
	@And("^I click on Grizzly redeem button$")
	public void i_click_on_grizzly_redeem_button() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:I click on the mobile offers button in the coupon page");
			
			if(isElementPresent(HomePageObjects.btn_tobbaccopref)) {
				Thread.sleep(6000);
				 clickIfElementPresent(HomePageObjects.btn_tobbaccopref, "Tobacco preferences icon"); 
				 Thread.sleep(3000);
				 System.out.println("Test passed");}
             // WebElement offer=driver.findElement(By.xpath("//a[contains(@class,'mobile-offers')]"));
			 // Actions a2=new Actions(driver);
			 // a2.moveToElement(offer).click().build().perform();
			
			// waitUntilElementVisible(HomePageObjects.btnOffers, 20);
			// clickIfElementPresent(HomePageObjects.btnOffers, "Coupon Page - Mobile Offers Button");			
			// //DriverManager.getReportiumClient().stepEnd();
//			if (strEnv.contains("QA")) {
//				//DriverManager.getReportiumClient().stepEnd();
//				clickIfElementPresent(HomePageObjects.Savings, "Savings");}
		} catch (Exception e) {
			
			failTestScript("click on redeem button failed", e);
		}
	}
	
	@And("^I navigate to Camel Offers Page$")
	public void i_navigate_toCamel_offers_page() throws Throwable {

		try {
			//DriverManager.getReportiumClient().stepStart("I navigate to Camel Offers Page");
			if (driver instanceof AndroidDriver) {
				if (isElementPresent(HomePageObjects.btn_tobbaccopref)) {
					clickIfElementPresent(HomePageObjects.btn_tobbaccopref, "Tobacco preferences icon");
				}
				
				
				Thread.sleep(5000);
////				waitUntilElementVisible(HomePageObjects.hamburgerMenu, 20);
//				System.out.println("clicking on HB1");
//			WebElement ele =	driver.findElement(By.xpath("//*[contains(@class, 'hamburger-wrapper hamburger')]"));
//			Actions action = new Actions(driver);
//			action.moveToElement(ele).build().perform();
//			System.out.println("clicking on HB2");
//
//			Thread.sleep(3000);
//			ele.click();
////			JavascriptExecutor executor = (JavascriptExecutor)driver;
////			executor.executeScript("arguments[0].click();", ele);
//			System.out.println("clicking on HB3");
				if (isElementPresent(HomePageObjects.hamburgerMenu)) {
				clickIfElementPresent(HomePageObjects.hamburgerMenu, "Home Page - Hamburger Menu");
				
//				waitUntilElementVisible(HomePageObjects.lnk_camelCoupon, 30);
//				clickIfElementPresent(HomePageObjects.lnk_camelCoupon, "Home Page - Get a Coupon link");
				if (isElementPresent(HomePageObjects.btn_tobbaccopref2)) {
					clickIfElementPresent(HomePageObjects.btn_tobbaccopref2, "Tobacco preferences icon");
				}
				if (isElementPresent(HomePageObjects.btn_tobbaccopref3)) {
					clickIfElementPresent(HomePageObjects.btn_tobbaccopref3, "Tobacco preferences icon");
				}
				if (isElementPresent(HomePageObjects.btn_tobbaccopref)) {
					clickIfElementPresent(HomePageObjects.btn_tobbaccopref, "Tobacco preferences icon");
				}
				if (isElementPresent(HomePageObjects.btn_tobbaccopref)) {
					clickIfElementPresent(HomePageObjects.btn_tobbaccopref, "Tobacco preferences icon");
				}
				if (driver.getCurrentUrl().contains("stage")) {
					driver.get("https://aem-stage.camel.com/secure/coupons.html");
				} else {
					driver.get("https://www.camel.com/secure/coupons.html");
				}
				}
				if(isElementPresent(HomePageObjects.StoreLocator)) {
					clickIfElementPresent(HomePageObjects.hamburgerMenu, "Home Page - Hamburger Menu");
//				waitUntilElementVisible(HomePageObjects.lnk_camelCoupon, 30);
				clickIfElementPresent(HomePageObjects.lnk_camelCoupon, "Home Page - Get a Coupon link");
				
				}
				//DriverManager.getReportiumClient().stepEnd();
			} else if (driver instanceof IOSDriver) {
				if (driver.getCurrentUrl().contains("stage")) {
					driver.get("https://aem-stage.camel.com/secure/coupons.html");
				} else {
					driver.get("https://www.camel.com/secure/coupons.html");
				}
			}
			
			} catch (Exception e) {
			
			failTestScript("Navigation to Camel offer page failed", e);
		}
	
	}


	@And("^I navigate to Newport Offers Page$")
	public void i_navigate_to_Newport_offers_page() throws Throwable {
		//DriverManager.getReportiumClient().stepStart("I navigate to Newport Offers Page");
		try {
			if (driver instanceof AndroidDriver) {
				Thread.sleep(6000);
				
				waitUntilElementVisible(HomePageObjects.hamburgerMenu, 20);
				clickIfElementPresent(HomePageObjects.hamburgerMenu, "Home Page - Hamburger Menu");
				waitUntilElementVisible(HomePageObjects.lnk_camelCoupon, 10);
				clickIfElementPresent(HomePageObjects.lnk_camelCoupon, "Home Page - Get a Coupon link");}
				//DriverManager.getReportiumClient().stepEnd();
			else if (driver instanceof IOSDriver) {
				if (driver.getCurrentUrl().contains("stage")) {
					driver.get("https://aem-stage.newport-pleasure.com/secure/coupons.html");
				} else {
					driver.get("https://www.newport-pleasure.com/secure/coupons.html");
				}
			}
		} catch (Exception e) {			
			failTestScript("Navigation to Newport offer page failed", e);
		}
	}

	
	@Given("^I'm on the login page IOS for (.+) with login (.+)$")
	public void im_on_the_login_page_IOS_for_with_login(String brand, String url) throws Throwable {
		try {
			  
			LoginPageStepDefs.setBrand(brand);
			if (url.contains("stage")) 
				strEnv = "QA";			
			else
				strEnv = "PROD";
			
			setURL(url);
		//WebDriver driver=new SafariDriver();
		((IOSDriver)driver).activateApp("com.apple.mobilesafari");
		System.out.println(((SupportsContextSwitching)driver).getContextHandles());
		
		Set<String> contextHandles = ((SupportsContextSwitching)driver).getContextHandles();
		String webviewContext = null;

		for (String contextHandle : contextHandles) {
		    if (contextHandle.contains("WEBVIEW")) {
		        webviewContext = contextHandle;
		        break;
		    }
		}

		if (webviewContext != null) {
		    ((SupportsContextSwitching)driver).context(webviewContext);
		    // Now you can use driver.get(url) to launch the URL in the webview
		    driver.get(url);
		} else {
		    // Handle the case when webview context is not found
		}

		//driver.context("WEBVIEW_1");
			
			

			
			//DriverManager.getReportiumClient().stepStart("Step:I validate that the mobile site is successfully launched and user is on login page");
			if (driver.findElement(LoginPageObjects.txtBoxUsername).isDisplayed()) {
				addStepLog("Login Page: The login page is successfully launched for the mobilesite " + brand);
				//DriverManager.getReportiumClient().reportiumAssert("Login Page: The login page is successfully launched for the mobilesite " + brand, true);
				//DriverManager.getReportiumClient().stepEnd();
				//attachScreenshotForMobile(true);
				
			} else {
				fail("Login Page: The login page is NOT launched for the mobilesite " + brand);
//				//DriverManager.getReportiumClient().reportiumAssert(
//						"Login Page: The login page is NOT launched for the mobilesite " + brand, false);
				//DriverManager.getReportiumClient().stepEnd();
				//attachScreenshotForMobile(false);
			}

		} catch (Exception e) {
			
			failTestScript("Login page loading failed", e);
		}

	}

	
	@Given("^I am on the login page for (.+) with login (.+) for the retailer (.+) with RBDS code (.+)$")
	public void i_am_on_the_login_page_for_with_login_for_the_retailer_with_rbds_code(String brand, String url,
			String retailer, String rbdscode) {
		try {
			System.out.println("URL:" + url);
			setURL(url);
			setBrand(brand);
		//	if (driver instanceof AndroidDriver) {
				//clearChromeCookies();
			//	if (DriverManager.getTestParameters().getScenario().getName().contains("content"))
				//	allowBrowserPopUp();
		//	}
			if (driver instanceof IOSDriver) {
				clearSafariCache();
				clearIOSPrivacyLocation();
				switchToContext((RemoteWebDriver) driver, "WEBVIEW_1");
			}
			driver.get(url);
			if(brand.contains("VELO")) {
			if (driver instanceof AndroidDriver)
				((AndroidDriver) driver).context("NATIVE_APP");
			else
				((IOSDriver) driver).context("NATIVE_APP");

//			if (isElementPresent(By.xpath("//*[@resource-id='com.android.chrome:id/username']"))) {
//				// if(driver.getCurrentUrl().contains("stage")) {
//				clearAndEnterText((HomePageObjects.veloLogin_User), "bat-reviewer", "Velo UserName");
//				clearAndEnterText((HomePageObjects.veloLogin_Pass), "bAT!+4sa7ya", "Velo Password");
//				clickIfElementPresent((HomePageObjects.velo_Login), "Velo Login Button");
//			}
			if (driver instanceof AndroidDriver) {
				
				((AndroidDriver) driver).context("CHROMIUM");}
			else
				((IOSDriver) driver).context("WEBVIEW_1");
			Thread.sleep(5000);
			((JavascriptExecutor) driver) .executeScript("window.stop();");
			}
			
			//DriverManager.getReportiumClient().stepStart(
//					"Step:I validate that the RBDS site is successfully launched and user is on login page for the retailer: "
//							+ retailer);
			waitUntilElementVisible(LoginPageObjects.txtBoxUsername, 60);
			if (driver.findElement(LoginPageObjects.txtBoxUsername).isDisplayed()) {
				addStepLog("Login Page: The login page is successfully launched for the mobilesite " + brand);
				//DriverManager.getReportiumClient().reportiumAssert(
//						"Login Page: The login page is successfully launched for the mobilesite " + brand, true);
				//DriverManager.getReportiumClient().stepEnd();
				attachScreenshotForMobile(true);
			} else {
				//DriverManager.getReportiumClient().reportiumAssert(
//						"Login Page: The login page is NOT launched for the mobilesite " + brand, false);
				//DriverManager.getReportiumClient().stepEnd();
				attachScreenshotForMobile(false);
				fail("Login Page: The login page is NOT launched for the mobilesite " + brand);
			}
		} catch (Exception e) {
			failTestScript("Login Page: The login page is NOT launched for the mobilesite for the brand:" + brand, e);
		}

	}

	@Given("^I'm on the Vuse login page with login (.+)$")
	public void im_on_the_Vuse_login_page_for_with_login(String url) {
		try {
			driver.get(url);
			//DriverManager.getReportiumClient().stepStart(
//					"Step:I validate that the mobile site is successfully launched and user is on Vuse login page");
			if (driver.findElement(LoginPageObjects.vusetxtBoxUsername).isDisplayed()) {
				addStepLog("Login Page: The login page is successfully launched for the mobilesite ");
				//DriverManager.getReportiumClient().reportiumAssert(
//						"Login Page: The login page is successfully launched for the mobilesite ", true);
				//DriverManager.getReportiumClient().stepEnd();
				attachScreenshotForMobile(true);
			} else {
				//DriverManager.getReportiumClient()
//						.reportiumAssert("Login Page: The login page is NOT launched for the mobilesite ", false);
				//DriverManager.getReportiumClient().stepEnd();
				attachScreenshotForMobile(false);
				fail("Login Page: The login page is NOT launched for the mobilesite ");
			}
		} catch (Exception e) {
			failTestScript("Login Page: The login page is NOT launched for the mobilesite for the Vuse", e);
		}

	}

	@Given("^I'm on the Velo login page with login (.+)$")
	public void im_on_the_Velo_login_page_for_with_login(String url) {
		try {
			driver.get(url);
			if (driver instanceof AndroidDriver)
				((AndroidDriver) driver).context("NATIVE_APP");
			else
				((IOSDriver) driver).context("NATIVE_APP");
			if (isElementPresent(By.xpath("//*[@resource-id='com.android.chrome:id/username']"))) {
				clearAndEnterText((HomePageObjects.veloLogin_User), "bat-reviewer", "Velo UserName");
				clearAndEnterText((HomePageObjects.veloLogin_Pass), "bAT!+4sa7ya", "Velo Password");
				clickIfElementPresent((HomePageObjects.velo_Login), "Velo Login Button");
			}
			if (driver instanceof AndroidDriver)
				((AndroidDriver) driver).context("CHROMIUM");
			else
				((IOSDriver) driver).context("WEBVIEW_1");
			//DriverManager.getReportiumClient().stepStart(
//					"Step:I validate that the mobile site is successfully launched and user is on Vuse login page");

			if (driver.findElement(HomePageObjects.veloLogin_UserId).isDisplayed()) {
				addStepLog("Login Page: The login page is successfully launched for the Velo mobilesite ");
				//DriverManager.getReportiumClient().reportiumAssert(
//						"Login Page: The login page is successfully launched for the mobilesite ", true);
				//DriverManager.getReportiumClient().stepEnd();
				attachScreenshotForMobile(true);
			} else {
				//DriverManager.getReportiumClient()
//						.reportiumAssert("Login Page: The login page is NOT launched for the mobilesite ", false);
				//DriverManager.getReportiumClient().stepEnd();
				attachScreenshotForMobile(false);
				fail("Login Page: The login page is NOT launched for the Velo mobilesite ");
			}

		} catch (Exception e) {
			failTestScript("Login Page: The login page is NOT launched for the Velo mobilesite", e);
		}

	}

	@And("^I click on redeem now button$")
	public void i_click_on_redeem_now_button() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:I click on the redeem now button in the coupon page");
			//String brand="CAMELSNUS";
			
			if(brand.get().contains("CAMELSNUS")) {
				
				if (strEnv.contains("QA")) {
					
					
					waitUntilElementVisible(HomePageObjects.lnk_redeemnowcamelsnus, 30);
					clickIfElementPresent(HomePageObjects.lnk_redeemnowcamelsnus, "Coupon Page - Redeem Now Button");	
					
				} else if(strEnv.contains("PROD")) {
					waitUntilElementVisible(HomePageObjects.lnk_redeemnow, 10);
					clickIfElementPresent(HomePageObjects.lnk_redeemnow, "Coupon Page - Redeem Now Button");}
					
					
				}
				else if(brand.get().equalsIgnoreCase("CAMEL")) {
					Thread.sleep(2000);
//					 if(driver.getCurrentUrl().contains("stage")) {
//						 System.out.println(isElementPresent(HomePageObjects.lnk_redeemnow));
						 if (isElementPresent(HomePageObjects.lnk_redeemnow)) {
						List<WebElement> e= driver.findElements(By.xpath("//button/span[text()='Redeem Now']"));
						e.get(1).click();}
//				 }else {
//						List<MobileElement> e= driver.findElements(By.xpath("//button/span[text()='Redeem Now']"));
//						e.get(1).click();
//				 }
				}
				else {
					Thread.sleep(3000);
					if (isElementPresent(HomePageObjects.btn_tobbaccopref2)) {
			clickIfElementPresent(HomePageObjects.lnk_redeemnow, "Coupon Page - Redeem Now Button");}}
			// if (strEnv.contains("QA")) {
				
			// 	waitUntilElementVisible(HomePageObjects.lnk_redeemnowcamelsnus, 30);
			// 	clickIfElementPresent(HomePageObjects.lnk_redeemnowcamelsnus, "Coupon Page - Redeem Now Button");	
				
			// }
//			if (strEnv.contains("QA")) {
//			//DriverManager.getReportiumClient().stepEnd();
//			clickIfElementPresent(HomePageObjects.Savings, "Savings");}
			Thread.sleep(3000);
		} catch (Exception e) {
			
			failTestScript("Clicking on redeem now button failed", e);
		}

	}
	
	@When("^The user login with valid UserId (.+) and Password (.+) for the brand (.+) env (.+) and for the retailer (.+) with RBDS code (.+)$")
	public void the_user_login_with_valid_userid_and_password_for_the_brand_env_and_for_the_retailer_with_rbds_code(
			String username, String password, String brand, String env, String retailer, String rbdscode) {
		try {
			//DriverManager.getReportiumClient().stepStart("I login with valid user id and password");
			boolean isLoggedIn=true;
			MasterSteps.env.set(env);
			MasterSteps.brand.set(brand);
			do {
				isLoggedIn=true;
				if (env.contains("PROD")) {
					//username = apiLibrary.getuserIdWithOffers(brand, rbdscode);
				//else if(brand.contains("NASCIGS"))
//					username = "<EMAIL>";}
//				else	{
	//				username = apiLibrary.getuserIdWithOffersprod(brand, rbdscode);
					username = "<EMAIL>";}
//				if(brand.contains("Vuse")){
//					clearAndEnterText(LoginPageObjects.txtBoxUsername_vuse, username, "Login Page - Vuse Username text box");
//					clearAndEnterText(LoginPageObjects.txtBoxPassword_vuse, password, "Login Page - Vuse Password text box");
//				}
//				else{
//				if(brand.contains("Velo"))
//					VeloLogin(username,password);
//				else
//				{
				System.out.println("username "+username);
				clearAndEnterText(LoginPageObjects.txtBoxUsername, username, "Login Page - Username text box");
				clearAndEnterText(LoginPageObjects.txtBoxPassword, password, "Login Page - Password text box");
				//clickIfElementPresent(LoginPageObjects.btnLogin, "Login Page - Login button");
//				}
//				if(getBrand().contains("VELO")){
//					Thread.sleep(10);
//					waitUntilPageReadyStateComplete(10);
//					driver.get(getURL());
//				}
				if (isElementPresent(LoginPageObjects.noOffersAvailable)) {
					clickIfElementPresent(LoginPageObjects.btnGoBackToHomePage, "Go Back To Home Page button");
					waitUntilPageReadyStateComplete(10);
					driver.manage().deleteAllCookies();
					driver.navigate().to(getURL());
					isLoggedIn=false;
				}
			} while (!isLoggedIn);
			waitUntilPageReadyStateComplete(30);
			
			
	        // switchToContext(driver, "NATIVE_APP");
	       //  Thread.sleep(3000);
	         
	         
	         
	      //   if (DriverManager.getAppiumDriver() instanceof AndroidDriver) {
			////		((AndroidDriver) DriverManager.getAppiumDriver()).context("NATIVE_APP");

//					if (isElementPresent(By.xpath(
//							"//*[@resource-id='com.android.permissioncontroller:id/permission_allow_foreground_only_button']")))
//						clickIfElementPresent(LocationServices.btnAllowOnly, "Allow Only While Using This App");
			
			//		if (isElementPresent(By.xpath("//android.widget.Button[@resource-id=\"com.android.chrome:id/negative_button\"]")));
					
				//	clickIfElementPresent(LocationServices.btnblock, "Allow Only While Using This App");
//			
				
				//id(block)=com.android.chrome:id/negative_button
				
			//	}
				
				
				

//				if (isElementPresent(By.id("com.android.chrome:id/nrgative_button"))) {
	//				clickIfElementPresent(LocationServices.btnblock, "Allow Device Location Setting Button");

		//		}
	         
//			MobileElement element = (MobileElement) DriverManager.getAppiumDriver().findElement((By.xpath("//android.widget.Button[@resource-id=\"com.android.chrome:id/negative_button\"]")));
//		WebElement ob = driver.findElement(By.xpath("//android.widget.Button[@resource-id=\"com.android.chrome:id/negative_button\"]"));		
//		JavascriptExecutor executor = (JavascriptExecutor)driver;
//		executor.executeScript("arguments[0].click();", element);
		
		//if (isElementPresent(By.xpath("//*[@label='Block While Using App'] | //*[@label='Block']"))) {
			//clickIfElementPresent(LocationServices.alertAllowwhileusingappButton, "Alert Block Button");
		
	         {
		
//		if (isElementPresent(By.xpath("//android.widget.Button[@resource-id=\"com.android.chrome:id/negative_button\"]")));
//			
//			clickIfElementPresent(LocationServices.btnblock, "Allow Only While Using This App");
	

			
			// waitUntilElementVisible(LocationServices.lnkBack, 60);			
			// closeSavePasswordPopUp();
			//DriverManager.getReportiumClient().stepEnd();
		}} catch (Exception e) {
			failTestScript("Failed to login to the application", e);
		}
		}
	
	private void VeloLogin(String username, String password) {
		clearAndEnterText(LoginPageObjects.velousername, username, "Velo Login Page - Username text box");
		clearAndEnterText(LoginPageObjects.velopassword, password, "Velo Login Page - Password text box");
		clickIfElementPresent(LoginPageObjects.btnLoginprd, "Login Page Velo - Login button");
	}

	@When("^The user login with valid username (.+) and password (.+) for the brand (.+) on env (.+) with RBDS code (.+) for validating (.+) Coupons$")
    public void theUserLoginWithValidUsernameAndPasswordForTheBrandOnEnvWithRBDSCodeForValidatingCoupons(String username, String password, String brand, String env, String rbdscode, String coupontype) throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("I login with valid user id and password");
			boolean isLoggedIn=true;
//			do {
//				isLoggedIn=true;
//				if (env.contains("QA"))
//					username = apiLibrary.getuserIdWithOffers(brand, rbdscode, coupontype);
//				else
				username = apiLibrary.getuserIdWithOffersprod(brand, rbdscode, coupontype);
				clearAndEnterText(LoginPageObjects.txtBoxUsername, username, "Login Page - Username text box");
				clearAndEnterText(LoginPageObjects.txtBoxPassword, password, "Login Page - Password text box");
				clickIfElementPresent(LoginPageObjects.btnLogin, "Login Page - Login button");
				if(getBrand().contains("VELO")){
					Thread.sleep(10);
					waitUntilPageReadyStateComplete(10);
					driver.get(getURL());
				}
				if (isElementPresent(LoginPageObjects.noOffersAvailable)) {
					clickIfElementPresent(LoginPageObjects.btnGoBackToHomePage, "Go Back To Home Page button");
					waitUntilPageReadyStateComplete(10);
					driver.manage().deleteAllCookies();
					driver.navigate().to(getURL());
					isLoggedIn=false;
				}
			// while (!isLoggedIn);
			waitUntilPageReadyStateComplete(30);
			waitUntilElementVisible(LocationServices.lnkBack, 60);			
			closeSavePasswordPopUp();
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to login to the application", e);
		}
	}
	
	@When("^The user login with existing username (.+) and password (.+) for the brand (.+) on env (.+) with RBDS code (.+) for validating (.+) Coupons$")
    public void theUserLoginWithExistingUsernameAndPasswordForTheBrandOnEnvWithRBDSCodeForValidatingCoupons(String username, String password, String brand, String env, String rbdscode, String coupontype) throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("I login with valid user id and password");
			MasterSteps.brand.set(brand);
			boolean isLoggedIn=true;
			do {
				isLoggedIn=true;
				clearAndEnterText(LoginPageObjects.txtBoxUsername, username, "Login Page - Username text box");
				clearAndEnterText(LoginPageObjects.txtBoxPassword, password, "Login Page - Password text box");
				clickIfElementPresent(LoginPageObjects.btnLogin, "Login Page - Login button");
				if(getBrand().contains("VELO")){
					Thread.sleep(10);
					waitUntilPageReadyStateComplete(10);
					driver.get(getURL());
				}
				if (isElementPresent(LoginPageObjects.noOffersAvailable)) {
					clickIfElementPresent(LoginPageObjects.btnGoBackToHomePage, "Go Back To Home Page button");
					waitUntilPageReadyStateComplete(10);
					driver.manage().deleteAllCookies();
					driver.navigate().to(getURL());
					isLoggedIn=false;
				}
			} while (!isLoggedIn);
			waitUntilPageReadyStateComplete(30);
			waitUntilElementVisible(LocationServices.lnkBack, 60);			
			closeSavePasswordPopUp();
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to login to the application", e);
		}
	}

	@And("^I login with valid vuse user id (.+) and password (.+)$")
	public void i_login_with_valid_vuse_user_id_and_password(String username, String password) {
		try {
			//DriverManager.getReportiumClient().stepStart("I login with valid user id and password");
			clearAndEnterText(LoginPageObjects.vusetxtBoxUsername, username, "Login Page - Username text box");
			clearAndEnterText(LoginPageObjects.vusetxtBoxPassword, password, "Login Page - Password text box");
			clickIfElementPresent(LoginPageObjects.vusebtnLogin, "Login Page - Login button");
			//DriverManager.getReportiumClient().stepEnd();
			closeSavePasswordPopUp();
		} catch (Exception e) {
			failTestScript("Failed to login to the application", e);
		}
	}

	@And("^I login with valid Velo user id (.+) and password (.+)$")
	public void i_login_with_valid_velo_user_id_and_password(String username, String password) {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:I login with velo valid user id and password");
			clearAndEnterText(HomePageObjects.veloLogin_UserId, username, "Login Page - Username text box");
			clearAndEnterText(HomePageObjects.veloLogin_Password, password, "Login Page - Password text box");
			clickIfElementPresent(HomePageObjects.velologin_Login, "Login Page - Login button");
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to login to the application", e);
		}
	}

	@And("^I close the save password alert$")
	public void i_close_the_save_password_alert() throws IOException {
		//DriverManager.getReportiumClient().stepStart("Step:The user will close the save password alert");
		closeSavePasswordPopUp();
		//DriverManager.getReportiumClient().stepEnd();
	}

	@And("^I validate the Login page Elements of Cougar$")
	public void i_validate_the_login_page_elements_of_cougar() {
		try {
			//DriverManager.getReportiumClient()
//					.stepStart("Step: The user will verify the Web elements in the Login page");
			isTextPresent("Already have an account? Your email is your username");
			isTextPresent("Forgot your Username or Password? | Not Registered?");
			isElementPresentVerification(LoginPageObjects.Chkbx_Rememberme, "Remember me Check box");
			isElementPresentVerification(LoginPageObjects.txtRegister, "Register");
			isElementPresentVerification(LoginPageObjects.lnk_Contactus, "Contact us");
			isElementPresentVerification(LoginPageObjects.lnk_Faq, "FAQ");
			isElementPresentVerification(LoginPageObjects.lnk_policyrights, "Policy rights");
			isElementPresentVerification(LoginPageObjects.lnk_TobaccoRights, "Tobacco rights");
			isElementPresentVerification(LoginPageObjects.lnk_SiteRequirements, "Site Requiremnts");
			//DriverManager.getReportiumClient().stepEnd();
			attachScreenshotForMobile();
		} catch (Exception e) {
			failTestScript("Failed to validate the login page", e);
		}
	}

	@And("^I validate the remaining SGW elements in the NAS SPA pages$")
	public void i_validate_the_remaining_sgw_elements_in_the_nas_spa_pages() {
		try {
			//DriverManager.getReportiumClient()
//					.stepStart("Step:I validate the remaining SGW elements in the NAS SPA pages");
			String sgw1 = "Organic tobacco does NOT mean a safer cigarette.";
			String sgw2 = "Natural American Spirit cigarettes are not safer than other cigarettes.";
			String actualImageSgw1 = extractImageTextUsingScreenShotOfElement(HomePageObjects.nas_SGW1);
			waitFor(1000);
			actualImageSgw1 = actualImageSgw1.replace("\n", " ");
			compareStringsContains(actualImageSgw1, sgw1);
			String actualImagesgw2 = extractImageTextUsingScreenShotOfElement(HomePageObjects.nas_SGW2);
			waitFor(1000);
			actualImagesgw2 = actualImagesgw2.replace("\n", " ");
			compareStringsContains(actualImagesgw2, sgw2);
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate  the SGW elements in NAS SPA:", e);
		}

	}
	@And("^I navigate to Luckystrike Offers Page$")
	public void i_navigate_to_luckystrike_offers_page() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("I navigate to Luckystrike Offers Page");
			//waitUntilElementVisible(HomePageObjects.hamburgerMenu, 20);
			//clickIfElementPresent(HomePageObjects.hamburgerMenu, "Home Page - Hamburger Menu");
			System.out.println("hamburger menu");
			//waitUntilElementVisible(HomePageObjects.lnk_camelCoupon, 30);
			//clickIfElementPresent(HomePageObjects.lnk_camelCoupon, "Home Page - Get a Coupon link");
			//System.out.println("coupon");
			// coupon option not Available so commenting this line.
			String currentURL = driver.getCurrentUrl();
			if (driver instanceof IOSDriver) {
				 if(currentURL.contains("stage")) {
					 driver.navigate().to("https://aem-stage.luckystrike.com/secure/coupons.html");
				 }else {
					 driver.navigate().to("https://www.luckystrike.com/secure/coupons.html");
					 
				 }
			} else {
				
				if(currentURL.contains("stage")) {
					 driver.navigate().to("https://aem-stage.luckystrike.com/secure/coupons.html");
				 }else {
					 driver.navigate().to("https://www.luckystrike.com/secure/coupons.html");
					 
				 }}
			addStepLog("The user click on Coupons from header.");
			//DriverManager.getReportiumClient().reportiumAssert("The user click on Coupons from header ", true);
			//DriverManager.getReportiumClient().stepEnd();

		} catch (Exception e) {
			
			failTestScript("Navigation to Luckystrike offer page failed", e);
		}
	}

	@And("^I click on view coupon link$")
    public void i_click_on_view_coupon_link() throws Throwable {
		
		try {
			//DriverManager.getReportiumClient().stepStart("Step:I click on the redeem now button in the coupon page");	
			waitUntilPageReadyStateComplete(30);
			if(isElementPresent(HomePageObjects.btn_tobbaccopref2)) {
				 clickIfElementPresent(HomePageObjects.btn_tobbaccopref2, "Tobacco preferences icon"); 
				 }
			 if(isElementPresent(HomePageObjects.btn_tobbaccopref)) {
			 clickIfElementPresent(HomePageObjects.btn_tobbaccopref, "Tobacco preferences icon"); 
			 }
			 if(isElementPresent(HomePageObjects.btn_tobbaccopref3)) {
				 clickIfElementPresent(HomePageObjects.btn_tobbaccopref3, "Tobacco preferences icon"); 
				 }
			 
			 
//			if (getURL().contains("stage")) {
//				boolean noOffers = false;
//				String brand = LoginPageStepDefs.getBrand();
//				int count = 2;
//				do {
//					noOffers = false;					
//						if (!isElementPresent(HomePageObjects.view_coupon)) {
//							DriverManager.getAppiumDriver().manage().deleteAllCookies();
//							DriverManager.getAppiumDriver().navigate().to(getURL());
//							noOffers = true;
//							reLoginToApplication();
//							i_navigate_to_Newport_offers_page();
//							count--;
//					}					
//					}
//				 while (noOffers & count>0);
//			}	 
			waitUntilElementVisible(HomePageObjects.view_coupon, 30);
			clickIfElementPresent(HomePageObjects.view_coupon, "Coupon Page - Redeem Now Button");
			
			//DriverManager.getReportiumClient().stepEnd();
//			if (strEnv.contains("QA")) {
//				//DriverManager.getReportiumClient().stepEnd();
//				clickIfElementPresent(HomePageObjects.Savings, "Savings");}
		} catch (Exception e) {			
			failTestScript("Click on view coupon failed", e);
		}
        
    }

	@And("^I click on redeem button$")
	public void i_click_on_redeem_button() {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:I click on the mobile offers button in the coupon page");
			waitUntilElementVisible(HomePageObjects.btnOffers, 20);
			clickIfElementPresent(HomePageObjects.btnOffers, "Coupon Page - Mobile Offers Button");
			closeSavePasswordPopUp();
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate the redemption: ", e);
		}

	}

	public void reLoginToApplication() {
		try {
			//DriverManager.getReportiumClient().stepStart("I re-login with valid user id and password");
			String brand = getBrand();
			String password = getPassword();
			String username;
			waitUntilPageReadyStateComplete(30);
			 if( getBrand().contains("COUGAR")) {
					username ="<EMAIL>";
				} else {
			username = apiLibrary.getuserIdWithOffers(brand);}
			
			//username ="<EMAIL>";
			System.out.println(password);
			clearAndEnterText(LoginPageObjects.txtBoxUsername, username, "Login Page - Username text box");
			//clearAndEnterText(LoginPageObjects.txtBoxPassword, password, "Login Page - Password text box");
			//clearAndEnterText(LoginPageObjects.txtBoxPassword, password, "Login Page - Password text box");
			Thread.sleep(3000);
		    driver.findElement(LoginPageObjects.txtBoxPassword).sendKeys("Password1");
			Thread.sleep(3000);
			clickIfElementPresent(LoginPageObjects.btnLogin, "Login Page - Login button");
			Thread.sleep(3000);
			//DriverManager.getReportiumClient().stepEnd();
			closeSavePasswordPopUp();
		} catch (Exception e) {
			
			failTestScript("Relogin to application got failed", e);
		}
	}

	
	@And("^I click on Get Offer button$")
	public void i_click_on_Get_Offer_button() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:I click on the mobile offers button in the coupon page");
			if (isElementPresent(HomePageObjects.btn_tobbaccopref)) {
				clickIfElementPresent(HomePageObjects.btn_tobbaccopref, "Tobacco preferences icon");
			}
			if (getURL().contains("stage")) {
				boolean noOffers = false;
				int count=5;
				do {
					noOffers = false;
					if (!isElementPresent(HomePageObjects.btnGetOffers)) {
						DriverManager.getAppiumDriver().manage().deleteAllCookies();
						DriverManager.getAppiumDriver().navigate().to(getURL());
						noOffers = true;
						reLoginToApplication();
						i_navigate_to_Kodiak_offers_page();
						count--;
					}
				} while (noOffers & count>0);
			}
			if(isElementPresent(HomePageObjects.btnGetOffers))
			{
				clickIfElementPresent(HomePageObjects.btnGetOffers, "Coupon Page - Offers Button");}
			if(isElementPresent(HomePageObjects.mobile_offerLink))
			{
				clickIfElementPresent(HomePageObjects.mobile_offerLink, "Coupon Page - Mobile Offers Button");
			}
				
//		if (strEnv.contains("QA")) {
//			//DriverManager.getReportiumClient().stepEnd();
//			clickIfElementPresent(HomePageObjects.Savings, "Savings");}
		}
		catch (Exception e) {			
			failTestScript("click on redeem button failed", e);
		}
	}

	@When("^I set the valid device location to (.+)$")
	public void i_set_the_valid_device_location_to(String loc) throws IOException, InterruptedException {
		//DriverManager.getReportiumClient().stepStart("Step:The device will be set to suitable loacation");
		//setLoc(loc);
		location.set(loc);
		setDeviceLocation(loc);
				Thread.sleep(2000);
		//DriverManager.getReportiumClient().stepEnd();
	}
	
	@And("^I navigate to Velo Offers Page$")
	public void i_navigate_to_velo_offers_page() throws Throwable {
		try {
			waitUntilPageReadyStateComplete(30);
			//DriverManager.getReportiumClient().stepStart("I navigate to Velo Offers Page");
			waitUntilElementVisible(HomePageObjects.velo_HamburgerMenu, 30);
			clickIfElementPresent(HomePageObjects.velo_HamburgerMenu, "Velo Hamburger Menu");
			clickIfElementPresent(HomePageObjects.veloAEM_offersbtn, "velo offers");
			//clickIfElementPresent(HomePageObjects.veloAEM_InStoreOffers, "Velo InstoreOffers");
			
		} catch (Exception e) {			
			failTestScript("Navigating to Velo offers Page failed", e);
		}
	}
	
	@And("^I click on reveal offers button$")
	public void i_click_on_reveal_offers_button() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:I click on reveal offers button in the coupon page");
			waitUntilPageReadyStateComplete(30);
		WebElement e3=driver.findElement(By.xpath("//span[text()='Reveal offers']"));
			
			scrollToElement(driver, e3);
			waitUntilElementVisible(HomePageObjects.veloAEM_RevealOffers, 30);
			clickIfElementPresent(HomePageObjects.veloAEM_RevealOffers, "Reveal Offers Button");
			waitFor(1000);
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			
			failTestScript("Click on reveal offers button failed", e);
		}
	}
	@When("^I navigate to Kodiak Offers Page$")
	public void i_navigate_to_Kodiak_offers_page() throws Throwable {
		try {
			waitUntilPageReadyStateComplete(30);
			if (driver instanceof AndroidDriver) {
				if(isElementPresent(HomePageObjects.popup_kodiakbtn))
				{
					clickIfElementPresent(HomePageObjects.popup_kodiakbtn, "Pop up close button");
				}
				if (isElementPresent(HomePageObjects.btn_tobbaccopref)) {
					clickIfElementPresent(HomePageObjects.btn_tobbaccopref, "Tobacco preferences icon");
				}
				//DriverManager.getReportiumClient().stepStart("I navigate to Kodiak Offers Page");
				if(isElementPresent(HomePageObjects.hamburgerMenu))
				{
					clickIfElementPresent(HomePageObjects.hamburgerMenu, "Home Page - Hamburger Menu");
				}if(isElementPresent(HomePageObjects.lnk_KodiakOffers))
				{
				clickIfElementPresent(HomePageObjects.lnk_KodiakOffers, "Home Page - Get a Coupon link");
				}
				//DriverManager.getReportiumClient().stepEnd();
			}

			else if (driver instanceof IOSDriver) {

				if (driver.getCurrentUrl().contains("stage")) {
					driver.get("https://aem-stage.kodiakspirit.com/secure/coupons.html");
				} else {
					driver.get("https://www.kodiakspirit.com/secure/coupons.html");
				}
			}
		}

		catch (Exception e) {

			failTestScript("Navigation to Kodiak offers page failed", e);
		}
	}
	
	@When("^I login with valid user id (.+) and password (.+) for (.+)$")
	public void i_login_with_valid_user_id_and_password_for(String username, String password, String brand) throws Throwable {
		//DriverManager.getReportiumClient().stepStart("I login with valid user id and password for brand");
		try {
			waitUntilPageReadyStateComplete(30);
			setPassword(password);
			setBrand(brand);
		if (strEnv.contains("QA"))	{
			if(getBrand().contentEquals("CAMEL")) {
			//username ="<EMAIL>";
			username ="<EMAIL>";
				//username = apiLibrary.getuserIdWithOffers(brand);
			}
		else if(getBrand().contains("COUGAR")) {
			username = apiLibrary.getuserIdWithOffers(brand);
		}
		
			else {
			//username = apiLibrary.getuserIdWithOffers(brand);
			username ="<EMAIL>";
			}
		
		//username ="<EMAIL>";
		}else {
//				username = apiLibrary.getuserIdWithOffersprod(brand);
			username ="<EMAIL>";
		}
//			JavascriptExecutor js = (JavascriptExecutor) driver;
//			js.executeScript("argument[0].setAtribute('value','<EMAIL>')",LoginPageObjects.txtBoxUsername);		waitUntilElementVisible(LoginPageObjects.txtBoxUsername, 30);
			//driver.findElement(By.xpath("//input[@id='loginUsername']")).sendKeys(username);
			
		
		
		Thread.sleep(3000);
			
			
			clearAndEnterText(LoginPageObjects.txtBoxUsername, username, "Login Page - Username text box");
			clearAndEnterText(LoginPageObjects.txtBoxPassword, password, "Login Page - Password text box");
			Thread.sleep(5000);	
//			clickIfElementPresent(LoginPageObjects.btnLogin, "Login Page - Login button");
//			Thread.sleep(2000);	
//			if(isElementPresent(LoginPageObjects.btnLogin)) {
//				clickIfElementPresent(LoginPageObjects.btnLogin, "Login Page - Login button");
//			}			//Thread.sleep(2000);	
			//clickIfElementPresent(LoginPageObjects.btnLogin, "Login Page - Login button");
			
			Thread.sleep(4000);			
			//DriverManager.getReportiumClient().stepEnd();
			closeSavePasswordPopUp();
		} 		
		catch (Exception e) {
			
			failTestScript("Login with valid userid & password failed", e);
		}
	}

	@And("^I navigate to AmericanSpirit Offers Page$")
	public void i_navigate_to_AmericanSpirit_offers_page() throws Throwable {
		try {
			if(driver instanceof AndroidDriver) {
				
				if(isElementPresent(HomePageObjects.btn_happynewyear)) {
					Thread.sleep(6000);
					 clickIfElementPresent(HomePageObjects.btn_happynewyear, "Tobacco happy new year icon"); 
					 Thread.sleep(3000);
					 System.out.println("Test passed");
					 }
				if(isElementPresent(HomePageObjects.nashamburgerMenu)) {
			clickIfElementPresent(HomePageObjects.nashamburgerMenu, "Home Page - Hamburger Menu");}
				if(isElementPresent(HomePageObjects.spa_gift)) {
			clickIfElementPresent(HomePageObjects.spa_gift, "Home Page - Get a Coupon link");}
			}
			else if (driver instanceof IOSDriver) {
				if (driver.getCurrentUrl().contains("stage")) {
					driver.get("https://aem-stage.americanspirit.com/secure/coupons.html");
				} else {
					driver.get("https://www.americanspirit.com/secure/coupons.html");
				}
			}
		} catch (Exception e) {
			
			failTestScript("Navigation to NAS offer page failed", e);
		}
	}
	
	@And("^I click on claim now button$")
	public void i_click_on_claim_now_button() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:I click on the redeem now button in the coupon page");	
			waitUntilPageReadyStateComplete(30);
			
			
			/*if(driver.getCurrentUrl().contains("stage") && driver instanceof AndroidDriver) 
			 {
				switchToContext(driver, "NATIVE_APP");
				clickIfElementPresent(LocationServices.androidLocationServicesPermissionAllow,"Location services Permission - Allow Button");
				switchToContext(driver, "WEBVIEW");
			}*/
			
			if(isElementPresent(HomePageObjects.btn_tobbaccopref)) {
				 clickIfElementPresent(HomePageObjects.btn_tobbaccopref, "Tobacco preferences icon"); 
				 }
			
			/*if(driver.getCurrentUrl().contains("newport-pleasure.com/secure/coupons.html")) {
			waitUntilElementVisible(CouponHomePage.np_signuptextclose, 30);
			clickIfElementPresent(CouponHomePage.np_signuptextclose, "NP Close button");
			 }*/
			
//			if (getURL().contains("stage")) {
//				boolean noOffers = false;
//				String brand = LoginPageStepDefs.getBrand();
//				int count = 2;
//				do {
//					noOffers = false;					
//						if (!isElementPresent(HomePageObjects.btn_ClaimNow)) {
//							DriverManager.getAppiumDriver().manage().deleteAllCookies();
//							DriverManager.getAppiumDriver().navigate().to(getURL());
//							noOffers = true;
//reLoginToApplication();
//							i_navigate_to_Newport_offers_page();
//							count--;
//					}					
//					}
//				 while (noOffers & count>0);
//			}
//			Thread.sleep(5000);
//			if(LoginPageStepDefs.getBrand().equalsIgnoreCase("NEWPORT") &&  driver instanceof AndroidDriver) {
//				if(!isElementPresent(HomePageObjects.btn_ClaimNow)) {
////				 switchToContext(driver, "WEBVIEW");
//				System.out.println("Newport");
//				}
//			}
//			Thread.sleep(5000);
//			waitUntilElementVisible(HomePageObjects.btn_ClaimNow, 30);
			Thread.sleep(5000);
			if (strEnv.contains("QA")) {
				//DriverManager.getReportiumClient().stepEnd();
				Thread.sleep(10000);
				clickIfElementPresent(HomePageObjects.btn_ClaimNowstage, "claimnow");}
			
			else {
				
				WebElement click = driver.findElement(HomePageObjects.btn_ClaimNow);
				JavascriptExecutor executor = (JavascriptExecutor)driver;
				
				executor.executeScript("arguments[0].click();",click);
				
				
				
//				WebElement click = driver.findElement(HomePageObjects.btn_ClaimNow);
//				Actions cli = new Actions(driver);
//				cli.moveToElement(click).click().perform();
//				
				Thread.sleep(3000);
			//clickIfElementPresent(HomePageObjects.btn_ClaimNow, "Coupon Page - Redeem Now Button");
			//DriverManager.getReportiumClient().stepEnd();}
			}}	
		 catch (Exception e) {			
			failTestScript("Click on claim now button failed", e);
		}
	}

	@And("^I click on get gift certificate button$")
	public void i_click_on_get_gift_certificate_button() throws Throwable {
		try {
			if(driver instanceof AndroidDriver) {
//				if(isElementPresent(HomePageObjects.btn_tobbaccopref1)) {
//					Thread.sleep(3000);
//					 clickIfElementPresent(HomePageObjects.btn_tobbaccopref1, "Tobacco preferences icon"); 
//					 Thread.sleep(3000);
//					 System.out.println("Test passed");
//					 }	Thread.sleep(6000);
				Thread.sleep(6000);
				
			if(isElementPresent(HomePageObjects.btn_tobbaccopref)) {
				Thread.sleep(6000);
				 clickIfElementPresent(HomePageObjects.btn_tobbaccopref, "Tobacco preferences icon"); 
				 Thread.sleep(3000);
				 System.out.println("Test passed");
				 }
			}
			else if(driver instanceof IOSDriver) {
				switchToContext(driver, "NATIVE_APP");
				
				if(isElementPresent(HomePageObjects.btn_tobbaccopref)) {
					 clickIfElementPresent(HomePageObjects.btn_tobbaccopref, "Tobacco preferences icon"); 
					 }
				switchToContext(driver, "WEBVIEW");
				}
			if(driver.getCurrentUrl().contains("stage")) {
			waitUntilElementVisible(HomePageObjects.checkforoffers, 30);
			clickIfElementPresent(HomePageObjects.checkforoffers, "Coupon Page - Redeem Now Button");
			}
			else {
				if(isElementPresent(HomePageObjects.checkforoffers)) {
				clickIfElementPresent(HomePageObjects.checkforoffers, "Coupon Page - Redeem Now Button");}
			}
			
			
			
			//			if (strEnv.contains("QA")) {
//				//DriverManager.getReportiumClient().stepEnd();
//				//clickIfElementPresent(HomePageObjects.Savings, "Savings");}
		} catch (Exception e) {
			
			failTestScript("Clicking on get gift certificate failed", e);
		}
	}
	
	@And("^I set the device location to nearby (.+) Store with (.+)$")
	public void i_set_the_device_location_to_nearby_store_with(String retailer, String loc) throws Throwable {
		//DriverManager.getReportiumClient()
//				.stepStart("Set the device location to the retailer: " + retailer + " with address: " + loc);
		setDeviceLocation(loc);
		//DriverManager.getReportiumClient().stepEnd();
		addStepLog("The device location is set for the retailer :" + retailer + " with location address:" + loc);
	}
	
	@When("^I launch Setting and select Ask first option in Location$")
	public void I_launch_Setting () throws Throwable {
		//DriverManager.getReportiumClient().stepStart("I login with valid user id and password for brand");
		try {
			
			lauchsetting();
			
		}
catch (Exception e) {
			
			failTestScript("Lauch setting failed", e);
		}}
	
	@And("^I scroll down and validate copyright text (.+) for (.+)$")
	public void i_scroll_down_and_validate_copyright_text(String copyrightText,String brand){
		try {
//			((JavascriptExecutor) driver).executeScript("mobile: scroll", ImmutableMap.of("direction", "down"));
//			scrollToBottom(driver);
			swipeFromUpToBottom(driver);
			if(brand.equals("NASCIGS")) {
				isElementPresentVerification(LoginPageObjects.NAScopyrightText, "NAS copyright text");
				isElementPresentContainsText(LoginPageObjects.NAScopyrightText, copyrightText, "NAS copyright text");
				System.out.println(copyrightText);
				}
				if(brand.equals("CAMEL")) {
//					isElementPresentContainsText(LoginPageObjects.CamelcopyrightText, copyrightText, "NAS copyright text");
					isElementPresentVerification(LoginPageObjects.CamelcopyrightText, "Camel copyright text");
				}
				if(brand.equals("CAMELSNUS")) {
					scrollToBottom(DriverManager.getAppiumDriver());
					scrollDown();
					scrollDownFromMid();
					scrollToMobileElement("©2024 R.J. Reynolds Tobacco Co");
					isElementPresentVerification(LoginPageObjects.CamelSnuscopyrightText, "CamelSnus copyright text");
					System.out.println(copyrightText);
				}
				if(brand.equals("COUGAR")) {
					scrollToBottom(DriverManager.getAppiumDriver());
					scrollDown();
					scrollDownFromMid();
					scrollToMobileElement("©2024 American Snuff Company");
					isElementPresentVerification(LoginPageObjects.CougarcopyrightText, "Cougar copyright text");
				}
				if(brand.equals("GRIZZLY")) {
					isElementPresentVerification(LoginPageObjects.GrizzlycopyrightText, "Grizzly copyright text");
				}
				if(brand.equals("KODIAK")) {
					isElementPresentVerification(LoginPageObjects.KodiakcopyrightText, "Kodiak copyright text");
				}
				if(brand.equals("Levigarrett")) {
					isElementPresentVerification(LoginPageObjects.LevigarrettcopyrightText, "Levigarrett copyright text");
				}
				if(brand.equals("LUCKY STRIKE")) {
					isElementPresentVerification(LoginPageObjects.LuckyStrikecopyrightText, "LuckyStrike copyright text");
				}
				if(brand.equals("PALLMALL")) {
					isElementPresentVerification(LoginPageObjects.PallMallcopyrightText, "PallMall copyright text");
				}
				if(brand.equals("NEWPORT")) {
					isElementPresentVerification(LoginPageObjects.NewportcopyrightText, "Newport copyright text");
				}
				if(brand.equals("VELO")) {
					isElementPresentVerification(LoginPageObjects.VelocopyrightText, "Velo copyright text");
				}
				if(brand.equals("VUSE")) {
					isElementPresentVerification(LoginPageObjects.VusecopyrightText, "Vuse copyright text");
				}
			Thread.sleep(1000);
		}
		catch(Exception e) {
			failTestScript("Validating the copyright text in login screen failed", e);
		}
	}

}
