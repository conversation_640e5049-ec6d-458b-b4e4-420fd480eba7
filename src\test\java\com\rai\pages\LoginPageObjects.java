package com.rai.pages;

import org.openqa.selenium.By;

public class LoginPageObjects {
	public static By spa_21 = By.xpath("//span[contains(text(),'21+')]"); // SPA login page - 21+
	public static By Locationvalue	= By.xpath("//XCUIElementTypeSwitch[@value='0']"); // SPA login page - 21+
	public static By kodiak_Login = By.xpath("(//span[text()='Login'])[2]"); //Kodiak login button
	public static By txtBoxUsername_velo_prod = By.xpath("(//input[@name='j_username'])[2]");
	public static By txtBoxPassword_velo = By.xpath("(//input[@name='j_password'])[2]");
	public static By txtBoxUsername_vuse = By.id("email");
	public static By txtBoxPassword_vuse =By.xpath("//div[@class='input-field control password pr']//input[@id='pass']");
	public static By txtBoxUsername = By.xpath("//input[@id='loginUsername']");
	public static By txtBoxPassword = By.xpath("//input[@id='loginPassword']");
	//Velo Login Elements
	public static By velousername =By.xpath("(//input[@name='j_username'])[2]");
	public static By velopassword =By.xpath("(//input[@name='j_password'])[2]");
	
	public static By vusetxtBoxUsername=By.xpath("//*[@class=\"input-field username control\"]//*[@class=\"input-text username\"]");
	public static By vusetxtBoxPassword=By.xpath("//*[@class=\"input-field control password pr\"]//*[@class=\"input-text password\"]");
	public static By vusebtnLogin= By.xpath("//*[@class=\"action next-login button button-alternate\"]//*[text()=\"Login\"]");
	public static By btnLogin = By.xpath("//button/span[contains(text(), 'SIGN') or contains(text(),'Login')]");
	public static By btnLoginprd = By.xpath("(//button/span[contains(text(), 'SIGN') or contains(text(),'Login')])[2]");
	public static By Chkbx_Rememberme=By.xpath("//*[text()=\"Remember Me\"]");
	public static By txtRegister= By.xpath("//*[text()=\"Register\"]");
	public static By lnk_Contactus=By.xpath("//*[@class=\"cmp-authenticated-container__unauthenticated-content\"]//*[text()=\"Contact Us\"]");
	public static By lnk_Faq=By.xpath("//*[@class=\"cmp-authenticated-container__unauthenticated-content\"]//*[text()=\"FAQ\"]");
	public static By lnk_TobaccoRights=By.xpath("//*[@class=\"cmp-authenticated-container__unauthenticated-content\"]//*[text()=\"Tobacco Rights\"]");
	public static By lnk_SiteRequirements=By.xpath("//*[@class=\"cmp-authenticated-container__unauthenticated-content\"]//*[text()=\"Site Requirements\"]");
	public static By lnk_termsofuse=By.xpath("//*[@class=\"cmp-authenticated-container__unauthenticated-content\"]//*[text()=\"Terms of Use\"]");
	public static By lnk_policyrights=By.xpath("//*[@class=\"cmp-authenticated-container__unauthenticated-content\"]//*[text()=\"Privacy Policy and Your California Privacy Rights\"]");
    
	public static By noOffersAvailable = By.xpath("//h1[contains(text(),'No offers available') or contains(text(), 'No Offers Available')]");
	public static By btnGoBackToHomePage = By.xpath("//a[contains(@class, 'button')]");
	public static By lnkLogout = By.xpath("//span[contains(text(),'Logout')]");
	//Newport Elements
	public static By BrandImage= By.xpath("//*[@class=\"cmp-image__img\"]");
	public static By NAScopyrightText					= By.xpath("//*[text()='©2024 Santa Fe Natural Tobacco Co.']");
	public static By CamelcopyrightText					= By.xpath("//*[text()='©2024 R.J. Reynolds Tobacco Co']");
	public static By GrizzlycopyrightText					= By.xpath("//*[text()='©2024 American Snuff Company ']");
	public static By NewportcopyrightText					= By.xpath("//*[text()='©2024 R.J. Reynolds Tobacco Co']");
	public static By LuckyStrikecopyrightText					= By.xpath("//*[text()='©2024 R.J. Reynolds Tobacco Co']");
	public static By VelocopyrightText					= By.xpath("//*[text()='©2024 Modoral Brands Inc.']");
	public static By VusecopyrightText					= By.xpath("//*[text()='©2024 RJR Vapor Co.']");
	public static By KodiakcopyrightText					= By.xpath("//*[text()='©2024 American Snuff Company ']");
	public static By CougarcopyrightText					= By.xpath("//*[text()='©2024 American Snuff Company ']");
	public static By CamelSnuscopyrightText					= By.xpath("//*[text()='©2024 R.J. Reynolds Tobacco Co']");
	public static By PallMallcopyrightText					= By.xpath("//*[text()='©2024 R.J. Reynolds Tobacco Co']");
	public static By LevigarrettcopyrightText					= By.xpath("//*[text()='©2024 American Snuff Company ']");
 
}
