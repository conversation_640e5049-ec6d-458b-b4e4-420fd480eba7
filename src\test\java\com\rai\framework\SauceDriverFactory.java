package com.rai.framework;

import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;

import io.appium.java_client.ios.IOSDriver;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.time.Duration;
import java.util.Properties;
import org.openqa.selenium.MutableCapabilities;
import org.openqa.selenium.Platform;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.remote.RemoteWebDriver;

public class SauceDriverFactory {

	private static Properties mobileProperties;

	private SauceDriverFactory() {
		// To prevent external instantiation of this class
	}

	private static URL getUrl(String remoteUrl) throws URISyntaxException, MalformedURLException {
		URI uri;
		uri = new URI(remoteUrl);
		return uri.toURL();
	}

	/**
	 * Function to return the Sauce MobileCloud {@link RemoteWebDriver} object based
	 * on the parameters passed
	 * 
	 * @param deviceId  The ID of the Sauce MobileCloud device to be used for the
	 *                  test execution
	 * @param browser   The {@link Browser} to be used for the test execution
	 * @param remoteUrl The Sauce MobileCloud URL to be used for the test execution
	 * @return The corresponding {@link RemoteWebDriver} object
	 * @throws URISyntaxException
	 * @throws MalformedURLException
	 */

	public static WebDriver getSauceRemoteWebDriver(String deviceId, Browser browser, String remoteUrl)
			throws MalformedURLException, URISyntaxException {
		DesiredCapabilities desiredCapabilities = getSauceExecutionCapabilities(browser);

		desiredCapabilities.setCapability("deviceName", deviceId);

		return new RemoteWebDriver(getUrl(remoteUrl), desiredCapabilities);
	}

	private static DesiredCapabilities getSauceExecutionCapabilities(Browser browser) {
		validateSauceSupports(browser);

		DesiredCapabilities desiredCapabilities = new DesiredCapabilities();
		desiredCapabilities.setBrowserName(browser.getValue());
		desiredCapabilities.setPlatform(Platform.ANY);
		// ((Object) desiredCapabilities).setJavascriptEnabled(true); // Pre-requisite
		// for
		// remote execution

		mobileProperties = Settings.getInstance();
		desiredCapabilities.setCapability("securityToken", mobileProperties.getProperty("SecurityToken"));

		return desiredCapabilities;
	}

	private static void validateSauceSupports(Browser browser) {
		switch (browser) {
			case INTERNET_EXPLORER:
			case FIREFOX:
				throw new FrameworkException(
						"The browser " + browser.toString() + " is not supported on the Perfecto MobileCloud");

			default:
				break;
		}
	}

	/**
	 * Function to return the Sacue MobileCloud {@link RemoteWebDriver} object based
	 * on the parameters passed
	 * 
	 * @param deviceId          The device Name
	 * @param osVersion         The device platform version to be used for the test
	 *                          execution
	 * @param browser           The {@link Browser} to be used for the test
	 *                          execution
	 * @param remoteUrl         The Sauce MobileCloud URL to be used for the test
	 *                          execution
	 * @param executionPlatform The MobileExecutionPlatform
	 *                          {@link MobileExecutionPlatform}
	 * @return The corresponding {@link RemoteWebDriver} object
	 * @throws URISyntaxException
	 * @throws MalformedURLException
	 */

	public static WebDriver getSauceRemoteWebDriverByDevicePlatform(String deviceId, String osVersion, Browser browser,
			String remoteUrl, MobileExecutionPlatform executionPlatform)
			throws MalformedURLException, URISyntaxException {

		String platformName = "";
		if (executionPlatform.equals(MobileExecutionPlatform.WEB_ANDROID)) {
			platformName = "Android";
		} else if (executionPlatform.equals(MobileExecutionPlatform.WEB_IOS)) {
			platformName = "ios";
		}
		DesiredCapabilities desiredCapabilities = getSauceExecutionCapabilities(browser);
		desiredCapabilities.setBrowserName(browser.getValue());
		desiredCapabilities.setCapability("platformName", platformName);
		desiredCapabilities.setCapability("platformVersion", osVersion);

		return new RemoteWebDriver(getUrl(remoteUrl), desiredCapabilities);
	}

	/**
	 * Function to return the Sauce MobileCloud {@link RemoteWebDriver} object based
	 * on the parameters passed
	 * 
	 * @param manufacturer The manufacturer of the device to be used for the test
	 *                     execution (Samsung, Apple, etc.)
	 * @param model        The device model to be used for the test execution
	 *                     (Galaxy S6, iPad Air, etc.)
	 * @param browser      The {@link Browser} to be used for the test execution
	 * @param remoteUrl    The Sauce MobileCloud URL to be used for the test
	 *                     execution
	 * @return The corresponding {@link RemoteWebDriver} object
	 * @throws URISyntaxException
	 * @throws MalformedURLException
	 */

	public static WebDriver getSauceRemoteWebDriverByDeviceModel(String manufacturer, String model, Browser browser,
			String remoteUrl) throws MalformedURLException, URISyntaxException {
		DesiredCapabilities desiredCapabilities = getSauceExecutionCapabilities(browser);
		desiredCapabilities.setCapability("manufacturer", manufacturer);
		desiredCapabilities.setCapability("model", model);
		return new RemoteWebDriver(getUrl(remoteUrl), desiredCapabilities);
	}

	/**
	 * Function to return the object for AppiumDriver {@link AppiumDriver}
	 * object
	 * 
	 * @param executionPlatform
	 *                          executionPlatform{@link MobileExecutionPlatform}
	 * @param deviceName
	 *                          The deviceNameF
	 * @param perfectoURLF
	 *                          Host URL of Sauce
	 * 
	 * @return Instance of the {@link AppiumDriver} object
	 */

	public static AppiumDriver getSauceAppiumDriver(SeleniumTestParameters testParameters, String ScenarioName) {
		AppiumDriver driver = null;
		mobileProperties = Settings.getInstance();
		MutableCapabilities caps = new MutableCapabilities();
		MutableCapabilities sauceOptions = new MutableCapabilities();
		try {

			switch (testParameters.getMobileExecutionPlatform()) {

				case WEB_ANDROID:
					caps.setCapability("platformName", "Android");
					caps.setCapability("browserName", "Chrome");
					caps.setCapability("appium:deviceName", testParameters.getDeviceName());
					caps.setCapability("appium:platformVersion", "15");

					caps.setCapability("appium:automationName", "UiAutomator2");
					sauceOptions.setCapability("phoneOnly", true);
					sauceOptions.setCapability("appiumVersion", "latest");
					sauceOptions.setCapability("username", mobileProperties.getProperty("SauceUsername"));
					sauceOptions.setCapability("accessKey", mobileProperties.getProperty("SauceAccessKey"));
					sauceOptions.setCapability("build", "SPA");
					sauceOptions.setCapability("name", ScenarioName);
					caps.setCapability("sauce:options", sauceOptions);
					try {
						driver = new AndroidDriver(new URI(mobileProperties.getProperty("SauceLabsHost")).toURL(),
								caps);
					} catch (Exception e) {
						e.printStackTrace();
						throw new FrameworkException(
								"The android driver/browser invokation has problem, please re-check the capabilities and check the perfecto details URL, Username and Password ");
					}
					System.out.println(driver.getSessionId());
					break;

				case WEB_IOS:
					caps.setCapability("platformName", "iOS");
					caps.setCapability("browserName", "Safari");
					caps.setCapability("appium:deviceName", "iPhone.*");
					caps.setCapability("appium:platformVersion", "17");
					caps.setCapability("appium:automationName", "XCUITest");
					sauceOptions.setCapability("phoneOnly", true);
					sauceOptions.setCapability("appiumVersion", "latest");
					sauceOptions.setCapability("username", mobileProperties.getProperty("SauceUsername"));
					sauceOptions.setCapability("accessKey", mobileProperties.getProperty("SauceAccessKey"));
					sauceOptions.setCapability("build", "SPA");
					sauceOptions.setCapability("name", "SPA - Mobile Test Execution on IOS devices");
					caps.setCapability("sauce:options", sauceOptions);

					try {
						driver = new IOSDriver(new URI(mobileProperties.getProperty("SauceLabsHost")).toURL(), caps);
					} catch (Exception e) {
						throw new FrameworkException(
								"The IOS driver invokation/browser has problem, please re-check the capabilities and check the perfecto details URL, Username and Password ");
					}
					break;

				default:
					throw new FrameworkException("Unhandled ExecutionMode!");

			}
			driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(30));
			driver.manage().timeouts().pageLoadTimeout(Duration.ofSeconds(30));
		} catch (Exception ex) {
			ex.printStackTrace();
			throw new FrameworkException(
					"The Sauce appium driver invocation created a problem , please check the capabilities");
		}
		return driver;

	}

	/**
	 * Function to return the object for RemoteWebDriver {@link RemoteWebDriver}
	 * object
	 * 
	 * @param testParameters
	 * 
	 * @return Instance of the {@link RemoteWebDriver} object
	 * @throws InterruptedException
	 * @throws URISyntaxException
	 */

	public static WebDriver getSauceRemoteWebDriverForDesktop(SeleniumTestParameters testParameters)
			throws InterruptedException, URISyntaxException {

		RemoteWebDriver driver = null;
		mobileProperties = Settings.getInstance();
		String browserName = getBrowerName(testParameters.getBrowser());
		DesiredCapabilities desiredCapabilities = new DesiredCapabilities("", "", Platform.ANY);
		;
		desiredCapabilities.setCapability("securityToken", mobileProperties.getProperty("SecurityToken"));
		desiredCapabilities.setCapability("platformName", testParameters.getPlatform());
		desiredCapabilities.setCapability("browserName", browserName);

		if (testParameters.getPlatform().toString().equalsIgnoreCase("Android")) {
			desiredCapabilities.setCapability("platformName", "Android");
			desiredCapabilities.setCapability("deviceName", testParameters.getDeviceName());
			desiredCapabilities.setCapability("browserName", "Chrome");
			desiredCapabilities.setCapability("autoGrantPermissions", "true");

		} else if (testParameters.getPlatform().toString().equalsIgnoreCase("iOS")) {
			desiredCapabilities.setCapability("platformName", "iOS");
			desiredCapabilities.setCapability("deviceName", testParameters.getDeviceName());
			desiredCapabilities.setCapability("browserName", "Safari");
			desiredCapabilities.setCapability("autoGrantPermissions", "true");

		} else if (testParameters.getPlatform().toString().equalsIgnoreCase("MAC")) {
			desiredCapabilities.setCapability("platformVersion", "macOS Mojave");// testParameters.getPlatformVersion());
			desiredCapabilities.setCapability("browserVersion", testParameters.getBrowserVersion());
			desiredCapabilities.setCapability("resolution", "800x600");
			desiredCapabilities.setCapability("location", "NA-US-BOS");
		} else {
			desiredCapabilities.setCapability("platformVersion", testParameters.getPlatformVersion());// testParameters.getPlatformVersion());
			desiredCapabilities.setCapability("browserVersion", testParameters.getBrowserVersion());
			desiredCapabilities.setCapability("resolution", mobileProperties.getProperty("Resolution"));
			desiredCapabilities.setCapability("location", mobileProperties.getProperty("Location"));
		}

		desiredCapabilities.setCapability("takesScreenshot", true);
		System.out.println(testParameters.getPlatform());
		try {
			driver = new RemoteWebDriver(new URI(mobileProperties.getProperty("SauceLabsHost")).toURL(),
					desiredCapabilities);

		} catch (MalformedURLException e) {
			e.printStackTrace();
		}
		return driver;
	}

	private static String getBrowerName(Browser browser) {
		String browserName = null;
		if (browser.equals(Browser.CHROME)) {
			browserName = "Chrome";
		} else if (browser.equals(Browser.FIREFOX)) {
			browserName = "Firefox";
		} else if (browser.equals(Browser.INTERNET_EXPLORER)) {
			browserName = "Internet Explorer";
		} else if (browser.equals(Browser.EDGE)) {
			browserName = "Edge";
		} else if (browser.equals(Browser.SAFARI)) {
			browserName = "Safari";
		}
		return browserName;
	}
}
