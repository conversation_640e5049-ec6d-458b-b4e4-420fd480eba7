Feature: Newport RBDS - Coupon Home for all the retailers
As a RJR user, I should be able to validate Newport coupon homepage 

@NewportValidateCouponHome
  Scenario Outline: Validate the RBDS Coupon Home Page and availability of the coupons for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate I'm on the Newport Coupons Home Page
      And I validate the Coupons Home Page
      
@NewportCouponHome_QA        	
 Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          						| Username                         | Password  | Loc                                            | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Newport | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
@NewportCouponHome_Prod
Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          			| Username                         | Password  | Loc                                            | Env  | 
      | Newport | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | Prod |
      | Newport | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | Prod |
      | Newport | Sheetz               | 595324   | https://www.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | Prod |
      | Newport | Speedway             | 529181   | https://www.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | Prod |
  
@NewportCouponSort
   Scenario Outline: Validate that the user is able to sort the coupons by value and expiration date for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
     Then I validate the default view of the coupons sorted by value
      And I sort the coupons by expiration date and validate the coupons
  
@NewportCouponSort_QA
 Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          						| Username                         | Password  | Loc                                            | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Newport | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
@NewportCouponSort_Prod
Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          			| Username                         | Password  | Loc                                            | Env  | 
      | Newport | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | Prod |
      | Newport | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | Prod |
      | Newport | Sheetz               | 595324   | https://www.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | Prod |
      | Newport | Speedway             | 529181   | https://www.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | Prod | 
  
@NewportCouponvalidateRestrictions      
   Scenario Outline: Validate the user is displayed with error message when he tries to access the coupon from the restricted states location for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc> with restricted state
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate the error popup stating that coupons are not available for restricted location for the brand <Brand>

@NewportCouponvalidateRestrictions_QA 
 Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          						| Username                         | Password  | Loc                               | Env | 
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Newport | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Newport | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
  
@NewportCouponvalidateRestrictions_Prod
Examples: 
      | Brand 	| Retailer             | RBDScode | URL                                          			| Username                         | Password  | Loc                               | Env  | 
      | Newport | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | Prod | 
      | Newport | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | Prod | 
      | Newport | Sheetz               | 595324   | https://www.newport-pleasure.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | Prod | 
      | Newport | Speedway             | 529181   | https://www.newport-pleasure.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | Prod | 
      
 @NewportWelcomeCoupon
  Scenario Outline: Validate the user is getting the <Coupontype> coupon for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid username <Username> and password <Password> for the brand <Brand> on env <Env> with RBDS code <RBDScode> for validating <Coupontype> Coupons
     And I click on the Understood button
     Then I validate the <Coupontype> present in the Coupons page
     
  @NewportWelcomeCoupon_QA
  Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                     | Username               | Password  | Loc                           | Env   | Coupontype |
      | Newport | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | QA  | Welcome    |
      | Newport | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | Birthday   |
      
  @NewportWelcomeCoupon_Prod
  Examples: 
      | Brand   | Retailer             | RBDScode | URL                                               | Username               | Password  | Loc                           | Env   | Coupontype |
      | Newport | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | Welcome    |
      | Newport | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | Birthday   |
      
 @Newport_CopyrightTextinLoginPage
Scenario Outline: Validate the user is able to view the Copyright text in Newport login page 
	 Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
	 And I scroll down and validate copyright text <copyrightText> for <Brand>
	 
	 
	 @Newport_CopyrightTextinLoginPage_QA
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | NEWPORT | 7-Eleven Corporation | 572896   | https://aem-stage.newport-pleasure.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | NEWPORT | Murphy               | 741592   | https://aem-stage.newport-pleasure.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | NEWPORT | Sheetz               | 595324   | https://aem-stage.newport-pleasure.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | NEWPORT | Speedway             | 529181   | https://aem-stage.newport-pleasure.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
		
	 
	 @Newport_CopyrightTextinLoginPage_Prod
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | NEWPORT | 7-Eleven Corporation | 572896   | https://www.newport-pleasure.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | NEWPORT | Murphy               | 741592   | https://www.newport-pleasure.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | NEWPORT | Sheetz               | 595324   | https://www.newport-pleasure.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | NEWPORT | Speedway             | 529181   | https://www.newport-pleasure.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
	