Feature: Vuse RBDS -- Android Location Services
As a RJR user, I shall be validating Vuse Location services

@VuseValidateLocationServices
  Scenario Outline: Validate Location Services And the navigation to Vuse Coupon Page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I click on the Understood button location service
       And I click on the Learn how 
    And I click on the continue button
    And I Navigate to Chrome App Loc
    And I Navigate to Website Loc
    And I Naviagte to Clearing cache
    Then I click the back to the vuse page

      
      
      
      
      
      
      
       @VuseValidateLocation_QA
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://aem-stage.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Vuse  | Murphy               | 741592   | https://aem-stage.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Vuse  | Sheetz               | 595324   | https://aem-stage.vusevapor.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Vuse  | Speedway             | 529181   | https://aem-stage.vusevapor.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  