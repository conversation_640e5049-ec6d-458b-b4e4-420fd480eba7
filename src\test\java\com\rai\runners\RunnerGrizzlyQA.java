package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;


//@CucumberOptions(features = "src/test/resources/features/Grizzly", glue = { "com.rai.steps" }, tags = {"@GrizzlyCouponHome_QAstage"},  monochrome = true, plugin = { "pretty",
//		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
//		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
//		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

@CucumberOptions(features = "src/test/resources/features/Grizzly", glue = { "com.rai.steps" }, tags = "@GrizzlyValidateContentPostRedemption_QA or "
		+ "@GrizzlyCouponHome_QAstage or @GrizzlyCouponSort_QAStage or @GrizzlyCouponHomeTabs_QAstage or  @GrizzlyCouponvalidateRestrictions_QAStage or "
		+ "@GrizzlyRedeemAtAny0.1MileStore_QA or @GrizzlyValidateRedeemNotNow_QA or @GrizzlyValidatefavouriteStore_QA or @GrizzlyValidateErrorMessageNotnearbyStore_QA or @GrizzlyMapViewCouponRedemtion_QA or "
		+ "@GrizzlyValidateHamburgerMenu_QA or @GrizzlyLogoutfromHamburgerMenu_QA or @NavigateGrizzlyMobilesiteHamburgerMenu_QA or "
		+ "@GrizzlySGWValidations_QA or "
		+ "@GrizzlyStoreListView_QA or @GrizzlyStoreListSearchByZip_QA or @GrizzlyStoreDetailsMapView_QA or @GrizzlyStoreMapViewbyZip_QA",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

// VeloValidateErrorMessageNotnearbyStore_QA
public class RunnerGrizzlyQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
