Feature: Camelsnus SPA-SGW Validation
As an RJR user, I should be able to validate the SGW for Camelsnus SPA. 

@CamelsnusSGWValidations
  Scenario Outline: Validate the SGW image text on all the pages for the Brand <Brand> and the Retailer <Retailer> for the brand <Brand>
   Scenario Outline: Validate the SGW image text on all the pages for the Brand <Brand> and the Retailer <Retailer> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
       And I validate triggertext<Triggertext>text<text>
       And I set the valid device location to <Loc>
      And The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     When I click on the Understood button
      And I validate coupontriggertext<CouponElementTriggertext>text<text>
       And I select a coupon and click on choose a store button
      And I validate storelisttriggertext<StorelistElementTriggertext>text<text>
      And I select a store from store list page
       And I validate StoreDetails<StoreDetails>text<text>
      And I navigate to store details map view page
       And I validate storelisttriggertext<StorelistElementTriggertext>text<text>
       And I navigate to map view show directions page
  
  @CamelsnusSGWValidations_QA
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                              | Username                         | Password  | Loc                                       | SGW_Q1                                                      | SGW_Q2                                                      | SGW_Q3                                                         | SGW_Q4                                                      | Element                                                 | LoginPageSGWElement                                | Env | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://aem-stage.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 1655 W Washington St, Bolivar, WV 25425   | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: Smokeless tobacco is addictive. | WARNING: Smokeless tobacco is addictive. | //div[contains(@id, 'lblsgwheader')]/following::span[1] | //div[contains(text(), 'SNUS')]/following::span[1] | QA  | 
      | Camelsnus | Murphy               | 741592   | https://aem-stage.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 79 E Bromley Ln, Brighton, CO 80601       | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: Smokeless tobacco is addictive. | WARNING: Smokeless tobacco is addictive.| //div[contains(@id, 'lblsgwheader')]/following::span[1] | //div[contains(text(), 'SNUS')]/following::span[1] | QA  | 
      | Camelsnus | Sheetz               | 595324   | https://aem-stage.camelsnus.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106 | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: Smokeless tobacco is addictive. | WARNING: Smokeless tobacco is addictive. | //div[contains(@id, 'lblsgwheader')]/following::span[1] | //div[contains(text(), 'SNUS')]/following::span[1] | QA  | 
      | Camelsnus | Speedway             | 529181   | https://aem-stage.camelsnus.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 1485 NC-66, Kernersville, NC 27284        | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: Smokeless tobacco is addictive. | WARNING: Smokeless tobacco is addictive.| //div[contains(@id, 'lblsgwheader')]/following::span[1] | //div[contains(text(), 'SNUS')]/following::span[1] | QA  | 
  
  @CamelsnusSGWValidations_Prod
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                        | Username                                  | Password  | Loc                                       | SGW_Q1                                                      | SGW_Q2                                                      | SGW_Q3                                                         | SGW_Q4                                   | Element                                                 | LoginPageSGWElement                                | Env  | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://www.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 1655 W Washington St, Bolivar, WV 25425   | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product is not a safe alternative to cigarettes. | WARNING: Smokeless tobacco is addictive. | //div[contains(@id, 'lblsgwheader')]/following::span[1] | //div[contains(text(), 'SNUS')]/following::span[1] | PROD | 
      | Camelsnus | Murphy               | 741592   | https://www.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 79 E Bromley Ln, Brighton, CO 80601       | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product is not a safe alternative to cigarettes. | WARNING: Smokeless tobacco is addictive. | //div[contains(@id, 'lblsgwheader')]/following::span[1] | //div[contains(text(), 'SNUS')]/following::span[1] | PROD | 
      | Camelsnus | Sheetz               | 595324   | https://www.camelsnus.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106 | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product is not a safe alternative to cigarettes. | WARNING: Smokeless tobacco is addictive. | //div[contains(@id, 'lblsgwheader')]/following::span[1] | //div[contains(text(), 'SNUS')]/following::span[1] | PROD | 
      | Camelsnus | Speedway             | 529181   | https://www.camelsnus.com/?RBDSCode=529181 | <EMAIL> | Password1 | 1485 NC-66, Kernersville, NC 27284        | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product can cause gum disease and tooth loss. | WARNING: This product is not a safe alternative to cigarettes. | WARNING: Smokeless tobacco is addictive. | //div[contains(@id, 'lblsgwheader')]/following::span[1] | //div[contains(text(), 'SNUS')]/following::span[1] | PROD | 
  
@CamelsnusTriggertextValidations
   Scenario Outline: Validate the SGW image text on all the pages for the Brand <Brand> and the Retailer <Retailer> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
       And I validate triggertext<Triggertext>text<text>
       And I set the valid device location to <Loc>
      And The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     When I click on the Understood button
      And I validate coupontriggertext<CouponElementTriggertext>text<text>
       And I select a coupon and click on choose a store button
      And I select a store from store list page
       And I validate StoreDetails<StoreDetails>text<text>
  
  
  @CamelsnusTriggertextValidations_QA
    Examples: 
     | Brand   | Retailer             | RBDScode | URL                                                   | Username                         | Password  | Loc                                            |  Env |CouponElementTriggertext|Triggertext|StorelistElementTriggertext|StoreDetails|text| 
     | Camelsnus | 7-Eleven Corporation | 572896   | https://aem-stage.Camelsnus.com/?RBDSCode=572896  | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  |  QA  | //div[@id='frmCouponsHome_CouponsHome_lblsgwheader' or text()='SNUS']|//div[text()='SNUS']|//div[@id='frmStoreList_StoreList_lblSgwHeaderImg']|//div[@id='frmSelectStore_SelectStore_lblsgwheader'] |SNUS|
      | Camelsnus | Murphy               | 741592   | https://aem-stage.Camelsnus.com/?RBDSCode=741592| <EMAIL>           | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 |  QA  |  //div[@id='frmCouponsHome_CouponsHome_lblsgwheader' or text()='SNUS']|//div[text()='SNUS']|//div[@id='frmStoreList_StoreList_lblSgwHeaderImg']|//div[@id='frmSelectStore_SelectStore_lblsgwheader']|SNUS|
      | Camelsnus | Sheetz               | 595324   | https://aem-stage.Camelsnus.com/?RBDSCode=595324     | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106  |  QA  | //div[@id='frmCouponsHome_CouponsHome_lblsgwheader' or text()='SNUS']|//div[text()='SNUS']|//div[@id='frmStoreList_StoreList_lblSgwHeaderImg']|//div[@id='frmSelectStore_SelectStore_lblsgwheader'] |SNUS|
      | Camelsnus | Speedway             | 529181   | https://aem-stage.Camelsnus.com/?RBDSCode=529181  | <EMAIL>           |Password1 | 401 N Main St, Kernersville, NC 27284           |  QA  |  //div[@id='frmCouponsHome_CouponsHome_lblsgwheader' or text()='SNUS']|//div[text()='SNUS']|//div[@id='frmStoreList_StoreList_lblSgwHeaderImg']|//div[@id='frmSelectStore_SelectStore_lblsgwheader']|SNUS|
 
  @CamelsnusTriggertextValidations_Prod
    Examples: 
    | Brand   | Retailer             | RBDScode | URL                                                   | Username                         | Password  | Loc                                            |  Env |CouponElementTriggertext|Triggertext|StorelistElementTriggertext|StoreDetails|text| 
     | Camelsnus | 7-Eleven Corporation | 572896    | https://www.Camelsnus.com/?RBDSCode=572896  | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  |  PROD | //div[@id='frmCouponsHome_CouponsHome_lblsgwheader' or text()='SNUS']|//div[text()='SNUS']|//div[@id='frmStoreList_StoreList_lblSgwHeaderImg']|//div[@id='frmSelectStore_SelectStore_lblsgwheader'] |SNUS|
      | Camelsnus | Murphy               | 741592   | https://www.Camelsnus.com/?RBDSCode=741592| <EMAIL>           | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 |  PROD  |  //div[@id='frmCouponsHome_CouponsHome_lblsgwheader' or text()='SNUS']|//div[text()='SNUS']|//div[@id='frmStoreList_StoreList_lblSgwHeaderImg']|//div[@id='frmSelectStore_SelectStore_lblsgwheader'] |SNUS|
      | Camelsnus | Sheetz               | 595324   | https://www.Camelsnus.com/?RBDSCode=595324     | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      |  PROD  | //div[@id='frmCouponsHome_CouponsHome_lblsgwheader' or text()='SNUS']|//div[text()='SNUS']|//div[@id='frmStoreList_StoreList_lblSgwHeaderImg']|//div[@id='frmSelectStore_SelectStore_lblsgwheader']|SNUS|
      | Camelsnus | Speedway             | 529181   | https://www.Camelsnus.com/?RBDSCode=529181  | <EMAIL>           |Password1 | 401 N Main St, Kernersville, NC 27284           |  PROD |  //div[@id='frmCouponsHome_CouponsHome_lblsgwheader' or text()='SNUS']|//div[text()='SNUS']|//div[@id='frmStoreList_StoreList_lblSgwHeaderImg']|//div[@id='frmSelectStore_SelectStore_lblsgwheader']|SNUS|
  
  