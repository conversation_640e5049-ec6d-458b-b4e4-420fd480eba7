package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Kodiak", glue = { "com.rai.steps" }, tags = "@KodiakCouponHome_Prod or @KodiakCouponSort_Prod or @KodiakCouponvalidateRestrictions_Prod or "
		+ "@KodiakValidateRedeemNotNow_Prod or @KodiakValidatefavouriteStore_Prod or @KodiakValidateErrorMessageNotnearbyStore_Prod or @KodiakMapViewCouponRedemtion_Prod or "
		+ "@NavigateKodiakMobilesiteHamburgerMenu_Prod or @KodiakLogoutfromHamburgerMenu_Prod or @KodiakValidateHamburgerMenu_Prod or "
		//+ "@KodiakAccessToBrowserAndDeviceLocation_Prod or "
		+ "@KodiakSGWValidations_Prod or "
		+ "@KodiakStoreListView_Prod or @KodiakStoreListSearchByZip_Prod or @KodiakStoreDetailsMapView_Prod or @KodiakStoreMapViewbyZip_Prod",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerKodiakProd extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
