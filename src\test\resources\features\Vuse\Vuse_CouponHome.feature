Feature: Vuse RBDS - Coupon Home
As an ATC user, I should be able to successfully validate the coupon home page for the RBDS Flow for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment

@VuseValidateCouponHome
  Scenario Outline: Validate the RBDS Coupon Home Page and availability of the coupons for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I validate the Velo or Vuse Coupons Home Page
  
  @VuseCouponHome_QAstage        	
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                                            | Username                         | Password  | Loc                                            | Env | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://aem-stage.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Vuse  | Murphy               | 741592   | https://aem-stage.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Vuse  | Sheetz               | 595324   | https://aem-stage.vusevapor.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Vuse  | Speedway             | 529181   | https://aem-stage.vusevapor.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @VuseCouponHome_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env   | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://login.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD  | 
      | Vuse  | Murphy               | 741592   | https://login.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD  | 
      | Vuse  | Sheetz               | 595324   | https://login.vusevapor.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD  | 
      | Vuse  | Speedway             | 529181   | https://login.vusevapor.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD  | 
  
  @VuseCouponSort
  Scenario Outline: Validate that the user is able to sort the coupons by value and expiration date for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
     Then I validate the default view of the coupons sorted by value
      And I sort the coupons by expiration date and validate the coupons
  
  @VuseCouponSort_QAStage
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://aem-stage.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Vuse  | Murphy               | 741592   | https://aem-stage.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Vuse  | Sheetz               | 595324   | https://aem-stage.vusevapor.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Vuse  | Speedway             | 529181   | https://aem-stage.vusevapor.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @VuseCouponSort_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env   | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://login.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD  | 
      | Vuse  | Murphy               | 741592   | https://login.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD  | 
      | Vuse  | Sheetz               | 595324   | https://login.vusevapor.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD  | 
      | Vuse  | Speedway             | 529181   | https://login.vusevapor.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD  | 
  
  @VuseCouponvalidateRestrictions      
  Scenario Outline: Validate the user is displayed with error message when he tries to access the vusevapor coupon from the restricted states location for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate the error popup stating that coupons are not available for restricted location for the brand <Brand>
  
  @VuseCouponvalidateRestrictions_QAStage 
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                               | Env | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://aem-stage.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Vuse  | Murphy               | 741592   | https://aem-stage.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Vuse  | Sheetz               | 595324   | https://aem-stage.vusevapor.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | Vuse  | Speedway             | 529181   | https://aem-stage.vusevapor.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
  
  @VuseCouponvalidateRestrictions_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                               | Env   | 
      | Vuse  | 7-Eleven Corporation | 572896   | https://login.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
      | Vuse  | Murphy               | 741592   | https://login.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
      | Vuse  | Sheetz               | 595324   | https://login.vusevapor.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
      | Vuse  | Speedway             | 529181   | https://login.vusevapor.com/?RBDSCode=529181 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
  
  @VuseWelcomeCoupon
  Scenario Outline: Validate the user is getting the <Coupontype> coupon for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with existing username <Username> and password <Password> for the brand <Brand> on env <Env> with RBDS code <RBDScode> for validating <Coupontype> Coupons
     And I click on the Understood button
     Then I validate the <Coupontype> present in the Coupons page
     
  @VuseWelcomeCoupon_QA
  Examples: 
      | Brand | Retailer             | RBDScode | URL                                                            | Username                   | Password  | Loc                           | Env   | Coupontype |
      | Vuse  | 7-Eleven Corporation | 572896   | https://aem-stage.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | All in One |
      #| Vuse  | Murphy               | 741592   | https://aem-stage.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | All in One |
      #| Vuse  | Sheetz               | 595324   | https://aem-stage.vusevapor.com/?RBDSCode=595324 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | All in One |
      #| Vuse  | Speedway             | 529181   | https://aem-stage.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | All in One |
  
  @VuseWelcomeCoupon_Prod
  Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                           | Env   | Coupontype |
      | Vuse  | 7-Eleven Corporation | 572896   | https://login.vusevapor.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | Welcome    |
      | Vuse  | Murphy               | 741592   | https://login.vusevapor.com/?RBDSCode=741592 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021 | PROD  | Birthday   |
  
   @Vuse_CopyrightTextinLoginPage
Scenario Outline: Validate the user is able to view the Copyright text in Vuse login page 
	 Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
	 And I scroll down and validate copyright text <copyrightText> for <Brand>
	 
	 
	 @Vuse_CopyrightTextinLoginPage_QA
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | VUSE | 7-Eleven Corporation | 572896   | https://aem-stage.vusevapor.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | VUSE | Murphy               | 741592   | https://aem-stage.vusevapor.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | VUSE | Sheetz               | 595324   | https://aem-stage.vusevapor.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | VUSE | Speedway             | 529181   | https://aem-stage.vusevapor.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
		
	 
	 @Vuse_CopyrightTextinLoginPage_Prod
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | VUSE | 7-Eleven Corporation | 572896   | https://login.vusevaport.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | VUSE | Murphy               | 741592   | https://login.vusevaport.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | VUSE | Sheetz               | 595324   | https://login.vusevaport.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | VUSE | Speedway             | 529181   | https://login.vusevaport.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
	