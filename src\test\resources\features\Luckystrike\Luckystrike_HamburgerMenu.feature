Feature: Luckystrike RBDS - Hamburger Menu
As a RJR user, I shall be validating Luckystrike Coupon Hamburger Functionality

@LuckystrikeValidateHamburgerMenu
  Scenario Outline: Validate Hamburger Menu And the navigation to Luckystrike Coupon Page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I validate the elements in Hamburger Menu
      And I click on coupons link
     Then I validate I'm on the Coupons Home Page  
  
  @LuckystrikeValidateHamburgerMenu_QA
    Examples: 
      | Brand       | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Luckystrike | 7-Eleven Corporation | 572896   | https://aem-stage.luckystrike.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Luckystrike | Murphy               | 741592   | https://aem-stage.luckystrike.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Luckystrike | Sheetz               | 595324   | https://aem-stage.luckystrike.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Luckystrike | Speedway             | 529181   | https://aem-stage.luckystrike.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @LuckystrikeValidateHamburgerMenu_Prod
    Examples: 
      | Brand       | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
      | Luckystrike | 7-Eleven Corporation | 572896   | https://www.luckystrike.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Luckystrike | Murphy               | 741592   | https://www.luckystrike.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Luckystrike | Sheetz               | 595324   | https://www.luckystrike.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Luckystrike | Speedway             | 529181   | https://www.luckystrike.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @LuckystrikeLogoutfromHamburgerMenu
  Scenario Outline: Validate that user can logout from the Luckystrike Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I click on logout link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @LuckystrikeLogoutfromHamburgerMenu_QA    
    Examples: 
      | Brand       | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Luckystrike | 7-Eleven Corporation | 572896   | https://aem-stage.luckystrike.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Luckystrike | Murphy               | 741592   | https://aem-stage.luckystrike.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Luckystrike | Sheetz               | 595324   | https://aem-stage.luckystrike.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Luckystrike | Speedway             | 529181   | https://aem-stage.luckystrike.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @LuckystrikeLogoutfromHamburgerMenu_Prod
    Examples: 
      | Brand       | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
      | Luckystrike | 7-Eleven Corporation | 572896   | https://www.luckystrike.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Luckystrike | Murphy               | 741592   | https://www.luckystrike.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Luckystrike | Sheetz               | 595324   | https://www.luckystrike.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Luckystrike | Speedway             | 529181   | https://www.luckystrike.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @NavigateLuckystrikeMobilesiteHamburgerMenu 
  Scenario Outline: Validate that user can navigate to Luckystrike brand mobile site from the Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
      And I click on the Understood button
      And I navigate to Hamburger menu
      And I click on brand link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @NavigateLuckystrikeMobilesiteHamburgerMenu_QA      
    Examples: 
      | Brand       | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Luckystrike | 7-Eleven Corporation | 572896   | https://aem-stage.luckystrike.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Luckystrike | Murphy               | 741592   | https://aem-stage.luckystrike.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Luckystrike | Sheetz               | 595324   | https://aem-stage.luckystrike.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Luckystrike | Speedway             | 529181   | https://aem-stage.luckystrike.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @NavigateLuckystrikeMobilesiteHamburgerMenu_Prod
    Examples: 
      | Brand       | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
     # | Luckystrike | 7-Eleven Corporation | 572896   | https://www.luckystrike.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
     # | Luckystrike | Murphy               | 741592   | https://www.luckystrike.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Luckystrike | Sheetz               | 595324   | https://www.luckystrike.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      #| Luckystrike | Speedway             | 529181   | https://www.luckystrike.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  
