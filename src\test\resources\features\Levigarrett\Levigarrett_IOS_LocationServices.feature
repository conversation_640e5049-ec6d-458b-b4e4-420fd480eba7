@LevigarrettIOSLocatonInstruction
Feature: Levigarrett SPA -Location Screen
As a RJR user, I Validate the Location setting instruction screen

@LevigarrettIOSLocatonInstruction
  Scenario Outline: Validate the  Location setting instruction screen
     
   
    Given I launch Setting and select Ask first option in Location
      And I'm on the login page IOS for <Brand> with login <URL>
     When I login with valid user id <Username> and password <Password> for <Brand>
      And I navigate to Levigarrett Offers Page
      And I click on redeem button
      #And I set the valid device location to <Loc>
      And I click on the Understood button for IOS
      And I Validate Location Disabled screen and click continue
      And I Validate Location Disabled screen2
       And I Validate Location Disabled screen3
       And I Validate Location Disabled screen4
       And I Validate Location Disabled screen5
       And I Validate Location Disabled screen6tryagain & Back to AEMscreen functionality
@LevigarrettIOSLocatonInstruction_QAstage        	
Examples: 
      | Brand  | URL                                          | Username                 | Password  | Loc                                   |
      | Levigarrett  | https://aem-stage.levigarrett.com/		              | <EMAIL>       | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 

@LevigarrettIOSLocatonInstruction_Prod
Examples: 
      | Brand  | URL                            | Username                                 | Password  | Loc                                   |
      | Levigarrett  |  https://www.levigarrett.com 	 		      | <EMAIL>   | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 
 
 
 @LevigarrettIOSShieldIcon
  Scenario Outline: Validate the Location setting instruction screen
  
     Given I'm on the login page for <Brand> with login <URL> 
     When I login with valid user id <Username> and password <Password> for <Brand>
      And I navigate to Levigarrett Offers Page
      And I click on redeem button
      #And I set the valid device location to <Loc>
      #And I click on the Understood button for IOS
      And I Validate Understood screen shield icon
      
      
@LevigarrettIOSShieldIcon_QAstage        	
Examples: 
      | Brand  | URL                                          | Username                 | Password  | Loc                                   |
      | Levigarrett  |  https://aem-stage.levigarrett.com/			   | <EMAIL>       | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 

@LevigarrettIOSShieldIcon_Prod
Examples: 
      | Brand  | URL                            | Username                                 | Password  | Loc                                   |
      | Levigarrett  | https://www.levigarrett.com 	      | <EMAIL>   | Password1 | 108 Cluff Crossing RD Salem,NH 03079  | 
 