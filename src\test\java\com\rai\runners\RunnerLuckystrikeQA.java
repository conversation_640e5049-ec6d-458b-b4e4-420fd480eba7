package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Luckystrike", glue = { "com.rai.steps" }, tags = "@LuckystrikeValidateContentPostRedemption_QA or "
		+ "@LuckystrikeCouponHome_QAstage or @LuckystrikeCouponSort_QAStage or  @LuckystrikeCouponvalidateRestrictions_QAStage or "
		+ "@LuckystrikeRedeemAtAny0.1MileStore_QA or @LuckystrikeValidateRedeemNotNow_QA or @LuckystrikeValidatefavouriteStore_QA or @LuckystrikeValidateErrorMessageNotnearbyStore_QA or @LuckystrikeMapViewCouponRedemtion_QA or "
		+ "@LuckystrikeValidateHamburgerMenu_QA or @LuckystrikeLogoutfromHamburgerMenu_QA or @NavigateLuckystrikeMobilesiteHamburgerMenu_QA or "
		+ "@LuckystrikeSGWValidations_QA or "
		+ "@LuckystrikeStoreListView_QA or @LuckystrikeStoreListSearchByZip_QA or @LuckystrikeStoreDetailsMapView_QA or @LuckystrikeStoreMapViewbyZip_QA",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerLuckystrikeQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
