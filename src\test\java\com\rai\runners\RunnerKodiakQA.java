package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Kodiak", glue = { "com.rai.steps" }, tags = "@KodiakValidateContentPostRedemption_QA or "
		+ "@KodiakCouponHome_QAstage or @KodiakCouponSort_QAStage or  @KodiakCouponvalidateRestrictions_QAStage or "
		+ "@KodiakRedeemAtAny0.1MileStore_QA or @KodiakValidateRedeemNotNow_QA or @KodiakValidatefavouriteStore_QA or @KodiakValidateErrorMessageNotnearbyStore_QA or @KodiakMapViewCouponRedemtion_QA or "
		+ "@KodiakValidateHamburgerMenu_QA or @KodiakLogoutfromHamburgerMenu_QA or @KodiakNavigateMobilesiteHamburgerMenu_QA or "
		+ "@KodiakSGWValidations_QA or "
		+ "@KodiakStoreListView_QA or @KodiakStoreListSearchByZip_QA or @KodiakStoreDetailsMapView_QA or @KodiakStoreMapViewbyZip_QA",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

// VeloValidateErrorMessageNotnearbyStore_QA
public class RunnerKodiakQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
