Feature: Cougar SPA - Store Details
As a RJR user, The user will be able to view the cougar store list by list and Mapview
 
@CougarStoreListView
  Scenario Outline: Validate that user is able to select a coupons to choose a store and view the store details upto 20 stores for the <Brand> brand & the <Retailer> retailer on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
     Then I click on loadmore button and validate the list

@CougarStoreListView_QA         	
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   						|Env	|
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA			   					| QA	|
      #| Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				|	QA	|

@CougarStoreListView_Prod
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   						|Env	 |
      | Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	|  <EMAIL>				 	| Password1 | 429 N MAIN ST, Elkhart, IN 46516			   					| Prod |
      #| Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	|  <EMAIL>					| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	|  <EMAIL>	   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				| Prod |
 
@CougarStoreListSearchByZip  	
   Scenario Outline: Validate the search By Zip and view the available stores in Store list page for the <Brand> brand & the <Retailer> retailer on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And  I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I do a Search By zip with invalid zip code
     Then I validate the error message for invalid zipcode
      And I do a Search By zip with valid zip code <ZipCode>
      And I select a store from store list page
     Then I validate the error message on coupon redemption
     
@CougarStoreListSearchByZip_QA  
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   						|Env	| ZipCode |
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 721 S Main St, Elkhart, IN 46516			   					| QA	| 46516   |
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 5600 Landers Rd, North Little Rock, AR 72117  		|	QA	| 72117   |
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 818 Corning Way, Martinsburg, WV 25405				    |	QA	| 25405   |

@CougarStoreListSearchByZip_Prod
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   						|Env	 | ZipCode |
      | Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 721 S Main St, Elkhart, IN 46516			   					| Prod | 46516   |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	| <EMAIL>				| Password1 | 5600 Landers Rd, North Little Rock, AR 72117  		| Prod | 72117   |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 818 Corning Way, Martinsburg, WV 25405				    | Prod | 25405   |
 
@CougarStoreDetailsMapView      
   Scenario Outline: Validate the user is able to view the store details in the map view for the <Brand> brand & the <Retailer> retailer on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And  I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I select a store from map view
     Then I validate the user is on Redeem now page

@CougarStoreDetailsMapView_QA
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   						|Env	|
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 2608 California Rd, Elkhart, IN 46514             | QA	|
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 4600 Silver Creek Dr, Sherwood, AR 72120      		|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 5424 Winchester Ave, Martinsburg, WV 25405				|	QA	|

@CougarStoreDetailsMapView_Prod
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   						|Env	 |
      | Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 2608 California Rd, Elkhart, IN 46514			   			| Prod |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 4600 Silver Creek Dr, Sherwood, AR 72120      		| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 5424 Winchester Ave, Martinsburg, WV 25405				| Prod |
       
@CougarStoreMapViewbyZip
 	Scenario Outline: Validate the search By Zip and view the available stores in Store Map view page for the <Brand> brand & the <Retailer> retailer on <Env> Environment
     Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And  I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I do a Search By zip with invalid zip code
     Then I validate the error message for invalid zipcode
      And I do a Search By zip with valid zip code <ZipCode>
      And I select a store from map view
      And I validate the error message on coupon redemption
     
@CougarStoreMapViewbyZip_QA
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   			 | Env | ZipCode |
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 721 S Main St, Elkhart, IN 46516			   		 | QA  | 46516   |
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 5600 Landers Rd, North Little Rock, AR 72117 | QA	 | 72117   |
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 818 Corning Way, Martinsburg, WV 25405       | QA	 | 25405   |

@CougarStoreMapViewbyZip_Prod
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   			 | Env	| ZipCode |
      | Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 721 S Main St, Elkhart, IN 46516             | Prod | 46516   |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 5600 Landers Rd, North Little Rock, AR 72117 | Prod | 72117   |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 818 Corning Way, Martinsburg, WV 25405       | Prod | 25405   |
 