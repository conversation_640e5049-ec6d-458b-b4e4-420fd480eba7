Feature: Cougar SPA - Coupon Hamburger Functionality and validation
As a RJR user, I should be able to validate the Hamburger functionality

@CougarValidateHamburgerMenu
Scenario Outline: Validate Hamburger Menu And the navigation to Coupon Page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
 		Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I validate the elements in Hamburger Menu
      And I click on coupons link
     Then I validate I'm on the Coupons Home Page 

@CougarValidateHamburgerMenu_QA
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   						|Env	|
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA		   					| QA	|
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				|	QA	|

@CougarValidateHamburgerMenu_Prod
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   						|Env	 |
      | Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 429 N MAIN ST, Elkhart, IN 46516			   					| Prod |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	| <EMAIL>				| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				| Prod |
       
@CougarLogoutfromHamburgerMenu
Scenario Outline: Validate that user can logout from the Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
 		Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I click on logout link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>

@CougarLogoutHamburgerMenu_QA      
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   						|Env	|
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA			   					| QA	|
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				|	QA	|

@CougarLogoutfromHamburgerMenu_Prod
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   						|Env	 |
      | Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	|  <EMAIL>			| Password1 | 429 N MAIN ST, Elkhart, IN 46516			   					| Prod |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	|  <EMAIL>			| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	|  <EMAIL>			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				| Prod |
        
@CougarNavigateMobilesiteHamburgerMenu 
 Scenario Outline: Validate that user can navigate to brand mobile site from the Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
	  Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
      And I click on the Understood button
      And I navigate to Hamburger menu
      And I click on brand link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
      
@CougarNavigateMobilesiteHamburgerMenu_QA      
Examples: 
      | Brand   |Retailer							|RBDScode	| URL                                     					| Username              						| Password  | Loc                                   						|Env	|
      | Cougar	| 7-Eleven Corporation|572896		|https://aem-stage.cougardips.com/?RBDSCode=572896	| <EMAIL>				 	| Password1 | 623 East Jackson Boulevard, Elkhart, IN, USA		   					| QA	|
      | Cougar  | Murphy							|741592 	|https://aem-stage.cougardips.com/?RBDSCode=741592	| <EMAIL>					| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		|	QA	|
      | Cougar	| Sheetz							|595324 	|https://aem-stage.cougardips.com/?RBDSCode=595324	| <EMAIL>	   			| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				|	QA	|

@NavigateCougarMobilesiteHamburgerMenu_Prod
Examples:
      | Brand   |Retailer							|RBDScode	| URL                                     		| Username              						| Password  | Loc                                   						|Env	 |
      | Cougar	| 7-Eleven Corporation|572896		|https://www.cougardips.com/?RBDSCode=572896	|  <EMAIL>	| Password1 | 429 N MAIN ST, Elkhart, IN 46516			   					| Prod |
      | Cougar  | Murphy							|741592 	|https://www.cougardips.com/?RBDSCode=741592	|  <EMAIL>	| Password1 | 4448 E MCCAIN BLVD, North Little Rock, AR 72117		| Prod |
      | Cougar	| Sheetz							|595324 	|https://www.cougardips.com/?RBDSCode=595324	|  <EMAIL>	| Password1 | 4430 WINCHESTER AVE, Martinsburg, WV 25405				| Prod |
    