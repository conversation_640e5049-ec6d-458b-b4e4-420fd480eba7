Feature: Pallmall RBDS - Hamburger Menu
As a RJR user, I shall be validating Pallmall Coupon Hamburger Functionality

@PallmallValidateHamburgerMenu
  Scenario Outline: Validate Hamburger Menu And the navigation to Pallmall Coupon Page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I validate the elements in Hamburger Menu
      And I click on coupons link
     Then I validate I'm on the Coupons Home Page  
  
  @PallmallValidateHamburgerMenu_QA
    Examples: 
      | Brand    | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Pallmall | 7-Eleven Corporation | 572896   | https://aem-stage.pallmallusa.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Pallmall | Murphy               | 741592   | https://aem-stage.pallmallusa.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Pallmall | Sheetz               | 595324   | https://aem-stage.pallmallusa.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Pallmall | Speedway             | 529181   | https://aem-stage.pallmallusa.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @PallmallValidateHamburgerMenu_Prod
    Examples: 
      | Brand    | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
      | Pallmall | 7-Eleven Corporation | 572896   | https://www.pallmallusa.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Pallmall | Murphy               | 741592   | https://www.pallmallusa.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Pallmall | Sheetz               | 595324   | https://www.pallmallusa.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Pallmall | Speedway             | 529181   | https://www.pallmallusa.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @PallmallLogoutfromHamburgerMenu
  Scenario Outline: Validate that user can logout from the Pallmall Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I click on logout link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @PallmallLogoutfromHamburgerMenu_QA    
    Examples: 
      | Brand    | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Pallmall | 7-Eleven Corporation | 572896   | https://aem-stage.pallmallusa.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Pallmall | Murphy               | 741592   | https://aem-stage.pallmallusa.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Pallmall | Sheetz               | 595324   | https://aem-stage.pallmallusa.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Pallmall | Speedway             | 529181   | https://aem-stage.pallmallusa.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @PallmallLogoutfromHamburgerMenu_Prod
    Examples: 
      | Brand    | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
      | Pallmall | 7-Eleven Corporation | 572896   | https://www.pallmallusa.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Pallmall | Murphy               | 741592   | https://www.pallmallusa.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Pallmall | Sheetz               | 595324   | https://www.pallmallusa.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Pallmall | Speedway             | 529181   | https://www.pallmallusa.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @NavigatePallmallMobilesiteHamburgerMenu 
  Scenario Outline: Validate that user can navigate to Pallmall brand mobile site from the Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
      And I click on the Understood button
      And I navigate to Hamburger menu
      And I click on brand link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @NavigatePallmallMobilesiteHamburgerMenu_QA      
    Examples: 
      | Brand    | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                            | Env | 
      | Pallmall | 7-Eleven Corporation | 572896   | https://aem-stage.pallmallusa.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Pallmall | Murphy               | 741592   | https://aem-stage.pallmallusa.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | Pallmall | Sheetz               | 595324   | https://aem-stage.pallmallusa.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Pallmall | Speedway             | 529181   | https://aem-stage.pallmallusa.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @NavigatePallmallMobilesiteHamburgerMenu_Prod
    Examples: 
      | Brand    | Retailer             | RBDScode | URL                                    | Username                | Password  | Loc                                            | Env  | 
      | Pallmall | 7-Eleven Corporation | 572896   | https://www.pallmallusa.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Pallmall | Murphy               | 741592   | https://www.pallmallusa.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Pallmall | Sheetz               | 595324   | https://www.pallmallusa.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Pallmall | Speedway             | 529181   | https://www.pallmallusa.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  
