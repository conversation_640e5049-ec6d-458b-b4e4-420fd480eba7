/*
 *  © [2020] Cognizant. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.rai.steps;

import java.io.IOException;

import org.openqa.selenium.JavascriptExecutor;

import com.rai.framework.DriverManager;
import com.rai.framework.TestHarness;

import io.cucumber.java.Scenario;
import io.cucumber.java.After;
import io.cucumber.java.Before;

public class CukeHooks extends MasterSteps {

	private TestHarness testHarness;

	@Before
	public void setUp(Scenario scenario) {
		testHarness = new TestHarness();
		DriverManager.getTestParameters().setScenario(scenario);
		testHarness.invokeDriver(scenario);
		System.out.println("Scenario:" + scenario.getName());
		 ((JavascriptExecutor) DriverManager.getAppiumDriver()).executeScript("sauce:job-name=" + scenario.getName());
		System.out.println("New Thread with ID:" + Thread.currentThread().threadId());

	}

	@After
	public void tearDown(Scenario scenario) throws IOException {
		
		try {
			if (DriverManager.getAppiumDriver() != null) {
				try {
					DriverManager.getAppiumDriver().getCurrentUrl();

					if (scenario.isFailed()) {
						System.out.println("Test cases failed");
						((JavascriptExecutor) DriverManager.getAppiumDriver()).executeScript("sauce:job-result=failed");
					} else {
						System.out.println("Test cases Passed");
						((JavascriptExecutor) DriverManager.getAppiumDriver()).executeScript("sauce:job-result=passed");
					}
				} catch (Exception driverException) {
					System.out.println("Driver session is no longer active, cannot update Sauce Labs status: " + driverException.getMessage());
					if (scenario.isFailed()) {
						System.out.println("Test Result: FAILED (could not update Sauce Labs)");
					} else {
						System.out.println("Test Result: PASSED (could not update Sauce Labs)");
					}
				}
			} else {
				System.out.println("Driver is null, cannot update Sauce Labs status");
			}
		} catch (Exception e) {
			System.out.println("Error occurred during tearDown: " + e.getMessage());
			e.printStackTrace();
		} finally {
			try {
				testHarness.closeRespestiveDriver(scenario);
			} catch (Exception closeException) {
				System.out.println("Error occurred while closing driver: " + closeException.getMessage());
			}
		}

	}

}
