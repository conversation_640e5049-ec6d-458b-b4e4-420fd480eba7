Feature: Kodiak RBDS- Coupon Redemption at different retailers using the RBDS url
As an ATC, I should be able to successfully use the RBDS url for a particular retailer and Redeem a coupon

@KodiakRedeemAtAny0.1MileStore
  Scenario Outline: Validate the user is able to redeem the coupon from RBDS site when he is at a distance of .1 miles for the retailer <Retailer> with RBDS code <RBDScode>  for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
        And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
      And I select a coupon and click on choose a store button
      And I select a store from store list page
      And I click on the Redeem Now button
     Then I validate the timer running
      And I click on the I'm Done button
  
  @KodiakRedeemAtAny0.1MileStore_QA  
     Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           						| Env | 
      #| Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 9010 Broadway templeCity,CA 91780     	| QA  | 
  #	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 4951 E Bridge St, Brighton, CO 80601			| QA	|
      #| Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 16420 National Pike Hagerstown, MD 21740 	| QA 	| 
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 3200 S Cicero Ave, Cicero, IL 60804       | QA 	| 
  
  @KodiakValidateRedeemNotNow
  Scenario Outline: Validate that no coupon is redeemed when the user clicks on the Not Now button on the redemption page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
        And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
      And I select a coupon and click on choose a store button
      And I select a store from store list page
      And I click on the Not Now button
     Then I validate I'm on the Coupons Home Page
  
  @KodiakValidateRedeemNotNow_QA 		    
  Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           						| Env | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 805 W Las Tunas Dr San Gabriel, CA 91776	| QA  | 
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 4951 E Bridge St, Brighton, CO 80601			| QA	|
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 16420 National Pike Hagerstown, MD 21740 	| QA 	| 
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 3200 S Cicero Ave, Cicero, IL 60804       | QA 	| 
  
  @KodiakValidateRedeemNotNow_Prod
    Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						| Env  | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 805 W Las Tunas Dr San Gabriel, CA 91776	| Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 4951 E Bridge St, Brighton, CO 80601    	| Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 16420 National Pike Hagerstown, MD 21740	| Prod | 
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 3200 S Cicero Ave, Cicero, IL 60804     	| Prod | 
 
  @KodiakValidatefavouriteStore
  Scenario Outline:Validate the favorite store selection in Redeem Now page and its update in Store list page  for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>   
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>     
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
       And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I select a coupon and click on choose a store button
      And I select a store from store list page
      And I mark the store as favorite
     Then I navigate to Store List and validate the favoritestore
  
  @KodiakValidatefavouriteStore_QA      	
    Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           						| Env | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 9010 E Broadway, Temple City, CA 91780  	| QA  | 
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 7101 Tower Rd, Denver, CO 80249     			| QA	|
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 17803 Venture Dr, Hagerstown, MD 21740  	| QA 	| 
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 6800 S Archer Rd, Bedford Park, IL 60501  | QA 	| 
  
  @KodiakValidatefavouriteStore_Prod
    Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						| Env  | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 9010 E Broadway, Temple City, CA 91780  	| Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 7101 Tower Rd, Denver, CO 80249         	| Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 17803 Venture Dr, Hagerstown, MD 21740  	| Prod | 
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 6800 S Archer Rd, Bedford Park, IL 60501  | Prod | 
 
  @KodiakValidateErrorMessageNotnearbyStore
  Scenario Outline: Validate the error message when user tries to redeem a coupon from Store List View when is not nearby the store for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>       
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I select a store from store list page
     Then I validate the error message on coupon redemption
  
  @KodiakValidateErrorMessageNotnearbyStore_QA     
    Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           								| Env | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801       			| QA  | 
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601	| QA	|
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 11915 Heather Dr, Hagerstown, MD 21740 				| QA 	| 
      #| Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 4950 W Pershing Rd, Cicero, IL 60804       		| QA 	| 
  
  @KodiakValidateErrorMessageNotnearbyStore_Prod
    Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						| Env  | 
      #| Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801 	| Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601    	| Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 11915 Heather Dr, Hagerstown, MD 21740 	| Prod | 
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 4950 W Pershing Rd, Cicero, IL 60804      	| Prod | 
 
 
	@KodiakMapViewCouponRedemtion     
   Scenario Outline: Validate the error message when user tries to redeem a coupon from Store MAP View when is not nearby the store for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc> with restricted state
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
       And I navigate to Kodiak Offers Page
      And I click on Get Offer button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I select a store from map view
     Then I validate the error message on coupon redemption
      
 @KodiakMapViewCouponRedemtion_QA     
 Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          				| Username                         	| Password  | Loc                           							| Env | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://aem-stage.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801						| QA  | 
  	  | Kodiak | Murphy							  | 741592 		| https://aem-stage.kodiakspirit.com/?RBDSCode=741592	| <EMAIL>	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601| QA	|
      | Kodiak | Sheetz   						| 595324 		| https://aem-stage.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 						| Password1 | 11915 Heather Dr, Hagerstown, MD 21740 			| QA 	| 
      | Kodiak | Speedway 						| 529181 		| https://aem-stage.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 						| Password1 | 4950 W Pershing Rd, Cicero, IL 60804       	| QA 	| 
  
 @KodiakMapViewCouponRedemtion_PROD 
    Examples: 
      | Brand  | Retailer             | RBDScode 	| URL                                          	| Username                         	| Password  | Loc                           						   | Env  | 
      | Kodiak | 7-Eleven Corporation | 572896   	| https://www.kodiakspirit.com/?RBDSCode=572896 | <EMAIL> 	| Password1 | 640 E Main St, Alhambra, CA 91801	           | Prod | 
      | Kodiak | Murphy               | 741592   	| https://www.kodiakspirit.com/?RBDSCode=741592 | <EMAIL> 	| Password1 | 2133 Prairie Center Pkwy, Brighton, CO 80601 | Prod | 
      | Kodiak | Sheetz               | 595324   	| https://www.kodiakspirit.com/?RBDSCode=595324 | <EMAIL> 	| Password1 | 11915 Heather Dr, Hagerstown, MD 21740	     | Prod | 
      | Kodiak | Speedway             | 529181   	| https://www.kodiakspirit.com/?RBDSCode=529181 | <EMAIL> 	| Password1 | 4950 W Pershing Rd, Cicero, IL 60804         | Prod | 
 
  