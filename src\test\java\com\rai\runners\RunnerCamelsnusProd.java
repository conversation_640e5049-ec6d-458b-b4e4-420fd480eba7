package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Camelsnus", glue = { "com.rai.steps" }, tags = "@CamelsnusCouponHome_Prod or @CamelsnusCouponSort_Prod or @CamelsnusCouponvalidateRestrictions_Prod or "
		+ "@CamelsnusValidateRedeemNotNow_Prod or @CamelsnusValidatefavouriteStore_Prod or @CamelsnusValidateErrorMessageNotnearbyStore_Prod or @CamelsnusMapViewCouponRedemtion_Prod or "
		+ "@NavigateCamelsnusMobilesiteHamburgerMenu_Prod or @CamelsnusLogoutfromHamburgerMenu_Prod or @CamelsnusValidateHamburgerMenu_Prod or "
		+ "@CamelsnusSGWValidations_Prod or "
		+ "@CamelsnusStoreListView_Prod or @CamelsnusStoreListSearchByZip_Prod or @CamelsnusStoreDetailsMapView_Prod or @CamelsnusStoreMapViewbyZip_Prod",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerCamelsnusProd extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
