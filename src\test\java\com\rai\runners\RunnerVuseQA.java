package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Vuse", glue = { "com.rai.steps" }, tags = // "@VuseValidateContentPostRedemption_QA
																									// or "
"@VuseCouponHome_QAstage or @VuseCouponSort_QAStage or  @VuseCouponvalidateRestrictions_QAStage or "
		+ "@VuseRedeemAtAny0.1MileStore_QA or @VuseValidateRedeemNotNow_QA or @VuseValidatefavouriteStore_QA or @VuseValidateErrorMessageNotnearbyStore_QA or @VuseMapViewCouponRedemtion_QA or "
		+ "@VuseValidateHamburgerMenu_QA or @VuseLogoutfromHamburgerMenu_QA or @NavigateVuseMobilesiteHamburgerMenu_QA or "
		+ "@VuseSGWValidations_QA or "
		+ "@VuseStoreListView_QA or @VuseStoreListSearchByZip_QA or @VuseStoreDetailsMapView_QA or @VuseStoreMapViewbyZip_QA", monochrome = true, plugin = {
				"pretty",
				"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
				"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
				"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

// VeloValidateErrorMessageNotnearbyStore_QA
public class RunnerVuseQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
