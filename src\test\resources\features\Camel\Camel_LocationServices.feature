@CamelLocationServices
Feature: Camel SPA - Location Services
  As a RJR user, I should know how to turn on the location services on my device so that I can follow the instruction and turn the location services

  @CamelAccessToBrowserAndDeviceLocation
  Scenario Outline: Validate user is able to navigate to coupon page after click on Understood button when the application has access to the browser location and device location
    #Given I'm on the login page for <Brand> with login <URL>
    #And I set the valid device location to <Loc>
    #When I login with valid user id <Username> and password <Password> for <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
    And I set the valid device location to <Loc>
    When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I click on the Understood button location service
 
    And I click on the Learn how 
    And I click on the continue button
    And I Navigate to Chrome App Loc
    And I Navigate to Website Loc
    And I Naviagte to Clearing cache
    Then I click the back to the camel page
    
    
    
  

    @CamelAccessToBrowserAndDeviceLocation_QA
    Examples: 
      | Brand | URL                         | Username                    | Password  | Loc                                  |
      | CAMEL | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 108 Cluff Crossing RD Salem,NH 03079 |

    @CamelAccessToBrowserAndDeviceLocation_Prod
    Examples: 
      | Brand | URL                   | Username                               | Password  | Loc                                  |
      | CAMEL | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 108 Cluff Crossing RD Salem,NH 03079 |
