package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Americanspirit", glue = { "com.rai.steps" }, tags = "@NASCIGSCouponHome_Prod or @NASCIGSCouponSort_Prod or @NASCIGSCouponvalidateRestrictions_Prod or "
		+ "@NASCIGSValidateRedeemNotNow_Prod or @NASCIGSValidatefavouriteStore_Prod or @NASCIGSValidateErrorMessageNotnearbyStore_Prod or @NASCIGSMapViewCouponRedemtion_Prod or "
		+ "@NavigateNASCIGSMobilesiteHamburgerMenu_Prod or @NASCIGSLogoutfromHamburgerMenu_Prod or @NASCIGSValidateHamburgerMenu_Prod or "
		+ "@NASCIGSSGWValidations_Prod or "
		+ "@NASCIGSStoreListView_Prod or @NASCIGSStoreDetailsMapView_Prod",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerAmericanspiritProd extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
