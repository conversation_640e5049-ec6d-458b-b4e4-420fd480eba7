/*
 *  © [2020] Cognizant. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.rai.framework;

public enum MobileExecutionPlatform {

	/**
	 * Execute on an Android Device
	 */
	ANDROID,

	/**
	 * Execute on an iOS Device
	 */
	IOS,
	/**
	 * Execute on a browser in Android Device
	 */
	WEB_ANDROID,

	/**
	 * Execute on browser in iOS Device
	 */
	WEB_IOS,
	
	/**
	 * Execute on Android Virtual Device
	 */
	VIRTUAL_ANDROID,
	
	/**
	 * Execute on browser in Android Virtual Device
	 */
	VIRTUAL_WEB_ANDROID,
	
	/**
	 * Execute on IOS Virtual Device
	 */
	VIRTUAL_IOS,
	
	/**
	 * Execute on browser in IOS Virtual Device
	 */
	VIRTUAL_WEB_IOS,
	
}
