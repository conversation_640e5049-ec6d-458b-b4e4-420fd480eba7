package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Camel", glue = { "com.rai.steps" }, tags = "@CamelCouponHome_Prod or @CamelCouponSort_Prod or @CamelCouponvalidateRestrictions_Prod or "
		+ "@CamelValidateRedeemNotNow_Prod or @CamelValidatefavouriteStore_Prod or @CamelValidateErrorMessageNotnearbyStore_Prod or @CamelMapViewCouponRedemtion_Prod or "
		+ "@NavigateCamelMobilesiteHamburgerMenu_Prod or @CamelLogoutfromHamburgerMenu_Prod or @CamelValidateHamburgerMenu_Prod or "
		+ "@CamelSGWValidations_Prod or "
		+ "@CamelStoreListView_Prod or @CamelStoreListSearchByZip_Prod or @CamelStoreDetailsMapView_Prod or @CamelStoreMapViewbyZip_Prod", monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerCamelProd extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
