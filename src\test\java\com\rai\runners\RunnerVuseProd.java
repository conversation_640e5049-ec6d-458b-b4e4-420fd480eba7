package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Vuse", glue = { "com.rai.steps" }, tags = "@VuseCouponHome_Prod or @VuseCouponSort_Prod or @VuseCouponvalidateRestrictions_Prod or "
		+ "@VuseValidateRedeemNotNow_Prod or @VuseValidatefavouriteStore_Prod or @VuseValidateErrorMessageNotnearbyStore_Prod or @VuseMapViewCouponRedemtion_Prod or "
		//+ "@VuseCouponSortByValue_Prod or "
		+ "@NavigateVuseMobilesiteHamburgerMenu_Prod or @VuseLogoutfromHamburgerMenu_Prod or @VuseValidateHamburgerMenu_Prod or "
		//+ "@VuseAccessToBrowserAndDeviceLocation_Prod or "
		+ "@VuseSGWValidations_Prod or "
		+ "@VuseStoreListView_Prod or @VuseStoreListSearchByZip_Prod or @VuseStoreDetailsMapView_Prod or @VuseStoreMapViewbyZip_Prod",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

// VeloValidateErrorMessageNotnearbyStore_QA
public class RunnerVuseProd extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
