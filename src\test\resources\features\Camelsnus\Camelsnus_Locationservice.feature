Feature: cougar SPA - Location Services
  As a RJR user, I should know how to turn on the location services on my device so that I can follow the instruction and turn the location services

  @cougarAccessToBrowserAndDeviceLocation
  Scenario Outline: Validate user is able to navigate to coupon page after click on Understood button when the application has access to the browser location and device location
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
       And I click on the Understood button location service
   And I click on the Learn how 
    And I click on the continue button
    And I Navigate to Chrome App Loc
    And I Navigate to Website Loc
    And I Naviagte to Clearing cache
    Then I click the back to the camelsnus page
    
    
    	Examples: 

          | Brand  | Retailer						  | RBDScode | URL                                     					  | Username              						| Password  | Loc                                   					| ExpectedTile1Text | ExpectedTile2Text		 |Env |
#		     | Cougar | 7-Eleven Corporation | 572896	 | https://aem-stage.cougardips.com/?RBDSCode=595324	|   <EMAIL> 	| Password1 |   623 East Jackson Boulevard, Elkhart, IN, USA| SEVEN    | SHOW YOUR						 |QA  |
		    
         | Camelsnus | Murphy               | 741592   | https://aem-stage.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 79 E Bromley Ln, Brighton, CO 80601						 | https://aem-stage.camelsnus.com/secure/products/mellow.html  | https://aem-stage.camelsnus.com/secure/products/frost-large.html | QA  | 
    