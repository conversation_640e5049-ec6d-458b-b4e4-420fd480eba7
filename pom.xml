<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>cognizant</groupId>
	<artifactId>cucumberDriven-maven-testng</artifactId>
	<version>2.0</version>
	<packaging>jar</packaging>

	<name>Cucumber Driven Maven TestNG Framework</name>
	<description>Cucumber BDD Test Automation Framework with Selenium and Appium using Java 21 LTS</description>

	<properties>
		<!-- Java 21 LTS Configuration -->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven.compiler.source>21</maven.compiler.source>
		<maven.compiler.target>21</maven.compiler.target>
		<maven.compiler.release>21</maven.compiler.release>

		<!-- Plugin Versions -->
		<maven-compiler-plugin.version>3.14.0</maven-compiler-plugin.version>
		<maven-surefire-plugin.version>3.5.2</maven-surefire-plugin.version>
		<maven-clean-plugin.version>3.4.0</maven-clean-plugin.version>
		<maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
		<owasp-dependency-check.version>12.1.0</owasp-dependency-check.version>

		<!-- Core Framework Versions - Java 21 LTS Compatible -->
		<!-- <cucumber.version>7.20.2</cucumber.version> -->
		<testng.version>7.10.2</testng.version>
		<selenium.version>4.34.0</selenium.version>
		<appium.version>9.5.0</appium.version>
		<!-- <extent-reports-cucumber.version>1.15.0</extent-reports-cucumber.version> -->
		<webdrivermanager.version>5.9.2</webdrivermanager.version>
		
		<!-- Logging Dependencies -->
		<log4j.version>2.24.1</log4j.version>
		<slf4j.version>2.0.16</slf4j.version>
		
		<!-- Google Cloud & API Dependencies -->
		<google-cloud-vision.version>3.49.0</google-cloud-vision.version>
		<google-api-client.version>2.7.0</google-api-client.version>
		<google-oauth-client.version>1.36.0</google-oauth-client.version>
		<google-maps-services.version>2.2.0</google-maps-services.version>
		<grpc-netty-shaded.version>1.69.0</grpc-netty-shaded.version>
		
		<!-- Utility Dependencies -->
		<jackson.version>2.18.2</jackson.version>
		<json.version>20240303</json.version>
		<!-- <json-simple.version>4.0.1</json-simple.version> -->
		<rest-assured.version>5.5.0</rest-assured.version>
		<lombok.version>1.18.34</lombok.version>
		<javafaker.version>1.0.2</javafaker.version>
		<selenium-shutterbug.version>1.6</selenium-shutterbug.version>
		<opencv.version>4.9.0-0</opencv.version>
		<mssql-jdbc.version>12.8.1.jre11</mssql-jdbc.version>
		<jakarta-mail.version>2.0.1</jakarta-mail.version>
		<saucerest.version>2.3.0</saucerest.version>
	</properties>

	<!-- Dependency Management for Version Control -->
	<dependencyManagement>
		<dependencies>
			<!-- Jackson BOM for consistent versions -->
			<dependency>
				<groupId>com.fasterxml.jackson</groupId>
				<artifactId>jackson-bom</artifactId>
				<version>${jackson.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			
			<!-- Google Cloud Libraries BOM -->
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>libraries-bom</artifactId>
				<version>26.49.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

		<profiles>
		<profile>
			<id>runTestNGSuite</id>
			<properties>
				<testNG.suiteXmlFile>${testngXml}</testNG.suiteXmlFile>
				<testngXmlDir>src/test/resources/TestNG Xml</testngXmlDir>
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-clean-plugin</artifactId>
						<version>${maven-clean-plugin.version}</version>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-compiler-plugin</artifactId>
						<version>${maven-compiler-plugin.version}</version>
						<configuration>
							<encoding>UTF-8</encoding>
							<source>${maven.compiler.source}</source>
							<target>${maven.compiler.target}</target>
							<release>${maven.compiler.release}</release>
							<compilerArgs>
								<!-- Removed -enable-preview for Java 21 LTS stability -->
							</compilerArgs>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-surefire-plugin</artifactId>
						<version>${maven-surefire-plugin.version}</version>
						<configuration>
							<suiteXmlFiles>
								<suiteXmlFile>${testngXmlDir}/${testNG.suiteXmlFile}</suiteXmlFile>
							</suiteXmlFiles>
							<!-- Removed -enable-preview for Java 21 LTS stability -->
						</configuration>
					</plugin>

					<!-- OWASP Dependency Check Plugin -->
					<plugin>
						<groupId>org.owasp</groupId>
						<artifactId>dependency-check-maven</artifactId>
						<version>${owasp-dependency-check.version}</version>
						<configuration>
							<failBuildOnCVSS>7</failBuildOnCVSS>
							<skipProvidedScope>true</skipProvidedScope>
						</configuration>
						<executions>
							<execution>
								<goals>
									<goal>check</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<dependencies>
		<!-- Cucumber Dependencies -->
		<dependency>
			   <groupId>io.cucumber</groupId>
			   <artifactId>cucumber-testng</artifactId>
			   <version>7.14.0</version>
		</dependency>

		<dependency>
			   <groupId>io.cucumber</groupId>
			   <artifactId>cucumber-java</artifactId>
			   <version>7.14.0</version>
		</dependency>

		<!-- TestNG Dependency -->
		<dependency>
			<groupId>org.testng</groupId>
			<artifactId>testng</artifactId>
			<version>${testng.version}</version>
		</dependency>

		<!-- Selenium Dependencies -->
		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-java</artifactId>
			<version>${selenium.version}</version>
		</dependency>

		<dependency>
			<groupId>io.github.bonigarcia</groupId>
			<artifactId>webdrivermanager</artifactId>
			<version>${webdrivermanager.version}</version>
		</dependency>

		<!-- Appium Dependencies -->
		<dependency>
			<groupId>io.appium</groupId>
			<artifactId>java-client</artifactId>
			<version>${appium.version}</version>
		</dependency>

		<!-- Extent Report Dependency (Updated for Cucumber 7) -->
		<dependency>
			   <groupId>tech.grasshopper</groupId>
			   <artifactId>extentreports-cucumber7-adapter</artifactId>
			   <version>1.14.0</version>
		</dependency>

		<!-- Logging Dependencies - Fixed Log4j Issues -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>${log4j.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>${log4j.version}</version>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-simple</artifactId>
			<version>${slf4j.version}</version>
			<scope>test</scope>
		</dependency>

		<!-- Google Cloud Vision -->
		<dependency>
			<groupId>com.google.cloud</groupId>
			<artifactId>google-cloud-vision</artifactId>
			<version>${google-cloud-vision.version}</version>
		</dependency>

		<!-- gRPC Netty Shaded -->
		<dependency>
			<groupId>io.grpc</groupId>
			<artifactId>grpc-netty-shaded</artifactId>
			<version>${grpc-netty-shaded.version}</version>
		</dependency>

		<!-- Google API Client Libraries -->
		<dependency>
			<groupId>com.google.api-client</groupId>
			<artifactId>google-api-client</artifactId>
			<version>${google-api-client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.google.oauth-client</groupId>
			<artifactId>google-oauth-client</artifactId>
			<version>${google-oauth-client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.google.oauth-client</groupId>
			<artifactId>google-oauth-client-jetty</artifactId>
			<version>${google-oauth-client.version}</version>
		</dependency>

		<!-- Google APIs Services -->
		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-storage</artifactId>
			<version>v1-rev171-1.25.0</version>
		</dependency>

		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-oauth2</artifactId>
			<version>v2-rev20200213-2.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-gmail</artifactId>
			<version>v1-rev110-1.25.0</version>
		</dependency>


		<!-- Google Maps Services -->
		<dependency>
			<groupId>com.google.maps</groupId>
			<artifactId>google-maps-services</artifactId>
			<version>${google-maps-services.version}</version>
		</dependency>

		<!-- SauceLabs REST API -->
		<dependency>
			<groupId>com.saucelabs</groupId>
			<artifactId>saucerest</artifactId>
			<version>${saucerest.version}</version>
		</dependency>

		<!-- REST Assured -->
		<dependency>
			<groupId>io.rest-assured</groupId>
			<artifactId>rest-assured</artifactId>
			<version>${rest-assured.version}</version>
		</dependency>

		<!-- JSON Libraries -->
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>${json.version}</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<!-- Version managed by BOM -->
		</dependency>

		<dependency>
			   <groupId>com.googlecode.json-simple</groupId>
			   <artifactId>json-simple</artifactId>
			   <version>1.1.1</version>
		</dependency>

		<!-- Jakarta Mail (replaces javax.mail) -->
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>jakarta.mail</artifactId>
			<version>${jakarta-mail.version}</version>
		</dependency>

		<!-- Selenium Shutterbug for Screenshots -->
		<dependency>
			<groupId>com.assertthat</groupId>
			<artifactId>selenium-shutterbug</artifactId>
			<version>${selenium-shutterbug.version}</version>
		</dependency>

		<!-- OpenCV -->
		<dependency>
			<groupId>org.openpnp</groupId>
			<artifactId>opencv</artifactId>
			<version>${opencv.version}</version>
		</dependency>

		<!-- Microsoft SQL Server JDBC Driver -->
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
			<version>${mssql-jdbc.version}</version>
		</dependency>

		<!-- Lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- JavaFaker for Test Data Generation -->
		<dependency>
			<groupId>com.github.javafaker</groupId>
			<artifactId>javafaker</artifactId>
			<version>${javafaker.version}</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<!-- Maven Resources Plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>${maven-resources-plugin.version}</version>
			</plugin>
		</plugins>
	</build>
</project>