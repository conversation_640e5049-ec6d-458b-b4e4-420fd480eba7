/*
 *  © [2020] Cognizant. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.rai.framework;
import java.util.Properties;
import org.openqa.selenium.WebDriver;
import io.appium.java_client.AppiumDriver;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * DriverFactory which will create respective driver Object
 * 
 * <AUTHOR>
 */
public class DriverFactory {
	static Logger log = LogManager.getLogger(DriverFactory.class);
	private static Properties properties = Settings.getInstance();

	
	public static WebDriver createWebDriverInstance(SeleniumTestParameters testParameters) {
		WebDriver driver = null;
		try {
			switch (testParameters.getExecutionMode()) {

			case LOCAL:
				driver = WebDriverFactory.getWebDriver(testParameters.getBrowser());
				break;

			case GRID:
				driver = WebDriverFactory.getRemoteWebDriver(testParameters.getBrowser(),
						testParameters.getBrowserVersion(), testParameters.getPlatform(),
						properties.getProperty("RemoteUrl"));
				break;
			
			case SAUCEDESKTOP:

				driver = SauceDriverFactory.getSauceRemoteWebDriverForDesktop(testParameters);
				break;
			
			default:
				throw new Exception("Unhandled Execution Mode!");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			log.error(ex.getMessage());
		}
		return driver;
	}

	
	
	public static AppiumDriver createAppiumInstance(SeleniumTestParameters testParameters,String ScenarioName) {

		AppiumDriver driver = null;
		try {
			switch (testParameters.getExecutionMode()) {

			case MOBILE:

				driver = AppiumDriverFactory.getAppiumDriver(testParameters);
				break;
			
			case SAUCEMOBILE:

				driver = SauceDriverFactory.getSauceAppiumDriver(testParameters,ScenarioName);
				break;
			default:
				throw new Exception("Unhandled Execution Mode!");
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			log.error(ex.getMessage());
		}
		return driver;
	}
}