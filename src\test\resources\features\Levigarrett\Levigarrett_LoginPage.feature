Feature: @<PERSON><PERSON>rettSGW
As a RJR user, I should be able to validate the copyright text for Levigarrett RBDS. 

@Levigarrett_CopyrightTextinLoginPage
Scenario Outline: Validate the user is able to view the Copyright text in Levigarrett login page 
	 Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
	 And I scroll down and validate copyright text <copyrightText> for <Brand>
	 
	 
	 @Levigarrett_CopyrightTextinLoginPage_QA
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | Levigarrett | 7-Eleven Corporation | 572896   | https://aem-stage.levigarrett.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | Levigarrett | Murphy               | 741592   | https://aem-stage.levigarrett.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | Levigarrett | Sheetz               | 595324   | https://aem-stage.levigarrett.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | Levigarrett | Speedway             | 529181   | https://aem-stage.levigarrett.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
		
	 
	 @Levigarrett_CopyrightTextinLoginPage_Prod
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | Levigarrett | 7-Eleven Corporation | 572896   | https://login.levigarrettt.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | Levigarrett | Murphy               | 741592   | https://login.levigarrettt.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | Levigarrett | Sheetz               | 595324   | https://login.levigarrettt.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | Levigarrett | Speedway             | 529181   | https://login.levigarrettt.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
	