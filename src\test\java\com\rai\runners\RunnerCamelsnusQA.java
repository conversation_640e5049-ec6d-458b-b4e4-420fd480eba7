package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Camelsnus", glue = { "com.rai.steps" }, tags = "@CamelsnusValidateContentPostRedemption_QA or "
		+ "@CamelsnusCouponHome_QAstage or @CamelsnusCouponSort_QAStage or  @CamelsnusCouponvalidateRestrictions_QAStage or "
		+ "@CamelsnusRedeemAtAny0.1MileStore_QA or @CamelsnusValidateRedeemNotNow_QA or @CamelsnusValidatefavouriteStore_QA or @CamelsnusValidateErrorMessageNotnearbyStore_QA or @CamelsnusMapViewCouponRedemtion_QA or "
		+ "@CamelsnusValidateHamburgerMenu_QA or @CamelsnusLogoutfromHamburgerMenu_QA or @NavigateCamelsnusMobilesiteHamburgerMenu_QA or "
		+ "@CamelsnusSGWValidations_QA or "
		+ "@CamelsnusStoreListView_QA or @CamelsnusStoreListSearchByZip_QA or @CamelsnusStoreDetailsMapView_QA or @CamelsnusStoreMapViewbyZip_QA",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerCamelsnusQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
