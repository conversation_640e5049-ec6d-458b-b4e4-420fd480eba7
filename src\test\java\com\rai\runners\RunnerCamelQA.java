package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;
@CucumberOptions(features = "src/test/resources/features/Camel", glue = { "com.rai.steps" }, tags = "@CamelValidateContentPostRedemption_QA or "
		+ "@CamelCouponHome_QAstage or @CamelCouponSort_QAStage or  @CamelCouponvalidateRestrictions_QAStage or "
		+ "@CamelRedeemAtAny0.1MileStore_QA or @CamelValidateRedeemNotNow_QA or @CamelValidatefavouriteStore_QA or @CamelValidateErrorMessageNotnearbyStore_QA or @CamelMapViewCouponRedemtion_QA or "
		+ "@CamelValidateHamburgerMenu_QA or @CamelLogoutfromHamburgerMenu_QA or @NavigateCamelMobilesiteHamburgerMenu_QA or "
		+ "@CamelSGWValidations_QA or "
		+ "@CamelStoreListView_QA or @CamelStoreListSearchByZip_QA or @CamelStoreDetailsMapView_QA or @CamelStoreMapViewbyZip_QA or @CamelSnusOnCamelCrossBrand_QA",  monochrome = true, plugin = { "pretty",

//@CucumberOptions(features = "src/test/resources/features/Camel", glue = { "com.rai.steps" }, tags = {"@CamelValidateContentPostRedemption"},  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerCamelQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
