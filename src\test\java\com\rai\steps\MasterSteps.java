/*
 *  © [2020] Cognizant. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.rai.steps;

import static org.testng.Assert.fail;
import java.lang.Math;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.InvocationTargetException;
import java.sql.ResultSet;
import java.time.Duration;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Properties;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.LongStream;

import javax.imageio.ImageIO;
import org.openqa.selenium.Keys;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.opencv.core.Mat;
import static org.opencv.imgcodecs.Imgcodecs.imread;
import org.openqa.selenium.By;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.Platform;
import org.openqa.selenium.Point;
import org.openqa.selenium.StaleElementReferenceException;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.interactions.PointerInput;
import org.openqa.selenium.interactions.Sequence;
import org.openqa.selenium.remote.DriverCommand;
import org.openqa.selenium.remote.RemoteExecuteMethod;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.support.ui.ExpectedCondition;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.Select;
import org.openqa.selenium.support.ui.Wait;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.Assert;
import com.assertthat.selenium_shutterbug.core.ElementSnapshot;
import com.assertthat.selenium_shutterbug.core.Shutterbug;
import com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter;
import com.google.common.collect.ImmutableMap;
import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.errors.ApiException;
import com.google.maps.model.GeocodingResult;
import com.rai.framework.CucumberException;
import com.rai.framework.DatabaseConnection;
import com.rai.framework.DetectTextGoogleAPI;
import com.rai.framework.DriverManager;
import com.rai.framework.ImageOperations;
import com.rai.framework.Settings;
import com.rai.framework.Util;
import com.rai.pages.ChromeBrowserObjects;
import com.rai.pages.HomePageObjects;
import com.rai.pages.LocationServices;
import com.rai.pages.LoginPageObjects;

import io.appium.java_client.AppiumBy;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.TouchAction;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.touch.offset.PointOption;
import io.restassured.RestAssured;
import io.restassured.authentication.PreemptiveBasicAuthScheme;

public class MasterSteps {

	protected static Properties properties = Settings.getInstance();
	static String SRC_PATH = properties.getProperty("SGWImagesPath");
	private static final String DATE_FORMAT = "dd MMMM yyyy";
	private static final String DAY_FIRST = "01";
	private static final String SPACE = " ";
	private static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern(DATE_FORMAT);
	WebElement curDate = null;
	public static int postCountInDashboard;
	public static String failureMessage;
	public static ThreadLocal<String> location = new ThreadLocal<String>();
	public static ThreadLocal<String> env = new ThreadLocal<String>();
	public static ThreadLocal<String> brand = new ThreadLocal<String>();
	public static ThreadLocal<String> tagname = new ThreadLocal<String>();
	AppiumDriver driver = DriverManager.getAppiumDriver();
	static {
		System.load(System.getProperty("user.dir") + properties.getProperty("OpenCvDllFilePath"));
	}

	// Reusable Functions to Take ScreenShots

	/**
	 * Function to take screenshot & attach it to Cucumber Default Reports & Extent
	 * for Web Application with WebDriver
	 * 
	 */

	protected void attachScreenshotForWeb() {

		try {
			DriverManager.getTestParameters().getScenario().attach(Util.takeScreenshot(DriverManager.getAppiumDriver()),
					"image/png", "Screenshot");
			ExtentCucumberAdapter
					.addTestStepScreenCaptureFromPath(Util.takeScreenshotFile(DriverManager.getAppiumDriver()));
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * Function to take screenshot of the element for web & attach it to Cucumber
	 * Default Reports & Extent for Web Application with WebDriver
	 * 
	 */

	protected void attachScreenshotOfElementForWeb() {

		try {
			DriverManager.getTestParameters().getScenario().attach(Util.takeScreenshot(DriverManager.getAppiumDriver()),
					"image/png", "Screenshot");
			ExtentCucumberAdapter
					.addTestStepScreenCaptureFromPath(Util.takeScreenshotFile(DriverManager.getAppiumDriver()));
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * Function to take screenshot & attach it to Cucumber Default Reports & Extent
	 * for Mobile Native Application with AppiumDriver
	 * 
	 */
	protected void attachScreenshotForMobile() {

		try {
			DriverManager.getTestParameters().getScenario().attach(Util.takeScreenshot(DriverManager.getAppiumDriver()),
					"image/png", "Screenshot");
			ExtentCucumberAdapter
					.addTestStepScreenCaptureFromPath(Util.takeScreenshotFile(DriverManager.getAppiumDriver()));
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * Function to take screenshot & attach it to Cucumber Default Reports & Extent
	 * for Mobile Native Application with AppiumDriver
	 * 
	 */
	protected static void attachScreenshotForMobile(boolean stepStatus) {
		boolean passedStep = Boolean.parseBoolean(properties.getProperty("TakeScreenshotForPassedStep"));
		boolean failedStep = Boolean.parseBoolean(properties.getProperty("TakeScreenshotForFailedStep"));
		try {
			if ((passedStep == true && stepStatus == true) || (failedStep == true && stepStatus == false)) {
				DriverManager.getTestParameters().getScenario()
						.attach(Util.takeScreenshot(DriverManager.getAppiumDriver()), "image/png");
				ExtentCucumberAdapter
						.addTestStepScreenCaptureFromPath(Util.takeScreenshotFile(DriverManager.getAppiumDriver()));
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	// Selenium Reusable Methods

	/**
	 * Function to pause the execution for the specified time period
	 * 
	 * @param milliSeconds The wait time in milliseconds
	 */
	public void waitFor(long milliSeconds) {
		try {
			Thread.sleep(milliSeconds);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}

	}

	/**
	 * Function to write step Log into the Extent Reports
	 * 
	 * @param stepDescription - Step Description
	 */
	protected static void addStepLog(String message) {
		ExtentCucumberAdapter.addTestStepLog(message);
	}

	/**
	 * Function to wait until the page loads completely
	 * 
	 * @param timeOutInSeconds The wait timeout in seconds
	 */
	public void waitUntilPageLoaded(long timeOutInSeconds) {
		WebElement oldPage = DriverManager.getAppiumDriver().findElement(By.tagName("html"));

		(new WebDriverWait(DriverManager.getAppiumDriver(), Duration.ofSeconds(timeOutInSeconds)))
				.until(ExpectedConditions.stalenessOf(oldPage));

	}

	/**
	 * Function to wait until the page readyState equals 'complete'
	 * 
	 * @param timeOutInSeconds The wait timeout in seconds
	 */
	public void waitUntilPageReadyStateComplete(long timeOutInSeconds) {
		try {
			ExpectedCondition<Boolean> pageReadyStateComplete = new ExpectedCondition<Boolean>() {
				public Boolean apply(WebDriver driver) {
					return ((JavascriptExecutor) driver).executeScript("return document.readyState").equals("complete");
				}
			};
			(new WebDriverWait(DriverManager.getAppiumDriver(), Duration.ofSeconds(timeOutInSeconds))).until(pageReadyStateComplete);
		} catch (Exception e) {
			System.out.println("[INFO] Finished the Wait for the page to be in ready state");
		}
	}

	/**
	 * Function to wait until the specified element is located
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param timeOutInSeconds The wait timeout in seconds
	 */
	public void waitUntilElementLocated(By by, long timeOutInSeconds) {
		(new WebDriverWait(DriverManager.getAppiumDriver(), Duration.ofSeconds(timeOutInSeconds)))
				.until(ExpectedConditions.presenceOfElementLocated(by));
	}

	/**
	 * Function to wait until the specified element is visible
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param timeOutInSeconds The wait timeout in seconds
	 */
	public WebElement waitUntilElementVisible(By by, long timeOutInSeconds) {
		WebElement element = (new WebDriverWait(DriverManager.getAppiumDriver(), Duration.ofSeconds(timeOutInSeconds)))
				.until(ExpectedConditions.visibilityOfElementLocated(by));
		return element;
	}

	/**
	 * Function to wait until the specified element is enabled
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param timeOutInSeconds The wait timeout in seconds
	 */
	public WebElement waitUntilElementEnabled(By by, long timeOutInSeconds) {
		WebElement element = (new WebDriverWait(DriverManager.getAppiumDriver(), Duration.ofSeconds(timeOutInSeconds)))
				.until(ExpectedConditions.elementToBeClickable(by));
		return element;
	}

	/**
	 * Function to wait until the specified element is disabled
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param timeOutInSeconds The wait timeout in seconds
	 */
	public void waitUntilElementDisabled(By by, long timeOutInSeconds) {
		(new WebDriverWait(DriverManager.getAppiumDriver(), Duration.ofSeconds(timeOutInSeconds)))
				.until(ExpectedConditions.not(ExpectedConditions.elementToBeClickable(by)));
	}

	/**
	 * Function to select the specified value from a listbox
	 * 
	 * @param by   The {@link WebDriver} locator used to identify the listbox
	 * @param item The value to be selected within the listbox
	 */
	public void selectListItem(By by, String item) {
		Select dropDownList = new Select(DriverManager.getAppiumDriver().findElement(by));
		dropDownList.selectByVisibleText(item);
	}

	/**
	 * Function to do a mouseover on top of the specified element
	 * 
	 * @param by The {@link WebDriver} locator used to identify the element
	 */
	public void mouseOver(By by) {
		Actions actions = new Actions(DriverManager.getAppiumDriver());
		actions.moveToElement(DriverManager.getAppiumDriver().findElement(by)).build().perform();
	}

	/**
	 * Function to verify whether the specified object exists within the current
	 * page
	 * 
	 * @param by The {@link WebDriver} locator used to identify the element
	 * @return Boolean value indicating whether the specified object exists
	 */
	public Boolean objectExists(By by) {
		return !DriverManager.getAppiumDriver().findElements(by).isEmpty();
	}

	/**
	 * Function to verify whether the specified text is present within the current
	 * page
	 * 
	 * @param textPattern The text to be verified
	 * @return Boolean value indicating whether the specified test is present
	 */
	public Boolean isTextPresent(String textPattern) {
		return DriverManager.getAppiumDriver().findElement(By.cssSelector("BODY")).getText().matches(textPattern);
	}

	/**
	 * Function to check if an alert is present on the current page
	 * 
	 * @param timeOutInSeconds The number of seconds to wait while checking for the
	 *                         alert
	 * @return Boolean value indicating whether an alert is present
	 */
	public Boolean isAlertPresent(long timeOutInSeconds) {
		try {
			new WebDriverWait(DriverManager.getAppiumDriver(), Duration.ofSeconds(timeOutInSeconds))
					.until(ExpectedConditions.alertIsPresent());
			return true;
		} catch (TimeoutException ex) {
			return false;
		}
	}

	/**
	 * Function to highlight the element on current page
	 * 
	 * @param by The {@link WebDriver} locator used to identify the element
	 */
	public void highLightElement(WebElement ele) {
		// Print current context (works for AndroidDriver and IOSDriver)
		String context = "";
		if (DriverManager.getAppiumDriver() instanceof AndroidDriver) {
			context = ((AndroidDriver) DriverManager.getAppiumDriver()).getContext();
		} else if (DriverManager.getAppiumDriver() instanceof IOSDriver) {
			context = ((IOSDriver) DriverManager.getAppiumDriver()).getContext();
		}
		System.out.println(context);
		try {
			// Creating JavaScriptExecuter Interface

			if (context.contains("CHROMIUM")) {
				JavascriptExecutor executor = (JavascriptExecutor) DriverManager.getAppiumDriver();
				for (int iCnt = 0; iCnt < 1; iCnt++) {
					// Execute java script
					executor.executeScript("arguments[0].style.border='6px groove green'", ele);
					Thread.sleep(100);
					executor.executeScript("arguments[0].style.border=''", ele);
				}
			}
		} catch (Exception e) {
			System.out.println("Exception - " + e.getMessage());
		}
	}

	public void captureElementScreenshot(WebElement element) throws IOException {
		File screen = ((TakesScreenshot) DriverManager.getAppiumDriver()).getScreenshotAs(OutputType.FILE);
		int ImageWidth = element.getSize().getWidth();
		int ImageHeight = element.getSize().getHeight();
		Point point = element.getLocation();
		int xcord = point.getX();
		int ycord = point.getY();
		BufferedImage img = ImageIO.read(screen);
		BufferedImage dest = img.getSubimage(xcord, ycord, ImageWidth, ImageHeight);
		ImageIO.write(dest, "png", screen);
		FileUtils.copyFile(screen, new File("D:\\screenshot.png"));
	}

	public void setDate(String date) {
		String prev = "a.ui-datepicker-prev";
		String next = "a.ui-datepicker-next";
		curDate = DriverManager.getAppiumDriver().findElement(By.cssSelector("div.ui-datepicker-title"));
		long diff = this.getDateDifferenceInMonths(date);
		int day = this.getDay(date);
		String arrow = (diff >= 0 ? next : prev);
		diff = Math.abs(diff);
		// click the arrows
		LongStream.range(0, diff).forEach(i -> getStaleElement(arrow).click());
		// select the date
		WebElement e = DriverManager.getAppiumDriver()
				.findElement(By.xpath("(//table[@class='ui-datepicker-calendar']/tbody/tr/td/a)" + "[" + day + "]"));
		e.click();
		/*
		 * dates.stream() .filter(ele -> Integer.parseInt(ele.getText()) == day)
		 * .findFirst() .ifPresent(ele -> ele.click());
		 */
	}

	private int getDay(String date) {
		LocalDate dpToDate = LocalDate.parse(date, DTF);
		return dpToDate.getDayOfMonth();
	}

	private long getDateDifferenceInMonths(String date) {
		LocalDate dpCurDate = LocalDate.parse(DAY_FIRST + SPACE + getCurrentMonthFromDatePicker(), DTF);
		LocalDate dpToDate = LocalDate.parse(date, DTF);
		return YearMonth.from(dpCurDate).until(dpToDate, ChronoUnit.MONTHS);
	}

	private String getCurrentMonthFromDatePicker() {
		return curDate.getText();
	}

	public void clickUsingJavaScript(WebElement element) {
		JavascriptExecutor executor = (JavascriptExecutor) DriverManager.getAppiumDriver();
		executor.executeScript("arguments[0].click();", element);
	}

	public static WebElement getStaleElement(String element) {
		Wait<WebDriver> stubbornWait = new FluentWait<WebDriver>(DriverManager.getAppiumDriver())
				.withTimeout(Duration.ofSeconds(30)).pollingEvery(Duration.ofSeconds(5))
				.ignoring(NoSuchElementException.class).ignoring(StaleElementReferenceException.class);
		WebElement foo = stubbornWait.until(new Function<WebDriver, WebElement>() {
			public WebElement apply(WebDriver driver) {
				return DriverManager.getAppiumDriver().findElement(By.xpath(element));
			}
		});
		return foo;
	}

	public static int randomNumber(int min, int max) {
		int a = (int) (Math.random() * (max - min + 1) + min);
		return a;
	}

	protected static boolean isElementPresent(By by) {
		DriverManager.getAppiumDriver().manage().timeouts().implicitlyWait(Duration.ofSeconds(5));
		try {
			if (DriverManager.getAppiumDriver().findElements(by).size() > 0
					&& DriverManager.getAppiumDriver().findElement(by).isDisplayed()) {
				// Revert back to default value of implicit wait
				DriverManager.getAppiumDriver().manage().timeouts().implicitlyWait(Duration.ofSeconds(30));
				return true;
			} else {
				// Revert back to default value of implicit wait
				DriverManager.getAppiumDriver().manage().timeouts().implicitlyWait(Duration.ofSeconds(30));
				return false;
			}
		} catch (Exception e) {
			// Revert back to default value of implicit wait
			DriverManager.getAppiumDriver().manage().timeouts().implicitlyWait(Duration.ofSeconds(10));
			return false;
		}
	}

	public void lauchsetting() {

		((IOSDriver) driver).activateApp("com.apple.Preferences");
		// driver.executeScript("mobile: launchApp", ImmutableMap.of("bundleId",
		// "com.apple.Preferences"));

		((JavascriptExecutor) driver).executeScript("mobile: scroll", ImmutableMap.of("direction", "up"));

		switchToContext(driver, "NATIVE_APP");
		// driver.findElement(AppiumBy.accessibilityId(appName)).click();
		driver.findElement(AppiumBy.accessibilityId("Search")).sendKeys("Location");

		driver.findElement(AppiumBy.accessibilityId("Location, Privacy & Security")).click();
		// clearAndEnterText(LoginPageObjects.Location, "Location", "search appName");
		driver.findElement(AppiumBy.accessibilityId("Location Services")).click();

		WebElement Locationtoggle = driver
				.findElement(By.xpath("//XCUIElementTypeSwitch[@name=\"Location Services\"]"));

		if (isElementPresent(LoginPageObjects.Locationvalue)) {

			System.out.println("Testpass");
			Locationtoggle.click();
			driver.findElement(new AppiumBy.ByAccessibilityId("com.apple.mobilesafari")).click();
			driver.findElement(new AppiumBy.ByAccessibilityId("LOCATION_SERVICES_AUTH_NOT_DETERMINED")).click();
			// driver.findElement(new AppiumBy.ByAccessibilityId("Never")).click();
		} else {

			driver.findElement(new AppiumBy.ByAccessibilityId("com.apple.mobilesafari")).click();
			driver.findElement(new AppiumBy.ByAccessibilityId("LOCATION_SERVICES_AUTH_NOT_DETERMINED")).click();
			// driver.findElement(new AppiumBy.ByAccessibilityId("Never")).click();

		}
		((IOSDriver)driver).terminateApp("com.apple.Preferences");
		// driver.findElement(By.xpath("//XCUIElementTypeButton[@name='Dont Use
		// Siri']")).click();

		// Locationtoggle.click() ;
		// String Location="Location";
		//

		// clearAndEnterText(LoginPageObjects.editSearch, appName, "search appName");
		// driver.findElement(new AppiumBy.ByAccessibilityId("Location")).click();
		// driver.findElement(AppiumBy.AccessibilityId("Privacy")).click();
		// driver.findElement(AppiumBy.AccessibilityId("Location")).click();
		// scrollToMobileElement("Location");

		////

		// clickIfElementPresent(ChromeBrowserObjects.Apps, "Apps");
	}

	public static void switchToContext(AppiumDriver driver, String context) {
		RemoteExecuteMethod executeMethod = new RemoteExecuteMethod(driver);
		Map<String, String> params = new HashMap<String, String>();
		params.put("name", context);
		executeMethod.execute("switchToContext", params);
	}

	public void setDeviceLocation() throws IOException {
		String Location = "401 N main st, Winston Salem, 27106";
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("address", Location);
		((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:location:set", params);
		System.out.println("Set device location: " + Location);
	}

	public void setDeviceLocationCoordinates() {
		Map<String, Object> params = new HashMap<>();
		params.put("coordinates", "41.691419946931624, -85.97609983148413");
		((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:location:set", params);
		System.out.println("Set device location: " + "41.691419946931624, -85.97609983148413");
	}

	public String findTextVisually(String textToFind) {
		String needle = textToFind;
		String findCommand = "mobile:text:find"; // The proprietary function call
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("content", needle); // The text we're looking for
		params.put("context", "body");
		// params.put("scrolling", "scroll"); // Add the scroll and search
		// params.put("next", "SWIPE_UP"); // Next is mandatory if using scroll and
		// search
		// Can also use customized swipe like:
		// "SWIPE=(50%,80%),(50%,60%);WAIT=1000" params.put("maxscroll", 3); // Not
		// mandatory, default is 5
		params.put("threshold", 100); // Adding threshold
		String res = (String) DriverManager.getAppiumDriver().executeScript(findCommand, params); // Calling the script
		return res;
	}

	public String findTextVisuallyUsingGoogleAPI(String textToFind)
			throws FileNotFoundException, ClassNotFoundException, NoSuchMethodException, SecurityException,
			IllegalAccessException, IllegalArgumentException, InvocationTargetException, IOException {
		String res = null;
		File screenShot = ((TakesScreenshot) DriverManager.getAppiumDriver()).getScreenshotAs(OutputType.FILE);
		FileUtils.copyFile(screenShot,
				new File(System.getProperty("user.dir") + "/src/test/resources/Screenshots/FullPage.png"));
		String wholePageText = DetectTextGoogleAPI
				.detectText(System.getProperty("user.dir") + "/src/test/resources/Screenshots/FullPage.png");
		if (StringUtils.normalizeSpace(wholePageText).contains(textToFind)) {
			res = "true";
		} else {
			res = "false";
		}
		return res;
	}

	@SuppressWarnings({ "rawtypes" })
	private void scroll(int fromX, int fromY, int toX, int toY) {
		// Create a sequence object
		Sequence sequence = new Sequence(DriverManager.getAppiumDriver(), 0)
			// Add the pointer move to start position
			.addAction(DriverManager.getAppiumDriver().createPointerInput(PointerInput.Kind.TOUCH, "finger")
				.createPointerMove(Duration.ZERO, PointerInput.Origin.viewport(), fromX, fromY))
			// Add the pointer down action
			.addAction(DriverManager.getAppiumDriver().createPointerInput(PointerInput.Kind.TOUCH, "finger")
				.createPointerDown(PointerInput.MouseButton.LEFT.asArg()))
			// Add the pointer move to end position
			.addAction(DriverManager.getAppiumDriver().createPointerInput(PointerInput.Kind.TOUCH, "finger")
				.createPointerMove(Duration.ofMillis(300), PointerInput.Origin.viewport(), toX, toY))
			// Add the pointer up action
			.addAction(DriverManager.getAppiumDriver().createPointerInput(PointerInput.Kind.TOUCH, "finger")
				.createPointerUp(PointerInput.MouseButton.LEFT.asArg()));
		
		// Perform the sequence
		DriverManager.getAppiumDriver().perform(Arrays.asList(sequence));
	}

	public void scrollDown() {
		int pressX = DriverManager.getAppiumDriver().manage().window().getSize().width / 2;
		int bottomY = DriverManager.getAppiumDriver().manage().window().getSize().height * 4 / 5;
		int topY = DriverManager.getAppiumDriver().manage().window().getSize().height / 4;
		scroll(pressX, bottomY, pressX, topY);
	}

	public void scrollDownFromMid() {
		int pressX = DriverManager.getAppiumDriver().manage().window().getSize().width / 2;
		int bottomY = DriverManager.getAppiumDriver().manage().window().getSize().height * 3 / 5;
		int topY = DriverManager.getAppiumDriver().manage().window().getSize().height / 8;
		scroll(pressX, bottomY, pressX, topY);
	}

	public void switchControlToChildTab(RemoteWebDriver driver) throws Exception {
		try {
			String AlreadySelectedWindow = driver.getWindowHandle();
			System.out.println("AlreadySelectedWindow:Parent " + AlreadySelectedWindow);
			Set<String> tab_handles = driver.getWindowHandles();
			int number_of_tabs = tab_handles.size();
			int new_tab_index = number_of_tabs - 1;
			driver.switchTo().window(tab_handles.toArray()[new_tab_index].toString());
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("Exception in switchControlToChildTab Function while trying to switch tabs");
		}
	}

	public void closeChildTab(RemoteWebDriver driver) throws Exception {
		try {
			String AlreadySelectedWindow = driver.getWindowHandle();
			System.out.println("AlreadySelectedWindow:Child " + AlreadySelectedWindow);
			Set<String> tab_handles = driver.getWindowHandles();
			int number_of_tabs = tab_handles.size();
			int new_tab_index = number_of_tabs - 1;
			driver.switchTo().window(tab_handles.toArray()[new_tab_index].toString());
			driver.close();
			System.out.println("Closed ChildTab");
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("Exception in switchControlToChildTab Function while trying to switch tabs");
		}
	}

	public void switchControlToParentTab(RemoteWebDriver driver) throws Exception {
		try {
			Set<String> tab_handles = driver.getWindowHandles();
			driver.switchTo().window(tab_handles.toArray()[0].toString());
			System.out.println("Returned to Parent tab");
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("Exception in switchControlToParentTab Function while trying to switch tabs");
		}
	}

	public static void scrollToBottom(WebDriver driver) {
		((JavascriptExecutor) driver).executeScript("window.scrollTo(0, document.body.scrollHeight)");
	}

	public static void scrollToTop(WebDriver driver) {
		((JavascriptExecutor) driver).executeScript("window.scrollTo(document.body.scrollHeight, 0)");
	}

	public void scrollToElement(WebDriver driver, WebElement element) {
		((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView();", element);
	}

	public static void clickIfElementPresent(By ele, String objName) {
		try {
			// DriverManager.getReportiumClient().stepStart("Validate that the user is able
			// to click on the element-" + objName);
			WebElement element = (WebElement) DriverManager.getAppiumDriver().findElement(ele);
			if (element.isDisplayed()) {
				highLightElement(element, "LawnGreen");
				element.click();
				// DriverManager.getReportiumClient().reportiumAssert(
				// "The element- " + objName + " is present and clicked on it successfully",
				// true);
				// DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(true, "The element- " + objName + " is present and clicked on it successfully");
				addStepLog("The element- " + objName + " is present and clicked on it successfully");
				System.out.println("[INFO] The element- " + objName + " is present and clicked on it successfully");
				attachScreenshotForMobile(true);
			} else {
				// DriverManager.getReportiumClient()
				// .reportiumAssert("The element- " + objName + " is NOT present and NOT clicked
				// on it", false);
				// DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(false, "The element- " + objName + " is NOT present and NOT clicked on it");
				attachScreenshotForMobile(false);
				System.out.println("[INFO] The element- " + objName + " is NOT present and NOT clicked on it");
				fail("The element- " + objName + " is NOT present and NOT clicked on it");
			}
		} catch (Exception e) {
			failTestScript("[INFO] The element- " + objName + " is NOT present and NOT clicked on it", e);
		}
	}

	public void clickTextPerfecto(String strStringToClick) {
		try {
			// DriverManager.getReportiumClient().stepStart("Validate that the user is able
			// to click on the element-" + strStringToClick);
			Map<String, Object> params = new HashMap<>();
			params.put("label", strStringToClick);
			params.put("threshold", 80);
			params.put("ignorecase", "nocase");
			DriverManager.getAppiumDriver().executeScript("mobile:button-text:click", params);
			// DriverManager.getReportiumClient().reportiumAssert(
			// "The element- " + strStringToClick + " is present and clicked on it
			// successfully", true);
			// DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("[INFO] The element- " + strStringToClick + " is NOT present and NOT clicked on it", e);
		}
	}

	public static void clickIfElementByText(String text) {
		try {
			// DriverManager.getReportiumClient()
			// .stepStart("Validate that the user is able to click on the element with
			// text-" + text);
			Map<String, Object> params = new HashMap<>();
			params.put("content", text);
			params.put("threshold", 80);
			params.put("ignorecase", "nocase");
			String res = (String) DriverManager.getAppiumDriver().executeScript("mobile:checkpoint:text", params);
			if (res.equalsIgnoreCase("true")) {
				params.clear();
				params.put("label", text);
				params.put("threshold", 80);
				params.put("ignorecase", "nocase");
				DriverManager.getAppiumDriver().executeScript("mobile:button-text:click", params);
				// DriverManager.getReportiumClient()
				// .reportiumAssert("The element- " + text + " is present and clicked on it
				// successfully", true);
				// DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(true, "The element- " + text + " is present and clicked on it successfully");
				addStepLog("The element- " + text + " is present and clicked on it successfully");
				attachScreenshotForMobile(true);
			} else {
				// DriverManager.getReportiumClient()
				// .reportiumAssert("The element- " + text + " is NOT present and NOT clicked on
				// it", false);
				// DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(false, "The element- " + text + " is NOT present and NOT clicked on it");
				attachScreenshotForMobile(false);
				fail("The element- " + text + " is NOT present and NOT clicked on it");
			}
		} catch (Exception e) {
			failTestScript("[INFO] The element- " + text + " is NOT present and NOT clicked on it", e);
		}
	}

	public static boolean compareStringsContains(String actualMsg, String expectedMsg) throws IOException {
		// DriverManager.getReportiumClient()
		// .stepStart("Compare the Expected text: '" + expectedMsg + "' with the actual
		// text : '" + actualMsg);
		try {
			if (equalsIgnoreNewlineStyleContains(actualMsg, expectedMsg)) {
				// DriverManager.getReportiumClient().reportiumAssert(
				// "The Expected Text: '" + expectedMsg + "' is present in the actual message :
				// '" + actualMsg,
				// true);
				// DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(true,
						"The Expected  Text: '" + expectedMsg + "' is present in the actual message : '" + actualMsg);
				addStepLog(
						"The Expected  Text: '" + expectedMsg + "' is present in the actual message : '" + actualMsg);
				attachScreenshotForMobile(true);
				return true;
			} else {
				// DriverManager.getReportiumClient().reportiumAssert("The Expected Text: " +
				// expectedMsg
				// + " is not present in the actual message page: " + actualMsg, false);
				// //DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(false, "The Expected  Text: " + expectedMsg
						+ " is not present in the actual message page: " + actualMsg);
				attachScreenshotForMobile(false);
				fail("The Expected  Text: " + expectedMsg + " is not present in the actual message page: " + actualMsg);
				return false;
			}
		} catch (Exception e) {
			failTestScript("[INFO] The Expected  Text: " + expectedMsg + " is not present in the actual message page: "
					+ actualMsg, e);
			return false;
		}
	}

	public static String extractImageTextUsingScreenShotOfElement(By ele) throws Exception {
		String screenshotpath = null;
		List<WebElement> elements = DriverManager.getAppiumDriver().findElements(ele);
		for (WebElement element : elements) {
			if (element.isDisplayed()) {
				if ("ios".equalsIgnoreCase(String.valueOf(DriverManager.getAppiumDriver().getCapabilities().getCapability("platformName")))) {
					screenshotpath = getElementScreenshotforIOSDevice(DriverManager.getAppiumDriver().findElement(ele));
				} else
					screenshotpath = getElementScreenshotAndroid(DriverManager.getAppiumDriver().findElement(ele));
				break;
			}
		}
		String extractedText = DetectTextGoogleAPI.detectText(screenshotpath);
		System.out.println(extractedText);
		return extractedText;
	}

	public static boolean equalsIgnoreNewlineStyleContains(String s1, String s2) {
		return s1 != null && s2 != null && normalizeLineEnds(s1).contains(normalizeLineEnds(s2));
	}

	public boolean equalsIgnoreNewlineStyle(String s1, String s2) {
		return s1 != null && s2 != null && normalizeLineEnds(s1).equals(normalizeLineEnds(s2));
	}

	private static String normalizeLineEnds(String s) {
		System.out.println(s.replace("\r\n", " ").replace('\r', ' ').replace('\n', ' ').trim());
		return s.replace("\r\n", "\n").replace('\r', '\n').replace('\n', ' ').trim();
	}

	public static void clearAndEnterText(By ele, String text, String objName) {
		try {
			// DriverManager.getReportiumClient()
			// .stepStart("Validate user is able to enter the text in the text box:" +
			// objName);
			WebElement element = (WebElement) DriverManager.getAppiumDriver().findElement(ele);
			if (element.isDisplayed()) {
				highLightElement(DriverManager.getAppiumDriver().findElement(ele), "LawnGreen");
				element.clear();
				element.sendKeys(text);
				element.sendKeys(Keys.ENTER);
				// DriverManager.getReportiumClient().reportiumAssert(
				// "The text box- " + objName + " is present and the text-" + text + " is
				// successfully entered", true);
				// //DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(true,
						"The text box- " + objName + " is present and the text-" + text + " is successfully entered");
				System.out.println("[INFO] The text box- " + objName + " is present and the text-" + text
						+ " is successfully entered");
				addStepLog(
						"The text box- " + objName + " is present and the text-" + text + " is successfully entered");
				attachScreenshotForMobile(true);
			} else {
				// DriverManager.getReportiumClient().reportiumAssert(
				// "The text box- " + objName + " is NOT present and the text-" + text + " is
				// NOT entered", false);
				// //DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(false,
						"The text box- " + objName + " is present and the text-" + text + " is successfully entered");
				System.out.println("[INFO] The text box- " + objName + " is present and the text-" + text
						+ " is successfully entered");
				attachScreenshotForMobile(false);
				fail("The text box- " + objName + " is present and the text-" + text + " is successfully entered");
			}
		} catch (Exception e) {
			failTestScript(
					"[INFO] The text box- " + objName + " is present and the text-" + text + " is successfully entered",
					e);
		}
	}

	public void isElementPresentVerification(By ele, String objName) {
		try {
			// DriverManager.getReportiumClient().stepStart("Validate that the element:" +
			// objName + " is present");
			WebElement element = (WebElement) DriverManager.getAppiumDriver().findElement(ele);
			if (element.isDisplayed()) {
				highLightElement(element, "LawnGreen");
				// DriverManager.getReportiumClient().reportiumAssert("The element- " + objName
				// + " is present", true);
				// DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(true, "The element- " + objName + " is present");
				System.out.println("[INFO] The element- " + objName + " is present");
				addStepLog("The element- " + objName + " is present");
				attachScreenshotForMobile(true);
			} else {
				// DriverManager.getReportiumClient().reportiumAssert("The element- " + objName
				// + " is NOT present", false);
				// DriverManager.getReportiumClient().stepEnd();
				System.out.println("[INFO] The element- " + objName + " is NOT present");
				attachScreenshotForMobile(false);
				fail("The element- " + objName + " is NOT present");
			}
		} catch (Exception e) {
			failTestScript("The element- " + objName + " is NOT present", e);
		}
	}

	public void isElementPresentContainsText(By ele, String textToVerify, String objName) throws IOException {
		try {
			// //DriverManager.getReportiumClient().stepStart(
			// "Validate that the element:" + objName + " is present and contains the text:"
			// + textToVerify);
			WebElement element = (WebElement) DriverManager.getAppiumDriver().findElement(ele);
			if (element.isDisplayed()) {
				highLightElement(element, "LawnGreen");
				String actualText = element.getText();
				System.out.println("[INFO] Actual Text from the element:" + actualText);
				if (actualText.contains(textToVerify)) {
					// DriverManager.getReportiumClient()
					// .reportiumAssert("The element- " + objName + " is present and the actual
					// text: " + actualText
					// + " matches with the expected text:" + textToVerify, true);
					// DriverManager.getReportiumClient().stepEnd();
					Assert.assertTrue(actualText.contains(textToVerify),
							"The element- " + objName + " is present and the actual text: " + actualText
									+ " matches with the expected text:" + textToVerify);
					addStepLog("The element- " + objName + " is present and the actual text: " + actualText
							+ " matches with the expected text:" + textToVerify);
					System.out
							.println("[INFO] The element- " + objName + " is present and the actual text: " + actualText
									+ " matches with the expected text:" + textToVerify);
					attachScreenshotForMobile(true);
				} else {
					// DriverManager.getReportiumClient()
					// .reportiumAssert("The element- " + objName + " is present but the actual
					// text: " + actualText
					// + " Doesn't matche with the expected text:" + textToVerify, false);
					// DriverManager.getReportiumClient().stepEnd();
					Assert.assertTrue(actualText.contains(textToVerify),
							"The element- " + objName + " is present but the actual text: " + actualText
									+ " does't match with the expected text:" + textToVerify);
					addStepLog("The element- " + objName + " is present but the actual text: " + actualText
							+ " does't matche with the expected text:" + textToVerify);
					System.out
							.println("[INFO] The element- " + objName + " is present but the actual text: " + actualText
									+ " does't matche with the expected text:" + textToVerify);
					attachScreenshotForMobile(false);
					throw new CucumberException(
							"The element- " + objName + " is present but the actual text: " + actualText
									+ " does't matche with the expected text:" + textToVerify);
				}

			} else {
				// DriverManager.getReportiumClient().reportiumAssert("The element- " + objName
				// + " is NOT present", false);
				// DriverManager.getReportiumClient().stepEnd();
				Assert.assertTrue(false, "The element- " + objName + " is NOT present");
				attachScreenshotForMobile(false);
				System.out.println("[INFO] The element- " + objName
						+ " is present but the actual text does't match with the expected text:" + textToVerify);
				fail("The element- " + objName + " is NOT present");
			}
		} catch (Exception e) {
			failTestScript("The element- " + objName + " is NOT present", e);
		}
	}

	public String elementGetText(By ele, String objName) throws IOException {
		try {
			WebElement element = (WebElement) DriverManager.getAppiumDriver().findElement(ele);
			String actualText;
			highLightElement(element, "LawnGreen");
			actualText = element.getText();
			System.out.println(actualText);
			attachScreenshotForMobile(true);
			return actualText;
		} catch (Exception e) {
			failTestScript("Unable to get the " + objName + " text", e);
			return null;
		}

	}

	/**
	 * Function to highlight the element on current page
	 * 
	 * @param by The {@link element} locator used to identify the element
	 */

	public static void highLightElement(WebElement ele, String color) {
		try {
			if (DriverManager.getAppiumDriver().getContext().contains("CHROMIUM")) {
				JavascriptExecutor executor = (JavascriptExecutor) DriverManager.getAppiumDriver();
				String script = "arguments[0].scrollIntoView({behavior: \"auto\", block: \"center\", inline: \"center\"});";
				executor.executeScript(script, ele);
				for (int iCnt = 0; iCnt < 1; iCnt++) {
					executor.executeScript("arguments[0].style.border='6px groove " + color + "'", ele);
					Thread.sleep(13);
					executor.executeScript("arguments[0].style.border=''", ele);
				}
			}
		} catch (Exception e) {
			System.out.println("Cannot highlight the element. Only Web objects can be highlighted");
		}
	}

	@SuppressWarnings("null")
	public synchronized void setDeviceLocation(String location) throws IOException, InterruptedException {

		String Location = location;
		GeoApiContext context = new GeoApiContext.Builder().apiKey("AIzaSyAEQdVTMjC8UNPTpo6BqLnFi7vD1uLplqk").build();
		System.out.println(context);
		GeocodingResult[] geocodingResults;
		try {
			geocodingResults = GeocodingApi.geocode(context, location).await();

			double latitude = geocodingResults[0].geometry.location.lat;
			double longitude = geocodingResults[0].geometry.location.lng;
			System.out.println(latitude);
			System.out.println(longitude);
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("latitude", latitude);
			params.put("longitude", longitude);
			// AppiumDriver driver=null;
			((RemoteWebDriver) DriverManager.getAppiumDriver())
					.executeScript("window.navigator.geolocation.getCurrentPosition = function(success){ "
							+ "var position = {\"coords\" : {\"latitude\": " + latitude + ", \"longitude\":" + longitude
							+ " }}; "
							+ "success(position); }");
			// AppiumDriver driver=null;

			// ((RemoteWebDriver)
			// DriverManager.getAppiumDriver()).executeScript("window.navigator.geolocation.getCurrentPosition
			// = function(success){{var position = {{coords:{{latitude:{latitude},
			// longitude:{longitude}}}}};success(position);}}");
			// //((RemoteWebDriver)
			// DriverManager.getAppiumDriver()).executeScript("mobile:location:set",
			// params);
		} catch (ApiException | InterruptedException | IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	public synchronized static String extractImageTextUsingScreenShot() throws Exception {

		// Mat orgin = imread(screenShotFullPage());
		// String extractedText =
		// DetectTextGoogleAPI.detectText((ImageOperations.dilateAndErode(imread(ImageOperations.convertGrey(ImageOperations.resizeImage(orgin))))));
		String extractedText = DetectTextGoogleAPI.detectText(screenShotFullPage());
		System.out.println(extractedText);
		return extractedText;
	}

	public static String extractImageTextUsingScreenShot(WebElement ele) throws Exception {

		// String extractedText =
		// DetectTextGoogleAPI.detectText(getElementScreenshotAndroid(ele));
		String elementFilePath = getElementScreenshotAndroid(ele);
		// String extractedText =
		// DetectTextGoogleAPI.detectText(ImageOperations.imageProcessingForOCR(imread(elementFilePath),elementFilePath));
		String extractedText = DetectTextGoogleAPI.detectText(elementFilePath);

		System.out.println(extractedText);
		return extractedText;
	}

	public synchronized static String screenShotFullPage() {
		try {
			File screenShot = ((TakesScreenshot) DriverManager.getAppiumDriver()).getScreenshotAs(OutputType.FILE);
			String filename = UUID.randomUUID().toString();
			String path = "test-output\\screenshots\\" + filename + "\\" + filename;
			String screenshotLocation = System.getProperty("user.dir") + "\\" + path + ".png";
			;
			File temp = new File(screenshotLocation);
			FileUtils.copyFile(screenShot, temp);

			BufferedImage img = null;
			BufferedImage dest = null;
			img = ImageIO.read(temp);
			dest = img;

			ImageIO.write(dest, "png", screenShot);
			FileUtils.copyFile(screenShot, new File(screenshotLocation));
			return screenshotLocation;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}

	public static String getScreenShotOfElement(WebElement ele) throws IOException, InterruptedException {
		highLightElement(ele, "green");
		String path = System.getProperty("user.dir") + "\\src\\test\\resources\\SGW_Images\\test.png";
		Shutterbug.shootPage(DriverManager.getAppiumDriver()).save(path);
		return path;
	}

	public static String extractImageText(WebElement element)
			throws IOException, InterruptedException, ClassNotFoundException, NoSuchMethodException, SecurityException,
			IllegalAccessException, IllegalArgumentException, InvocationTargetException {
		Mat orgin = imread(getScreenShotOfElement(element));
		String extractedText = DetectTextGoogleAPI.detectText((ImageOperations
				.dilateAndErode(imread(ImageOperations.convertGrey(ImageOperations.resizeImage(orgin))))));
		System.out.println(extractedText);
		return extractedText;
	}

	public static String extractImageTextWithoutResize(WebElement element) throws FileNotFoundException,
			ClassNotFoundException, NoSuchMethodException, SecurityException, IllegalAccessException,
			IllegalArgumentException, InvocationTargetException, IOException, InterruptedException {
		String extractedText = DetectTextGoogleAPI.detectText(
				(ImageOperations.dilateAndErode(imread(ImageOperations.convertGrey(getScreenShotOfElement(element))))));
		System.out.println(extractedText);
		return extractedText;
	}

	public String getUserNameFromDb() {
		try {
			String query = "SELECT a.ACCTNO,a.USERID, b.FNAME, b.LNAME, b.DOB, c.ADD1, c.ZIP, c.CITY, c.STATE, a.PASSWORD, a.WEBACCESS, a.LOGIN_RESET, a.SOURCESYSTEM, a.SITECODE, a.MANUFACTURER \r\n"
					+ "FROM [RAI_APP].[dbo].[Consumer_Credentials] a, [RAI_BASE].[dbo].[CONSUMERS_ACCTNO] b, [RAI_BASE].[dbo].[CONSUMERS_ADDRESS] c where\r\n"
					+ "a.ACCTNO=b.ACCTNO and b.ACCTNO=c.ACCTNO and a.ACCTNO=c.ACCTNO\r\n"
					+ "and a.PASSWORD='4tJ22wPRKQsXr8hazDXdVBzVJFtlEnce7zBfz2FVuYF/2nPdSnETbB82b/Sg0/Ce0OghjPdBu4NQmSjKEVXcmg==' \r\n"
					+ "and a.WEBACCESS='U' and a.LOGIN_RESET='N'\r\n" + "ORDER BY NEWID()";
			ResultSet result = DatabaseConnection.getResultSet(query);
			String userid = DatabaseConnection.getDBColumnValue(result, "USERID");
			System.out.print("Username selected from db:" + userid);
			return userid;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public List<String> getDataFromDb() {
		try {
			List<String> data = new ArrayList<String>();
			String query = "SELECT TOP (1) a.ACCTNO,a.USERID, b.FNAME, b.LNAME, b.DOB, c.ADD1, c.ZIP, c.CITY, c.STATE, a.PASSWORD, a.WEBACCESS, a.LOGIN_RESET, a.SOURCESYSTEM, a.SITECODE, a.MANUFACTURER \r\n"
					+ "FROM [RAI_APP].[dbo].[Consumer_Credentials] a, [RAI_BASE].[dbo].[CONSUMERS_ACCTNO] b, [RAI_BASE].[dbo].[CONSUMERS_ADDRESS] c where\r\n"
					+ "a.ACCTNO=b.ACCTNO and b.ACCTNO=c.ACCTNO and a.ACCTNO=c.ACCTNO\r\n"
					+ "and a.PASSWORD='4tJ22wPRKQsXr8hazDXdVBzVJFtlEnce7zBfz2FVuYF/2nPdSnETbB82b/Sg0/Ce0OghjPdBu4NQmSjKEVXcmg==' \r\n"
					+ "and a.WEBACCESS='U' and a.LOGIN_RESET='N'\r\n" + " and a.CREATE_DATE > '2022-05-07 10:23:00'\r\n"
					+ "ORDER BY NEWID()";
			ResultSet result = DatabaseConnection.getResultSet(query);
			String acctno = DatabaseConnection.getDBColumnValue(result, "ACCTNO");
			result.beforeFirst();
			String userid = DatabaseConnection.getDBColumnValue(result, "USERID");
			data.add(acctno);
			data.add(userid);
			System.out.println("Data retrived from the db is :" + data);
			return data;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}

	public List<String> getDataFromDbProd() {
		try {
			List<String> data = new ArrayList<String>();
			String query = "SELECT TOP (1) a.ACCTNO,a.USERID, b.FNAME, b.LNAME, b.DOB, c.ADD1, c.ZIP, c.CITY, c.STATE, a.PASSWORD, a.WEBACCESS, a.LOGIN_RESET, a.SOURCESYSTEM, a.SITECODE, a.MANUFACTURER \r\n"
					+ "FROM [RAI_APP].[dbo].[Consumer_Credentials] a, [RAI_BASE].[dbo].[CONSUMERS_ACCTNO] b, [RAI_BASE].[dbo].[CONSUMERS_ADDRESS] c where\r\n"
					+ "a.ACCTNO=b.ACCTNO and b.ACCTNO=c.ACCTNO and a.ACCTNO=c.ACCTNO\r\n"
					+ "and a.PASSWORD='4tJ22wPRKQsXr8hazDXdVBzVJFtlEnce7zBfz2FVuYF/2nPdSnETbB82b/Sg0/Ce0OghjPdBu4NQmSjKEVXcmg==' \r\n"
					+ "and a.WEBACCESS='U' and a.LOGIN_RESET='N'\r\n" + " and a.CREATE_DATE > '2022-05-07 10:23:00'\r\n"
					+ "and a.USERID LIKE 'rjrautomationtest%rbds%' ORDER BY NEWID()";
			ResultSet result = DatabaseConnection.getResultSetProd(query);
			String acctno = DatabaseConnection.getDBColumnValue(result, "ACCTNO");
			result.beforeFirst();
			String userid = DatabaseConnection.getDBColumnValue(result, "USERID");
			data.add(acctno);
			data.add(userid);
			System.out.println("Data retrived from the db is :" + data);
			return data;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}

	public List<String> getDataFromDb(String ctype) {
		try {
			String query = null;
			LocalDate currentdate = LocalDate.now();
			int month = currentdate.getMonthValue();
			int day = currentdate.getDayOfMonth();
			int year = currentdate.getYear();
			String date = Integer.toString(year) + "-" + Integer.toString(month - 1) + "-" + Integer.toString(day);
			List<String> data = new ArrayList<String>();
			if (ctype.contains("Welcome"))
				query = "SELECT TOP (1) a.ACCTNO,a.USERID, b.FNAME, b.LNAME, b.DOB, c.ADD1, c.ZIP, c.CITY, c.STATE, a.PASSWORD, a.WEBACCESS, a.LOGIN_RESET, a.SOURCESYSTEM, a.SITECODE, a.MANUFACTURER \r\n"
						+ "FROM [RAI_APP].[dbo].[Consumer_Credentials] a, [RAI_BASE].[dbo].[CONSUMERS_ACCTNO] b, [RAI_BASE].[dbo].[CONSUMERS_ADDRESS] c where\r\n"
						+ "a.ACCTNO=b.ACCTNO and b.ACCTNO=c.ACCTNO and a.ACCTNO=c.ACCTNO\r\n"
						+ "and a.PASSWORD='4tJ22wPRKQsXr8hazDXdVBzVJFtlEnce7zBfz2FVuYF/2nPdSnETbB82b/Sg0/Ce0OghjPdBu4NQmSjKEVXcmg==' \r\n"
						+ "and a.WEBACCESS='U' and a.LOGIN_RESET='N'\r\n" + " and a.CREATE_DATE > '" + date + "'\r\n"
						+ " ORDER BY NEWID()";
			else if (ctype.contains("Birthday"))
				query = "SELECT TOP (1) a.ACCTNO,a.USERID, b.FNAME, b.LNAME, b.DOB, c.ADD1, c.ZIP, c.CITY, c.STATE, a.PASSWORD, a.WEBACCESS, a.LOGIN_RESET, a.SOURCESYSTEM, a.SITECODE, a.MANUFACTURER \r\n"
						+ "FROM [RAI_APP].[dbo].[Consumer_Credentials] a, [RAI_BASE].[dbo].[CONSUMERS_ACCTNO] b, [RAI_BASE].[dbo].[CONSUMERS_ADDRESS] c where\r\n"
						+ "a.ACCTNO=b.ACCTNO and b.ACCTNO=c.ACCTNO and a.ACCTNO=c.ACCTNO\r\n"
						+ "and a.PASSWORD='4tJ22wPRKQsXr8hazDXdVBzVJFtlEnce7zBfz2FVuYF/2nPdSnETbB82b/Sg0/Ce0OghjPdBu4NQmSjKEVXcmg==' \r\n"
						+ "and a.WEBACCESS='U' and a.LOGIN_RESET='N'\r\n"
						+ " and a.CREATE_DATE > '2022-05-07 10:23:00'\r\n"
						+ "and b.DOB LIKE '%-" + (month - 1) + "-%' ORDER BY NEWID()";
			System.out.println(query);
			ResultSet result = DatabaseConnection.getResultSet(query);
			String acctno = DatabaseConnection.getDBColumnValue(result, "ACCTNO");
			result.beforeFirst();
			String userid = DatabaseConnection.getDBColumnValue(result, "USERID");
			data.add(acctno);
			data.add(userid);
			System.out.println("Data retrived from the db is :" + data);
			return data;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}

	public List<String> getDataFromDbProd(String ctype) {
		try {
			String query = null;
			LocalDate currentdate = LocalDate.now();
			int month = currentdate.getMonthValue();
			int day = currentdate.getDayOfMonth();
			int year = currentdate.getYear();
			String date = Integer.toString(year) + "-" + Integer.toString(month - 1) + "-" + Integer.toString(day);
			List<String> data = new ArrayList<String>();
			if (ctype.contains("Welcome"))
				query = "SELECT TOP (1) a.ACCTNO,a.USERID, b.FNAME, b.LNAME, b.DOB, c.ADD1, c.ZIP, c.CITY, c.STATE, a.PASSWORD, a.WEBACCESS, a.LOGIN_RESET, a.SOURCESYSTEM, a.SITECODE, a.MANUFACTURER \r\n"
						+ "FROM [RAI_APP].[dbo].[Consumer_Credentials] a, [RAI_BASE].[dbo].[CONSUMERS_ACCTNO] b, [RAI_BASE].[dbo].[CONSUMERS_ADDRESS] c where\r\n"
						+ "a.ACCTNO=b.ACCTNO and b.ACCTNO=c.ACCTNO and a.ACCTNO=c.ACCTNO\r\n"
						+ "and a.PASSWORD='4tJ22wPRKQsXr8hazDXdVBzVJFtlEnce7zBfz2FVuYF/2nPdSnETbB82b/Sg0/Ce0OghjPdBu4NQmSjKEVXcmg==' \r\n"
						+ "and a.WEBACCESS='U' and a.LOGIN_RESET='N'\r\n" + " and a.CREATE_DATE > '" + date + "'\r\n"
						+ "and a.USERID LIKE 'rjrautomationtest%rbds%' ORDER BY NEWID()";
			else if (ctype.contains("Birthday"))
				query = "SELECT TOP (1) a.ACCTNO,a.USERID, b.FNAME, b.LNAME, b.DOB, c.ADD1, c.ZIP, c.CITY, c.STATE, a.PASSWORD, a.WEBACCESS, a.LOGIN_RESET, a.SOURCESYSTEM, a.SITECODE, a.MANUFACTURER \r\n"
						+ "FROM [RAI_APP].[dbo].[Consumer_Credentials] a, [RAI_BASE].[dbo].[CONSUMERS_ACCTNO] b, [RAI_BASE].[dbo].[CONSUMERS_ADDRESS] c where\r\n"
						+ "a.ACCTNO=b.ACCTNO and b.ACCTNO=c.ACCTNO and a.ACCTNO=c.ACCTNO\r\n"
						+ "and a.PASSWORD='4tJ22wPRKQsXr8hazDXdVBzVJFtlEnce7zBfz2FVuYF/2nPdSnETbB82b/Sg0/Ce0OghjPdBu4NQmSjKEVXcmg==' \r\n"
						+ "and a.WEBACCESS='U' and a.LOGIN_RESET='N'\r\n"
						+ " and a.CREATE_DATE > '2022-05-07 10:23:00'\r\n"
						+ "and a.USERID LIKE 'rjrautomationtest+%' and b.DOB LIKE '%-" + (month - 1)
						+ "-%' ORDER BY NEWID()";
			ResultSet result = DatabaseConnection.getResultSetProd(query);
			String acctno = DatabaseConnection.getDBColumnValue(result, "ACCTNO");
			result.beforeFirst();
			String userid = DatabaseConnection.getDBColumnValue(result, "USERID");
			data.add(acctno);
			data.add(userid);
			System.out.println("Data retrived from the db is :" + data);
			return data;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}

	public synchronized static String getElementScreenshotforIOSDevice(WebElement element) throws IOException {
		highLightElement(element, "green");
		File screenshotLocation = null;
		JavascriptExecutor js = (JavascriptExecutor) DriverManager.getAppiumDriver();
		Dimension d = element.getSize();
		int itemCodeEleX = ((Number) js.executeScript("return arguments[0].getBoundingClientRect().x;", element))
				.intValue();
		int itemCodeEleY = ((Number) js.executeScript("return arguments[0].getBoundingClientRect().y;", element))
				.intValue();
		int pixel = ((Number) js.executeScript("return window.devicePixelRatio;")).intValue();
		int iosHeaderOffset = getIosHeaderOffsetWrtYAxis(pixel);
		itemCodeEleX = itemCodeEleX * pixel;
		itemCodeEleY = itemCodeEleY * pixel + iosHeaderOffset;
		File scrFile = ((TakesScreenshot) DriverManager.getAppiumDriver()).getScreenshotAs(OutputType.FILE);
		BufferedImage fullImg = ImageIO.read(scrFile);
		BufferedImage eleScreenshot = fullImg.getSubimage(itemCodeEleX, itemCodeEleY, d.getWidth() * pixel,
				d.getHeight() * pixel);
		ImageIO.write(eleScreenshot, "png", scrFile);
		String path = "test-output/screenshots/" + UUID.randomUUID() + ".png";
		screenshotLocation = new File(System.getProperty("user.dir") + "/" + path);
		FileUtils.copyFile(scrFile, screenshotLocation);
		System.out.println("Screenshot Location:" + screenshotLocation.toString());
		return screenshotLocation.toString();
	}

	public synchronized static int getIosHeaderOffsetWrtYAxis(int iosScale) {
		switchToContext(DriverManager.getAppiumDriver(), "NATIVE");
		WebElement addressBarEle = DriverManager.getAppiumDriver()
				.findElement(By.xpath("//*[@label='Address']/parent::XCUIElementTypeOther"));
		int iosHeaderOffset = (addressBarEle.getLocation().getY() * iosScale)
				+ (addressBarEle.getSize().getHeight() * iosScale);
		System.out.println("Header Offset was - " + iosHeaderOffset);
		String CHROMIUMContext = "";
		// Cast to AndroidDriver or IOSDriver to access getContextHandles()
		Set<String> contextHandles = null;
		if (DriverManager.getAppiumDriver() instanceof AndroidDriver) {
			contextHandles = ((AndroidDriver) DriverManager.getAppiumDriver()).getContextHandles();
		} else if (DriverManager.getAppiumDriver() instanceof IOSDriver) {
			contextHandles = ((IOSDriver) DriverManager.getAppiumDriver()).getContextHandles();
		}
		if (contextHandles != null) {
			for (Object view : contextHandles)
				if (String.valueOf(view).contains("CHROMIUM"))
					CHROMIUMContext = String.valueOf(view);
		}
		switchToContext(DriverManager.getAppiumDriver(), CHROMIUMContext);
		return iosHeaderOffset;
	}

	public synchronized static String getElementScreenshotAndroid(WebElement element) {
		highLightElement(element, "green");
		String filename = UUID.randomUUID().toString();
		String path = "test-output\\screenshots\\" + filename + "\\";
		ElementSnapshot snapshot = Shutterbug.shootElement(DriverManager.getAppiumDriver(), element, true);
		String screenshotLocation = System.getProperty("user.dir") + "\\" + path;
		snapshot.withName(filename).save(screenshotLocation);
		System.out.println(screenshotLocation);
		return screenshotLocation + filename + ".png";
	}

	/*
	 * private WebElement getPageElement(HomePageObjects pageEnum) throws
	 * IOException { WebElement element; try { element =
	 * getElementByProperty(pageEnum.getProperty(),
	 * pageEnum.getLocatorType().toString(), true); if (element != null)
	 * System.out.println("Found the element: " + pageEnum.getObjectname()); else
	 * System.out.println("Element Not Found: " + pageEnum.getObjectname()); return
	 * element; } catch (Exception e) {
	 * GenericLib.updateExtentStatus("Mobile Site Login Page - get page element",
	 * pageEnum.toString() + " object is not defined or found.", Status.FAIL);
	 * return null; } }
	 */

	@SuppressWarnings({ "rawtypes" })
	public void closeSavePasswordPopUp() throws IOException {
		Set<String> views = null;
		try {
			if (DriverManager.getAppiumDriver() instanceof AndroidDriver) {
				((AndroidDriver) DriverManager.getAppiumDriver()).context("NATIVE_APP");
				views = ((AndroidDriver) DriverManager.getAppiumDriver()).getContextHandles();
				if (isElementPresent(HomePageObjects.neverSavePasswordPopUp))
					clickIfElementPresent(HomePageObjects.neverSavePasswordPopUp,
							"Never Option in  Save Password Pop Up");
				System.out.println("[INFO] The Save Password pop up is successfull closed");
				for (String view : views) {
					if (view.contains("CHROMIUM")) {
						((AndroidDriver) DriverManager.getAppiumDriver()).context(view);
					}
				}
			}
		} catch (NoSuchElementException e) {
			System.out.println("[INFO] The Save Password pop up is NOT displayed");
			for (String view : views) {
				if (view.contains("CHROMIUM")) {
					((AndroidDriver) DriverManager.getAppiumDriver()).context(view);
				}
			}
		}

		if (DriverManager.getAppiumDriver() instanceof AndroidDriver) {
			((AndroidDriver) DriverManager.getAppiumDriver()).context("CHROMIUM");
		}
	}

	public void clickusingTextOnScreen(String text) {
		try {
			Map<String, Object> params = new HashMap<>();
			params.put("label", text);
			params.put("threshold", 80);
			params.put("ignorecase", "case");
			DriverManager.getAppiumDriver().executeScript("mobile:button-text:click", params);
		} catch (Exception e) {
			System.out.println("No string is displayed:");
		}
	}

	public void authenticateAPI(String userName, String password) {
		PreemptiveBasicAuthScheme authScheme = new PreemptiveBasicAuthScheme();
		authScheme.setUserName(userName);
		authScheme.setPassword(password);
		RestAssured.authentication = authScheme;
		System.out.println("API authentication is done:");
	}

	@SuppressWarnings("rawtypes")
	public void allowBrowserPopUp() {
		try {
			if (DriverManager.getAppiumDriver() instanceof AndroidDriver)
				((AndroidDriver) DriverManager.getAppiumDriver()).context("NATIVE_APP");
			clickIfElementPresent(ChromeBrowserObjects.menuButton, "Menu");
			clickIfElementPresent(ChromeBrowserObjects.settings, "Browser Settings");
			scrollToMobileElement("Site settings");
			clickIfElementPresent(ChromeBrowserObjects.siteSettings, "SiteSettings");
			scrollToMobileElement("Pop-ups and redirects");
			clickIfElementPresent(ChromeBrowserObjects.popUpsAndRedirects, "Pop Up and redirect");
			clickIfElementPresent(ChromeBrowserObjects.unblockpopUpsToggle, "Toggle Pop up and redirects");
			while (isElementPresent(ChromeBrowserObjects.navigateUparrow)) {
				clickIfElementPresent(ChromeBrowserObjects.navigateUparrow, "Toggle Pop up and redirects");
			}
			if (DriverManager.getAppiumDriver() instanceof AndroidDriver)
				((AndroidDriver) DriverManager.getAppiumDriver()).context("CHROMIUM");
		} catch (NoSuchElementException e) {
			if (DriverManager.getAppiumDriver() instanceof AndroidDriver) {
				((AndroidDriver) DriverManager.getAppiumDriver()).context("CHROMIUM");
			}
		}
	}

	public void scrollToMobileElement(String elementText) {
		try {
			WebElement listItem = (WebElement) DriverManager.getAppiumDriver()
					.findElement(AppiumBy.AndroidUIAutomator(
							"new UiScrollable(new UiSelector()).scrollIntoView(text(\"" + elementText + "\"))"));
			System.out.println("[INFO] Scroll to the element with text:" + elementText + " " + listItem.isDisplayed());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public synchronized static void setFailureMessage(Exception e) {
		StringWriter sw = new StringWriter();
		e.printStackTrace(new PrintWriter(sw));
		failureMessage = sw.toString();
	}

	public synchronized static void failTestScript(String errorMessage, Exception e) {
		System.out.println("[INFO] " + errorMessage + e.getMessage());
		// DriverManager.getReportiumClient().stepEnd();
		System.out.println(errorMessage + e.getMessage());
		attachScreenshotForMobile(false);
		e.printStackTrace();
		setFailureMessage(e);
		fail(errorMessage + e.getMessage());
	}

	public synchronized static void switchToContext(RemoteWebDriver driver, String context) {
		RemoteExecuteMethod executeMethod = new RemoteExecuteMethod(driver);
		Map<String, String> params = new HashMap<String, String>();
		params.put("name", context);
		executeMethod.execute(DriverCommand.SWITCH_TO_CONTEXT, params);
	}

	public synchronized static void scrolltoSafari(RemoteWebDriver driver) {
		do {
			try {
				driver.findElement(By.xpath("//*[@value='Safari' and @visible='true']")).click();
				driver.findElement(By.xpath("//*[@value='Safari']/..")).click();
				break;

			} catch (Exception NoSuchElementException) {

				Map<String, Object> params = new HashMap<>();
				params.put("start", "20%,90%");
				params.put("end", "20%,30%");
				params.put("duration", "2");
				driver.executeScript("mobile:touch:swipe", params);
			}
		} while (true);
	}

	public static String deviceInfo(RemoteWebDriver driver, String deviceProperty) {
		Map<String, Object> params = new HashMap<>();
		params.put("property", deviceProperty);
		return (String) driver.executeScript("mobile:device:info", params);
	}

	@SuppressWarnings("rawtypes")
	public void clearChromeCookies() throws IOException, InterruptedException {
		try {
			if (DriverManager.getAppiumDriver() instanceof AndroidDriver)
				((AndroidDriver) DriverManager.getAppiumDriver()).context("NATIVE_APP");
			clickIfElementPresent(ChromeBrowserObjects.menuButton, "Menu");
			clickIfElementPresent(ChromeBrowserObjects.history, "History ");
			clickIfElementPresent(ChromeBrowserObjects.clearBrowsingData, "ClearBrowsingData ");
			clickIfElementPresent(ChromeBrowserObjects.advancedTab, "advancedTab");
			clickIfElementPresent(ChromeBrowserObjects.lastHourDropDown, "lastHourDropDown");
			clickIfElementPresent(ChromeBrowserObjects.drpDownAllTime, "drpDownAllTime");
			clickIfElementPresent(ChromeBrowserObjects.siteSettings, "SiteSettings");
			clickIfElementPresent(ChromeBrowserObjects.clearData, "Login Page - ClearBrowser");
			if (isElementPresent(By.xpath("//*[@resource-id='android:id/button1']")))
				clickIfElementPresent(ChromeBrowserObjects.clearDataPopup, "Login Page - ClearDataPopup");
			clickIfElementPresent(ChromeBrowserObjects.historyclose, "Close the History section on Chrome browser");
			if (DriverManager.getAppiumDriver() instanceof AndroidDriver)
				((AndroidDriver) DriverManager.getAppiumDriver()).context("CHROMIUM");

		} catch (NoSuchElementException e) {
			if (DriverManager.getAppiumDriver() instanceof AndroidDriver) {
				((AndroidDriver) DriverManager.getAppiumDriver()).context("CHROMIUM");
			}
		}

	}

	public void clearSafariCache() throws InterruptedException {
		Map<String, Object> params = new HashMap<>();
		params.put("identifier", "com.apple.Preferences");
		((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:application:open", params);
		params.clear();
		params.put("identifier", "com.apple.Preferences");
		((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:application:close", params);
		params.clear();
		params.put("identifier", "com.apple.Preferences");
		((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:application:open", params);
		switchToContext((RemoteWebDriver) DriverManager.getAppiumDriver(), "NATIVE_APP");
		if (deviceInfo((RemoteWebDriver) DriverManager.getAppiumDriver(), "model").contains("iPad")) {
			scrolltoSafari((RemoteWebDriver) DriverManager.getAppiumDriver());
		} else {
			params.clear();
			params.put("end", "50%,50%");
			params.put("start", "50%,30%");
			params.put("duration", "1");
			((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:touch:swipe", params);
			((RemoteWebDriver) DriverManager.getAppiumDriver()).findElementByXPath("//*[@label=\"Search\"]")
					.sendKeys("Safari");
			try {
				((RemoteWebDriver) DriverManager.getAppiumDriver())
						.findElementByXPath("//XCUIElementTypeCell[2]//*[@name='Safari']").click();
			} catch (Exception e) {
				((RemoteWebDriver) DriverManager.getAppiumDriver()).findElementByXPath("//*[@label='Safari']").click();
			}
		}
		Thread.sleep(1000);
		params.clear();
		params.put("start", "50%,90%");
		params.put("end", "50%,20%");
		params.put("duration", "1");
		((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:touch:swipe", params);
		WebElement el;
		try {
			el = DriverManager.getAppiumDriver()
					.findElement(By.xpath("//*[@value=\"Clear History and Website Data\" and @visible='true']"));
		} catch (Exception e) {
			params.clear();
			params.put("start", "50%,70%");
			params.put("end", "50%,20%");
			params.put("duration", "1");
			((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:touch:swipe", params);
			el = DriverManager.getAppiumDriver()
					.findElement(By.xpath("//*[@value=\"Clear History and Website Data\" and @visible='true']"));
		}
		if (el.isDisplayed()) {
			el.click();
		} else {
			// Swipe one more time
		}
		if (deviceInfo((RemoteWebDriver) DriverManager.getAppiumDriver(), "model").contains("iPad")) {
			DriverManager.getAppiumDriver().findElement(By.xpath("//*[@label=\"Clear\"]")).click();
		} else {
			DriverManager.getAppiumDriver().findElement(By.xpath("//*[@label=\"Clear History and Data\"]")).click();
		}
	}

	@SuppressWarnings("rawtypes")
	public void clearIOSPrivacyLocation() throws InterruptedException {
		// open and close settings app, so that it comes to a default state.
		Map<String, Object> params = new HashMap<>();
		params.put("identifier", "com.apple.Preferences");
		((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:application:open", params);
		params.clear();
		params.put("identifier", "com.apple.Preferences");
		((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:application:close", params);
		params.clear();
		params.put("identifier", "com.apple.Preferences");
		((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:application:open", params);
		switchToContext((RemoteWebDriver) DriverManager.getAppiumDriver(), "NATIVE_APP");
		if (deviceInfo((RemoteWebDriver) DriverManager.getAppiumDriver(), "model").contains("iPad")) {
			// scroll and search safari button
			scrolltoSafari((RemoteWebDriver) DriverManager.getAppiumDriver());
		} else {
			// scroll up and enter Privacy in search label for iOS devices
			params.clear();
			params.put("end", "50%,50%");
			params.put("start", "50%,30%");
			params.put("duration", "1");
			((RemoteWebDriver) DriverManager.getAppiumDriver()).executeScript("mobile:touch:swipe", params);
			((RemoteWebDriver) DriverManager.getAppiumDriver()).findElementByXPath("//*[@label=\"Search\"]")
					.sendKeys("Privacy");
			((IOSDriver) DriverManager.getAppiumDriver()).hideKeyboard();
			try {
				((RemoteWebDriver) DriverManager.getAppiumDriver())
						.findElementByXPath("//*[@label=\"Location, Privacy\"]").click();
			} catch (Exception e) {
				((RemoteWebDriver) DriverManager.getAppiumDriver()).findElementByXPath("//*[@label='Privacy']").click();
			}
		}
		/*
		 * WebElement el =
		 * DriverManager.getAppiumDriver().findElement(By.
		 * xpath("//*[@value=\"Location Services\"]"));
		 * 
		 * if (el.isDisplayed()) { el.click(); } else { // Swipe one more time
		 * 
		 * }
		 */
		WebElement safariwebsites = DriverManager.getAppiumDriver()
				.findElement(By.xpath("//XCUIElementTypeStaticText[contains(@label,'Safari')]"));
		// WebElement safariwebsites =
		// DriverManager.getAppiumDriver().findElement(By.xpath("//*[@label=\"Safari
		// Websites, Requested
		// your location within the last 24 hours\"]"));
		if (safariwebsites.isDisplayed()) {
			safariwebsites.click();
		} else {
			// Swipe one more time
		}
		// WebElement askNextTime =
		// DriverManager.getAppiumDriver().findElement(By.xpath("//*[@value=\"Ask Next
		// Time\"]"));
		WebElement askNextTime = DriverManager.getAppiumDriver()
				.findElement(By.xpath("//*[@name=\"Ask Next Time Or When I Share\"] | //*[@value=\"Ask Next Time\"]"));
		if (askNextTime.isDisplayed()) {
			askNextTime.click();
			Thread.sleep(5000);
		} else {
			// Swipe one more time

		}
	}

	@SuppressWarnings({ "rawtypes", "unused" })
	public void allowPopUpIOS() throws IOException {
		// DriverManager.getReportiumClient().stepStart("Validate that the popup is
		// present");
		if (DriverManager.getAppiumDriver() instanceof IOSDriver) {
			Set<String> views = null;
			try {
				((IOSDriver) DriverManager.getAppiumDriver()).context("NATIVE_APP");
				int pressX = DriverManager.getAppiumDriver().manage().window().getSize().width / 2;
				int bottomY = DriverManager.getAppiumDriver().manage().window().getSize().height / 4;
				try {
					TouchAction touchAction = new TouchAction((IOSDriver) DriverManager.getAppiumDriver());
					touchAction.press(PointOption.point(pressX, bottomY)).release().perform();
					Thread.sleep(500);
					touchAction.press(PointOption.point(pressX, bottomY)).release().perform();
					Thread.sleep(500);
					touchAction.press(PointOption.point(pressX, bottomY)).release().perform();
					Thread.sleep(500);
					touchAction.press(PointOption.point(pressX, bottomY)).release().perform();
					Thread.sleep(500);
					touchAction.press(PointOption.point(pressX, bottomY)).release().perform();
					Thread.sleep(2000);
					// DriverManager.getReportiumClient()
					// .reportiumAssert("The popup is present and clicked on it successfully",
					// true);
					// DriverManager.getReportiumClient().stepEnd();
					addStepLog("The popup is present and clicked on it successfully");
				} catch (Exception e) {
					// DriverManager.getReportiumClient().reportiumAssert("The popup is NOT present
					// and NOT clicked on it",
					// false);
					// DriverManager.getReportiumClient().stepEnd();
				}
				((IOSDriver) DriverManager.getAppiumDriver()).context("NATIVE_APP");
				if (isElementPresent(By.xpath("//*[@label=\"Allow While Using App\"] | //*[@label='Allow']"))) {
					clickIfElementPresent(LocationServices.alertAllowwhileusingappButton, "Alert Allow Button");
				}
				if (isElementPresent(By.xpath("//*[@label=\"Allow While Using App\"] | //*[@label='Allow']"))) {
					clickIfElementPresent(LocationServices.alertAllowwhileusingappButton, "Alert Allow Button");
				}
				((IOSDriver) DriverManager.getAppiumDriver()).context("CHROMIUM");
			} catch (NoSuchElementException e) {
			}
			((IOSDriver) DriverManager.getAppiumDriver()).context("CHROMIUM");
		}
	}

	@SuppressWarnings({ "rawtypes", "null" })
	public void allowPopUp() throws IOException {
		Set<String> views = null;
		try {
			if (DriverManager.getAppiumDriver() instanceof AndroidDriver) {
				((AndroidDriver) DriverManager.getAppiumDriver()).context("NATIVE_APP");

				if (isElementPresent(By.xpath(
						"//*[@resource-id='com.android.permissioncontroller:id/permission_allow_foreground_only_button']")))
					clickIfElementPresent(LocationServices.btnAllowOnly, "Allow Only While Using This App");

				// if
				// (isElementPresent(By.xpath("//android.widget.Button[@resource-id=\"com.android.chrome:id/negative_button\"]")));
				//
				// clickIfElementPresent(LocationServices.btnblock, "Allow Only While Using This
				// App");
				//

				// id(block)=com.android.chrome:id/negative_button

			}

			if (isElementPresent(By.id("com.android.chrome:id/positive_button"))) {
				clickIfElementPresent(LocationServices.btnAllow, "Allow Device Location Setting Button");

			}
		} catch (NoSuchElementException e) {
			for (String view : views) {
				if (view.contains("CHROMIUM")) {
					((AndroidDriver) DriverManager.getAppiumDriver()).context(view);
				}
			}
		}
		if (DriverManager.getAppiumDriver() instanceof AndroidDriver) {
			((AndroidDriver) DriverManager.getAppiumDriver()).context("CHROMIUM");
		}
	}

	public static void sendKeysJavascript(By element, String keysToSend) {
		WebElement el = DriverManager.getAppiumDriver().findElement(element);
		JavascriptExecutor ex = (JavascriptExecutor) DriverManager.getAppiumDriver();
		ex.executeScript("arguments[0].value='" + keysToSend + "';", el);
	}

	public boolean swipeFromUpToBottom(WebDriver driver) {
		try {
			JavascriptExecutor js = (JavascriptExecutor) driver;
			HashMap<String, String> scrollObject = new HashMap<String, String>();
			scrollObject.put("direction", "up");
			js.executeScript("mobile: scroll", scrollObject);
			System.out.println("Swipe up was Successfully done.");
		} catch (Exception e) {
			System.out.println("swipe up was not successfull");
		}
		return false;
	}

	@SuppressWarnings({ "rawtypes", "null" })
	public void blockPopUp() throws IOException {
		Set<String> views = null;
		try {
			if (DriverManager.getAppiumDriver() instanceof AndroidDriver) {
				((AndroidDriver) DriverManager.getAppiumDriver()).context("NATIVE_APP");

				if (isElementPresent(
						By.xpath("//android.widget.Button[@resource-id=\"com.android.chrome:id/negative_button\"]")))
					;

				clickIfElementPresent(LocationServices.btnblock, "Allow Only While Using This App");
			}

			if (isElementPresent(By.id("com.android.chrome:id/nrgative_button"))) {
				clickIfElementPresent(LocationServices.btnblock, "Allow Device Location Setting Button");

			}
		}

		catch (NoSuchElementException e) {
			for (String view : views) {
				if (view.contains("CHROMIUM")) {
					((AndroidDriver) DriverManager.getAppiumDriver()).context(view);
				}
			}
		}
		if (DriverManager.getAppiumDriver() instanceof AndroidDriver) {
			((AndroidDriver) DriverManager.getAppiumDriver()).context("CHROMIUM");
		}
	}
}
