/*
 *  © [2020] Cognizant. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.rai.framework;

/**
 * Enumeration to represent the browser to be used for execution
 * 
 * <AUTHOR>
 */
public enum Browser {
	CHROME("chrome"), FIREFOX("firefox"), GHOST_DRIVER("phantomjs"), INTERNET_EXPLORER("internet explorer"),CHROME_HEADLESS("chrome_headless"), EDGE("edge"), SAFARI("Safari");
	private String value;
	
	Browser(String value) {
		this.value = value;
	}

	public String getValue() {
		return value;
	}
	
	public static Browser browserValue(String letter) {
        for (Browser s : values() ){
            if (s.name().equalsIgnoreCase(letter)) return s;
        }
        return null;
    }
}