Feature: Camelsnus SPA - Hamburger Menu
As a RJR user, I shall be validating Camelsnus Coupon Hamburger Functionality

@CamelsnusValidateHamburgerMenu
  Scenario Outline: Validate Hamburger Menu And the navigation to Camelsnus Coupon Page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I validate the elements in Hamburger Menu
      And I click on coupons link
     Then I validate I'm on the Coupons Home Page  
  
  @CamelsnusValidateHamburgerMenu_QA
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                              | Username                         | Password  | Loc                                            | Env | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://aem-stage.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Camelsnus | Murphy               | 741592   | https://aem-stage.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 79 E Bromley Ln, Brighton, CO 80601 | QA  | 
      | Camelsnus | Sheetz               | 595324   | https://aem-stage.camelsnus.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Camelsnus | Speedway             | 529181   | https://aem-stage.camelsnus.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @CamelsnusValidateHamburgerMenu_Prod
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                        | Username                                  | Password  | Loc                                            | Env  | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://www.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Camelsnus | Murphy               | 741592   | https://www.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 79 E Bromley Ln, Brighton, CO 80601            | PROD | 
      | Camelsnus | Sheetz               | 595324   | https://www.camelsnus.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Camelsnus | Speedway             | 529181   | https://www.camelsnus.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @CamelsnusLogoutfromHamburgerMenu
  Scenario Outline: Validate that user can logout from the Camelsnus Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to Hamburger menu
      And I click on logout link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @CamelsnusLogoutfromHamburgerMenu_QA    
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                              | Username                         | Password  | Loc                                            | Env | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://aem-stage.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Camelsnus | Murphy               | 741592   | https://aem-stage.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 79 E Bromley Ln, Brighton, CO 80601 | QA  | 
      | Camelsnus | Sheetz               | 595324   | https://aem-stage.camelsnus.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Camelsnus | Speedway             | 529181   | https://aem-stage.camelsnus.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @CamelsnusLogoutfromHamburgerMenu_Prod
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                        | Username                                  | Password  | Loc                                            | Env  | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://www.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Camelsnus | Murphy               | 741592   | https://www.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 79 E Bromley Ln, Brighton, CO 80601            | PROD | 
      | Camelsnus | Sheetz               | 595324   | https://www.camelsnus.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Camelsnus | Speedway             | 529181   | https://www.camelsnus.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @NavigateCamelsnusMobilesiteHamburgerMenu 
  Scenario Outline: Validate that user can navigate to Camelsnus brand mobile site from the Hamburger menu for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand>
      And I click on the Understood button
      And I navigate to Hamburger menu
      And I click on brand link in Hamburger menu
     Then I validate I'm on the brand home page for the brand <BrandName>
  
  @NavigateCamelsnusMobilesiteHamburgerMenu_QA      
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                              | Username                         | Password  | Loc                                            | Env | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://aem-stage.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | Camelsnus | Murphy               | 741592   | https://aem-stage.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 79 E Bromley Ln, Brighton, CO 80601 | QA  | 
      | Camelsnus | Sheetz               | 595324   | https://aem-stage.camelsnus.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | Camelsnus | Speedway             | 529181   | https://aem-stage.camelsnus.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @NavigateCamelsnusMobilesiteHamburgerMenu_Prod
    Examples: 
      | Brand     | Retailer             | RBDScode | URL                                        | Username                                  | Password  | Loc                                            | Env  | 
      | Camelsnus | 7-Eleven Corporation | 572896   | https://www.camelsnus.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Camelsnus | Murphy               | 741592   | https://www.camelsnus.com/?RBDSCode=741592 | <EMAIL> | Password1 | 79 E Bromley Ln, Brighton, CO 80601            | PROD | 
      | Camelsnus | Sheetz               | 595324   | https://www.camelsnus.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Camelsnus | Speedway             | 529181   | https://www.camelsnus.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  
