package com.rai.steps;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathFactory;

import org.json.XML;
import org.json.simple.JSONObject;
import org.testng.Assert;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import com.rai.framework.FrameworkException;
import com.rai.framework.HeadersForAPI;

import io.restassured.RestAssured;
import io.restassured.authentication.PreemptiveBasicAuthScheme;
import io.restassured.http.ContentType;
import io.restassured.response.Response;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class APIReusuableLibrary extends MasterSteps {

	public enum SERVICEMETHOD {
		GET, POST, PUT, DELETE;
	}

	public enum SERVICEFORMAT {
		JSON, XML, TEXT, FILE;
	}

	public enum ASSERT_RESPONSE {
		BODY, TAG, HEADER, LIST;

	}

	public enum COMPARISON {
		IS_EXISTS, IS_EQUALS, IS_CONTAINS;
	}

	/**
	 * Function to read input file from given Path
	 * 
	 * @param inputFilePath The Path of the given File
	 * @return The String of the input File
	 * 
	 */
	public String readInput(String inputFilePath) {
		String intputFileContent = "";
		BufferedReader bufferReader = null;
		try {
			String line;
			File inputFile = new File(inputFilePath);
			FileReader fileReader = new FileReader(inputFile);
			bufferReader = new BufferedReader(fileReader);
			while ((line = bufferReader.readLine()) != null) {
				intputFileContent += line.trim();
			}
		} catch (FileNotFoundException x) {
			throw new FrameworkException(x.getMessage());
		} catch (IOException ex) {
			throw new FrameworkException(ex.getMessage());
		} finally {
			try {
				if (bufferReader != null) {
					bufferReader.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return intputFileContent;

	}

	/**
	 * Function to get value from given XML Tag
	 * 
	 * @param response      The Response of the API
	 * @param tagToValidate The XML Tag/Node from which value has to be fetched
	 * @return The String value
	 * 
	 */
	public String extractValue(Response response, String tagToValidate) {

		if (!tagToValidate.contains("//")) {
			return jsonParser(response, tagToValidate);
		} else {
			return xmlParser(response, tagToValidate);
		}
	}

	/**
	 * Function to get the response of an API
	 * 
	 * @param url        The URL of the Application
	 * @param methodType The Service Method GET,POST, PUT ,DELETE
	 *                   {@link SERVICEMETHOD}
	 * @param headersMap The headers passed as Map object , refer to
	 *                   {@link HeadersForAPI} to create custom Headers
	 * @param statusCode The Expected Status Code
	 * @return The Response {@link Response}
	 * @throws IOException
	 * 
	 */
	public Response sendNReceive(String url, SERVICEMETHOD methodType, Map<String, String> headersMap, int statusCode)
			throws IOException {
		Response response = null;
		try {
			switch (methodType) {
			case GET:

				if (headersMap != null) {
					response = RestAssured.given().relaxedHTTPSValidation().headers(headersMap).get(url);
				} else {
					response = RestAssured.given().relaxedHTTPSValidation().get(url);
				}

				break;

			case DELETE:
				if (headersMap != null) {
					response = RestAssured.given().relaxedHTTPSValidation().headers(headersMap).delete(url);
				} else {
					response = RestAssured.given().relaxedHTTPSValidation().headers(headersMap).delete(url);
				}
				break;

			default:
				break;
			}
		}

		catch (AssertionError x) {
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return response;
	}

	/**
	 * Function to get the response of an API
	 * 
	 * @param url             The URL of the Application
	 * @param methodType      The Service Method GET,POST, PUT ,DELETE
	 *                        {@link SERVICEMETHOD}
	 * @param postBodyType    The Format of Post Body {@link SERVICEFORMAT}
	 * @param postBodyContent The Post Body Content
	 * @param headersMap      The headers passed as Map object , refer to
	 *                        {@link HeadersForAPI} to create custom Headers
	 * @param statusCode      The Expected Status Code
	 * @return The Response {@link Response}
	 * @throws IOException
	 * 
	 */
	public Response sendNReceive(String url, SERVICEMETHOD methodType, SERVICEFORMAT postBodyType,
			String postBodyContent, Map<String, String> headersMap, int statusCode) throws IOException {
		Response response = null;
		try {
			Object postBody = getPostBodyContent(postBodyContent, postBodyType);
			ContentType contentType = getPostContentType(postBodyType);
			switch (methodType) {
			case POST:

				if (postBody instanceof File) {/* File */
					if (headersMap != null) {
						response = RestAssured.given().contentType(contentType).relaxedHTTPSValidation()
								.body((File) postBody).headers(headersMap).post(url);
					} else {
						response = RestAssured.given().contentType(contentType).relaxedHTTPSValidation()
								.body((File) postBody).post(url);
					}
				} else if (postBody instanceof String) {/* String */
					if (headersMap != null) {
						response = RestAssured.given().contentType(contentType).relaxedHTTPSValidation().body(postBody)
								.headers(headersMap).post(url);
					} else {
						response = RestAssured.given().contentType(contentType).relaxedHTTPSValidation().body(postBody)
								.post(url);
					}
				}
				break;

			case PUT:

				if (postBody instanceof File) {/* File */
					if (headersMap != null) {
						response = RestAssured.given().contentType(contentType).relaxedHTTPSValidation()
								.body((File) postBody).headers(headersMap).put(url);
					} else {
						response = RestAssured.given().contentType(contentType).relaxedHTTPSValidation()
								.body((File) postBody).put(url);
					}
				} else if (postBody instanceof String) {/* String */
					if (headersMap != null) {
						response = RestAssured.given().contentType(contentType).relaxedHTTPSValidation().body(postBody)
								.headers(headersMap).put(url);

					} else {
						response = RestAssured.given().contentType(contentType).relaxedHTTPSValidation().body(postBody)
								.put(url);

					}
				}
				break;

			default:
				break;
			}
		}

		catch (AssertionError x) {
			x.printStackTrace();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return response;
	}

	/**
	 * Function to get updated String value
	 * 
	 * @param inputString The input String
	 * @param paramVar    The Variable
	 * @param paramValue  The Variable Value to be updated
	 * @return The updated String
	 * 
	 */
	public String updateContent(String inputString, String paramVar, String paramValue) {
		String updatedString = null;
		try {
			updatedString = inputString.replaceAll("%%" + paramVar + "%%", paramValue);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return updatedString;
	}

	/**
	 * Function to get the response of an API
	 * 
	 * @param url              The URL of the Application
	 * @param assertResponse   The Type of Assertion required for Response
	 *                         {@link ASSERT_RESPONSE}
	 * @param tagToValidate    The Tag to be validated
	 * @param expectedResponse The Expected Response
	 * @param comparison       The Type of Comparison to be done {@link COMPARISON }
	 * @throws IOException
	 * 
	 */
	public boolean assertIt(Response response, ASSERT_RESPONSE assertResponse, String tagToValidate,
			Object expectedResponse, COMPARISON comparison) throws IOException {
		Object actualResponse = null;
		boolean retval = false;
		switch (assertResponse) {

		case BODY:

			actualResponse = response.body().asString();
			ObjectMapper objectMapper = new ObjectMapper();
			JsonNode jsonNodeActualResponse = objectMapper.readTree(actualResponse.toString());
			JsonNode jsonNodeExpectedResponse = objectMapper.readTree(expectedResponse.toString());
			if (compareIt(comparison, actualResponse, expectedResponse)) {
				Assert.assertEquals(jsonNodeActualResponse, jsonNodeExpectedResponse);
				retval = true;
			} else {
				Assert.assertEquals(jsonNodeActualResponse, jsonNodeExpectedResponse);
				retval = false;
			}
			break;
		case TAG:
			if (!tagToValidate.contains("//"))
				actualResponse = jsonParser(response, tagToValidate);
			else
				actualResponse = xmlParser(response, tagToValidate);
			if (compareIt(comparison, actualResponse, expectedResponse))
				retval = true;
			else
				retval = false;

			break;
		case HEADER:
			actualResponse = response.contentType();
			if (compareIt(comparison, actualResponse, expectedResponse)) {
				Assert.assertEquals(actualResponse, expectedResponse);
				retval = true;
			} else {
				Assert.assertEquals(actualResponse, expectedResponse);
				retval = false;
			}
			break;

		case LIST:
			if (!tagToValidate.contains("//"))
				actualResponse = jsonParserAsList(response, tagToValidate);
			else
				actualResponse = xmlParserAsList(response, tagToValidate);
			if (compareIt(comparison, actualResponse, expectedResponse))
				retval = true;
			else
				retval = false;
			break;

		default:
			break;

		}
		return retval;
	}

	public void assertKeyValue(JSONObject dataObj, String key, String expectedValue) throws IOException {

		if (dataObj.get(key).equals(expectedValue)) {
			Assert.assertEquals(dataObj.get(key), expectedValue);
		} else {
			Assert.assertEquals(dataObj.get(key), expectedValue);
		}
	}

	public boolean assertKeyValueB(JSONObject dataObj, String key, String expectedValue) throws IOException {

		if (dataObj.get(key).equals(expectedValue)) {
			// Assert.assertEquals(dataObj.get(key), expectedValue);
			return true;
		} else {
			// Assert.assertEquals(dataObj.get(key), expectedValue);
			return false;
		}
	}

	/**
	 * Function to estabilish Connection to access DB
	 * 
	 * 
	 */
	public String establishConnectionNExecute(String dbConnection, String dbUname, String dbPassword, String sqlQuery) {
		String queryOutput = null;
		Connection con = null;
		try {
			Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
			con = DriverManager.getConnection(dbConnection, dbUname, dbPassword);
			Statement stmt = (Statement) con.createStatement();
			ResultSet rs = stmt.executeQuery(sqlQuery);
			while (rs.next()) {
				queryOutput = rs.getString(1);
				System.out.println(queryOutput);
			}
		} catch (SQLException sq) {
			System.out.println("SQL Exception");

		} catch (ClassNotFoundException cs) {
			System.out.println("ClassNotFoundException");
		} finally {
			try {
				if (con != null) {
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return queryOutput;

	}

	/**
	 * Function to estabilish Connection to access DB
	 * 
	 * 
	 */
	public List<String> establishConnectionNExecuteNGetList(String dbConnection, String dbUname, String dbPassword,
			String sqlQuery) {
		List<String> queryOutput = new ArrayList<String>();
		Connection con = null;
		try {
			Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
			con = DriverManager.getConnection(dbConnection, dbUname, dbPassword);
			Statement stmt = (Statement) con.createStatement();
			ResultSet rs = stmt.executeQuery(sqlQuery);
			ResultSetMetaData rsmt = rs.getMetaData();
			int columnCount = rsmt.getColumnCount();
			List<List<String>> rowList = new LinkedList<List<String>>();
			while (rs.next()) {
				rowList.add(queryOutput);
				for (int column = 1; column <= columnCount; column++) {
					final Object value = rs.getObject(column);
					String valueAsString = String.valueOf(value);
					if (!valueAsString.contains("."))
						valueAsString = valueAsString + ".0";
					queryOutput.add(valueAsString.toUpperCase());
				}
			}
		} catch (SQLException sq) {
			sq.printStackTrace();
		} catch (ClassNotFoundException cf) {
			cf.printStackTrace();
		} finally {
			try {
				if (con != null) {
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return queryOutput;
	}

	private Object getPostBodyContent(String postBodyContent, SERVICEFORMAT postBodyType) {

		if (postBodyType.equals(SERVICEFORMAT.FILE)) {
			File file = new File(postBodyContent);
			return file;
		} else {
			return postBodyContent;
		}
	}

	private ContentType getPostContentType(SERVICEFORMAT contentTypes) {
		ContentType contentType = null;
		switch (contentTypes) {
		case FILE:
			contentType = ContentType.BINARY;
			break;
		case JSON:
			contentType = ContentType.JSON;
			break;
		case XML:
			contentType = ContentType.XML;
			break;
		case TEXT:
			contentType = ContentType.TEXT;
			break;
		default:
			break;
		}
		return contentType;
	}

	private String xmlParser(Response response, String tagToValidate) {

		return response.xmlPath().getString(tagToValidate);
	}

	private String jsonParser(Response response, String tagToValidate) {

		return response.jsonPath().getString(tagToValidate);
	}

	private List<String> xmlParserAsList(Response response, String tagToValidate) {

		return response.xmlPath().getList(tagToValidate);
	}

	private Object jsonParserAsList(Response response, String tagToValidate) {

		return response.jsonPath().getList(tagToValidate);
	}

	private Boolean compareIt(COMPARISON comparator, Object actualResponse, Object expectedResponse)
			throws JsonMappingException, JsonProcessingException {

		switch (comparator) {
		case IS_EQUALS:
			ObjectMapper objectMapper = new ObjectMapper();
			JsonNode jsonNodeActualResponse = objectMapper.readTree(actualResponse.toString());
			JsonNode jsonNodeExpectedResponse = objectMapper.readTree(expectedResponse.toString());
			if ((jsonNodeActualResponse).equals(jsonNodeExpectedResponse))
				return true;
			else
				return false;

		case IS_CONTAINS:
			if (((String) actualResponse).contains((String) expectedResponse))
				return true;
			else
				return false;

		case IS_EXISTS:
			if (((String) actualResponse).contains((String) expectedResponse))
				return true;
			else
				return false;
		default:
			break;
		}

		return null;
	}

	public String xmlParserByTag(Response response, String tagToValidate) throws SAXException {

		String elementValue = null;
		String inputXml = response.asString();
		String responseBody = response.getBody().asString();
		org.json.JSONObject json = new org.json.JSONObject(responseBody);
		inputXml = XML.toString(json);
		DocumentBuilder builder;
		Document doc;
		try {
			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
			factory.setNamespaceAware(false);
			InputStream in = new ByteArrayInputStream(inputXml.getBytes("utf-8"));
			builder = factory.newDocumentBuilder();
			doc = builder.parse(in);
			XPathFactory xPathfactory = XPathFactory.newInstance();
			XPath xpath = xPathfactory.newXPath();
			XPathExpression expr = xpath.compile(tagToValidate);
			elementValue = (String) expr.evaluate(doc, XPathConstants.STRING);
		} catch (Exception e) {
			throw new FrameworkException("API Error", e.getMessage());
		} finally {
		}
		return elementValue;

	}

	public void setProxy() {
		// final String username = "midathr1";
		// final String password = "nov-2021";
		String proxyHost = "gateway.zscaler.net";
		int proxyPort = 80;
		RestAssured.proxy(proxyHost, proxyPort);
		System.out.println("Proxy is set:");
	}

	public void authenticateAPI(String userName, String password) {
		PreemptiveBasicAuthScheme authScheme = new PreemptiveBasicAuthScheme();
		authScheme.setUserName(userName);
		authScheme.setPassword(password);
		RestAssured.authentication = authScheme;
		System.out.println("Authentication is done:");
	}

	public void authenticateOauth2(String accessToken) {
		RestAssured.oauth2(accessToken);

	}

	public String getuserIdWithOffers() {
		try {

			authenticateAPI("Test_user", "Welcome@1212");
			HeadersForAPI headers = new HeadersForAPI();
			Map<String, String> headersMap = headers.getHeaders3();
			Response response;
			String acctNo;
			String userid;
			boolean respCode;
			do {
				List<String> data = getDataFromDb();
				acctNo = data.get(0);
				userid = data.get(1);
				String url = "http://*************:15000/digitaloffers/v1/" + acctNo
						+ "/CAMEL?Channel=APP&State=NC&SessionID=123456789";
				response = sendNReceive(url, SERVICEMETHOD.GET, headersMap, 201);
				String responseBody = response.getBody().asString();
				System.out.println("Response in runit : " + responseBody);
				respCode = responseBody.contains("Offers");
				System.out.println("Response contains Offers:" + respCode);
			} while (!respCode);
			return userid;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public String getuserIdWithOffers(String brand) {
		try {
			authenticateAPI("Test_user", "Welcome@1212");
			HeadersForAPI headers = new HeadersForAPI();
			Map<String, String> headersMap = headers.getHeaders3();
			Response response;
			String acctNo;
			String userid;
			//String dob;
			boolean respCode;
			do {
				List<String> data = getDataFromDb();
				acctNo = data.get(0);
				userid = data.get(1);
				String url = "http://172.25.173.79:15000/digitaloffers/v1/" + acctNo + "/"+brand+"?Channel=SPA&State=NC&SessionID=123456789";	
				response = sendNReceive(url, SERVICEMETHOD.GET, headersMap, 201);
				String responseBody = response.getBody().asString();
				System.out.println("Response in runit : " + responseBody);
				respCode = responseBody.contains("Offers");
			} while (!respCode);
			return userid;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public String getuserIdWithOffers(String brand, String rbdsCode) {
		try {
			System.out.println("QA");
			authenticateAPI("Test_user", "Welcome@1212");
			HeadersForAPI headers = new HeadersForAPI();
			Map<String, String> headersMap = headers.getHeaders3();
			Response response;
			String acctNo;
			String userid;
			int count=1;
			boolean respCode;
			if(brand.contains("Luckystrike"))
				brand = "Lucky strike";
			brand= brand.toUpperCase();
			do {
				List<String> data = getDataFromDb();
				acctNo = data.get(0);
				userid = data.get(1);
				String url = "http://172.25.173.79:15000/digitaloffers/v1/" + acctNo
						+ "/"+brand+"?Channel=SPA&SessionID=123456789&RBDSCode="+rbdsCode;
				response = sendNReceive(url, SERVICEMETHOD.GET, headersMap, 201);
				String responseBody = response.getBody().asString();
				System.out.println("Response in runit : " + responseBody);
				respCode = responseBody.contains("Offers");	
				count++;
				if(count>20)
					break;
			} while (!respCode);
			return userid;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public String getuserIdWithOffersprod(String brand, String rbdsCode) {
		try {
			System.out.println("Prod");
			authenticateAPI("prd_autotest_user", "7Bms4>9R~QSW");
			HeadersForAPI headers = new HeadersForAPI();
			Map<String, String> headersMap = headers.getHeaders3();
			Response response;
			String acctNo;
			String userid;
			int count=1;
			boolean respCode;
			if(brand.contains("Luckystrike"))
				brand = "Lucky strike";
			brand= brand.toUpperCase();
			do {
				List<String> data = getDataFromDbProd();
				acctNo = data.get(0);
				userid = data.get(1);
				String url = "http://172.25.97.97:15000/digitaloffers/v1/" + acctNo
						+ "/"+brand+"?Channel=SPA&SessionID=123456789&RBDSCode="+rbdsCode;
				response = sendNReceive(url, SERVICEMETHOD.GET, headersMap, 201);
				String responseBody = response.getBody().asString();
				System.out.println("Response in runit : " + responseBody);
				respCode = responseBody.contains("Offers");
				count++;
				if(count>20)
					break;	
			} while (!respCode);
			return userid;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public String getuserIdWithOffers(String brand, String rbdsCode, String ctype) {
		try {

			authenticateAPI("Test_user", "Welcome@1212");
			HeadersForAPI headers = new HeadersForAPI();
			Map<String, String> headersMap = headers.getHeaders3();
			Response response;
			String acctNo;
			String userid;
			int count = 1;
			boolean respCode = false;
			if(brand.contains("Luckystrike"))
				brand = "Lucky strike";
			brand= brand.toUpperCase();
			do {
				List<String> data = getDataFromDb(ctype);
				acctNo = data.get(0);
				userid = data.get(1);
				String url = "http://172.25.173.79:15000/digitaloffers/v1/" + acctNo
						+ "/"+brand+"?Channel=SPA&SessionID=123456789%RBDSCode="+rbdsCode;
				response = sendNReceive(url, SERVICEMETHOD.GET, headersMap, 201);
				String responseBody = response.getBody().asString();
				System.out.println("Response in runit : " + responseBody);
				if(ctype.contains("Birthday"))
					respCode = responseBody.contains("Offers") && responseBody.contains("BIRTHDAY");
				else if(ctype.contains("Welcome"))
					respCode = responseBody.contains("Offers") && responseBody.contains("WELCOME");
				count++;
				if(count>20)
					break;
			} while (!respCode);
			return userid;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public String getuserIdWithOffersprod(String brand, String rbdsCode, String ctype) {
		try {

			authenticateAPI("prd_autotest_user", "7Bms4>9R~QSW");
			HeadersForAPI headers = new HeadersForAPI();
			Map<String, String> headersMap = headers.getHeaders3();
			Response response;
			String acctNo;
			String userid;
			int count = 1;
			boolean respCode = false;
			if(brand.contains("Luckystrike"))
				brand = "Lucky strike";
			brand= brand.toUpperCase();
			do {
				List<String> data = getDataFromDbProd(ctype);
				acctNo = data.get(0);
				userid = data.get(1);
				String url = "http://172.25.96.83:15000/digitaloffers/v1/" + acctNo
						+ "/"+brand+"?Channel=SPA&SessionID=123456789%RBDSCode="+rbdsCode;
				response = sendNReceive(url, SERVICEMETHOD.GET, headersMap, 201);
				String responseBody = response.getBody().asString();
				System.out.println("Response in runit : " + responseBody);
				if(ctype.contains("Birthday"))
					respCode = responseBody.contains("Offers") && responseBody.contains("BIRTHDAY");
				else if(ctype.contains("Welcome"))
					respCode = responseBody.contains("Offers") && responseBody.contains("WELCOME");
				count++;
				if(count>20)
					break;
			} while (!respCode);
			return userid;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public String getuserIdWithOutOffers(String brand, String rbdsCode) {
		try {

			authenticateAPI("Test_user", "Welcome@1212");
			HeadersForAPI headers = new HeadersForAPI();
			Map<String, String> headersMap = headers.getHeaders3();
			Response response;
			String acctNo;
			String userid;
			boolean respCode;
			brand= brand.toUpperCase();
			do {
				List<String> data = getDataFromDb();
				acctNo = data.get(0);
				userid = data.get(1);
				String url = "http://*************:15000/digitaloffers/v1/" + acctNo
						+ "/"+brand+"?Channel=SPA&SessionID=123456789%RBDSCode="+rbdsCode;
				response = sendNReceive(url, SERVICEMETHOD.GET, headersMap, 201);
				String responseBody = response.getBody().asString();
				System.out.println("Response in runit : " + responseBody);
				respCode = responseBody.contains("Offers");	
			} while (respCode);
			return userid +" "+acctNo;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}