package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Luckystrike", glue = { "com.rai.steps" }, tags = "@LuckystrikeCouponHome_Prod or @LuckystrikeCouponSort_Prod or @LuckystrikeCouponvalidateRestrictions_Prod or "
		+ "@LuckystrikeValidateRedeemNotNow_Prod or @LuckystrikeValidatefavouriteStore_Prod or @LuckystrikeValidateErrorMessageNotnearbyStore_Prod or @LuckystrikeMapViewCouponRedemtion_Prod or "
		//+ "@LuckystrikeCouponSortByValue_Prod or "
		+ "@NavigateLuckystrikeMobilesiteHamburgerMenu_Prod or @LuckystrikeLogoutfromHamburgerMenu_Prod or @LuckystrikeValidateHamburgerMenu_Prod or "
		//+ "@LuckystrikeAccessToBrowserAndDeviceLocation_Prod or "
		+ "@LuckystrikeSGWValidations_Prod or "
		+ "@LuckystrikeStoreListView_Prod or @LuckystrikeStoreListSearchByZip_Prod or @LuckystrikeStoreDetailsMapView_Prod or @LuckystrikeStoreMapViewbyZip_Prod",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerLuckystrikeProd extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
