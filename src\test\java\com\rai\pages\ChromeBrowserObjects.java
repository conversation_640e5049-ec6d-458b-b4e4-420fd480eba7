package com.rai.pages;

import org.openqa.selenium.By;

public class ChromeBrowserObjects {

	public static By chromeMenu = By.xpath("//*[@resource-id=\"com.android.chrome:id/menu_button_wrapper\"]");
	public static By menuButton = By.xpath("//*[@resource-id='com.android.chrome:id/menu_button_wrapper']");
	public static By settings = By.xpath("//*[@content-desc='Settings']"); // ChromeMenu Settings Button
	public static By history = By.xpath("//*[@content-desc='History']");
	public static By clearBrowsingData = By.xpath("//*[@resource-id='com.android.chrome:id/clear_browsing_data_button']");
	public static By privacy = By.xpath("//*[contains(@text,'Privacy')]");// ChromeSettings Privacy Button
	public static By clearBrowser = By.xpath("//*[@text='Clear browsing data']"); // ChromeSettings Clear browsing data
																					// Button
	public static By advancedTab = By.xpath("//*[@text='Advanced']"); // ChromeSettings - advanced Tab
	public static By lastHourDropDown = By.xpath("//*[@resource-id=\"com.android.chrome:id/spinner\"]"); // ChromeSettings
																											// - Last
																											// Hour
																											// settings
	public static By drpDownAllTime = By.xpath("//*[@text=\"All time\"]"); // ChromeSettings - Drop Down value - All
																			// Time
	public static By clearData = By.xpath("//*[@resource-id='com.android.chrome:id/clear_button']");// ChromeSettings
																									// Clear data Button
	public static By clearDataPopup = By.xpath("//*[@resource-id='android:id/button1']"); // ChromeSettings Clear data
																							// popup Button
	public static By siteSettings = By.xpath("//*[@text='Site settings']"); // ChromeMenu Site Settings Button
	public static By historyclose = By.xpath("//*[@resource-id='com.android.chrome:id/close_menu_id']");
	
	public static By popUpsAndRedirects = By.xpath("//*[@text='Pop-ups and redirects']"); // Pop Ups and Redirects
	public static By unblockpopUpsToggle = By.xpath("//android.widget.Switch[@resource-id='com.android.chrome:id/switchWidget']"); // Toggle Pop up and redirects
	public static By navigateUparrow = By.xpath("//*[@content-desc='Navigate up']"); // Arrow - Navigate back
	public static By androidLocationServicesPermissionAllow = By.id("com.android.chrome:id/positive_button");
    public static By iOSLocationServicesPermissionAllow = By.xpath("//XCUIElementTypeButton[@label='Allow']");
}
