package com.rai.framework;

import static org.opencv.imgcodecs.Imgcodecs.imwrite;
import static org.opencv.imgproc.Imgproc.COLOR_BGR2GRAY;
import static org.opencv.imgproc.Imgproc.MORPH_RECT;
import static org.opencv.imgproc.Imgproc.cvtColor;
import static org.opencv.imgproc.Imgproc.dilate;
import static org.opencv.imgproc.Imgproc.erode;
import static org.opencv.imgproc.Imgproc.getStructuringElement;

import java.awt.image.BufferedImage;
import java.awt.image.DataBufferByte;
import java.io.File;
import java.util.Properties;

import javax.imageio.ImageIO;

import org.opencv.core.Core;
import org.opencv.core.CvType;
import org.opencv.core.Mat;
import org.opencv.core.Size;
import org.opencv.imgproc.Imgproc;

public class ImageOperations {

	protected static Properties properties = Settings.getInstance();
	static String SRC_PATH = properties.getProperty("SGWImagesPath");
	static {
		System.load(System.getProperty("user.dir") + properties.getProperty("OpenCvDllFilePath"));
	}

	public static String resizeImage(Mat inputImage) {
		Mat resizeImage = new Mat(2560, 1440, inputImage.type());
		int interpolation = Imgproc.INTER_CUBIC;
		Imgproc.resize(inputImage, resizeImage, resizeImage.size(), 0, 0, interpolation);
		Mat destination = new Mat(inputImage.rows(), inputImage.cols(), inputImage.type());
		Imgproc.GaussianBlur(inputImage, destination, new Size(0, 0), 10);
		Core.addWeighted(inputImage, 1.5, destination, -0.5, 0, destination);
		boolean written = imwrite(SRC_PATH + "ResizedImage.png", resizeImage);
		System.out.println(written);
		return SRC_PATH + "ResizedImage.png";
	}

	public static String convertGrey(String filepath) {
		try {
			File input = new File(filepath);
			BufferedImage image = ImageIO.read(input);
			byte[] data = ((DataBufferByte) image.getRaster().getDataBuffer()).getData();
			Mat mat = new Mat(image.getHeight(), image.getWidth(), CvType.CV_8UC3);
			mat.put(0, 0, data);
			Mat mat1 = new Mat(image.getHeight(), image.getWidth(), CvType.CV_8UC1);
			Imgproc.cvtColor(mat, mat1, Imgproc.COLOR_RGB2GRAY);
			byte[] data1 = new byte[mat1.rows() * mat1.cols() * (int) (mat1.elemSize())];
			mat1.get(0, 0, data1);
			BufferedImage image1 = new BufferedImage(mat1.cols(), mat1.rows(), BufferedImage.TYPE_BYTE_GRAY);
			image1.getRaster().setDataElements(0, 0, mat1.cols(), mat1.rows(), data1);
			File ouptut = new File(SRC_PATH + "grayscale.png");
			ImageIO.write(image1, "png", ouptut);
			return ouptut.getAbsolutePath();

		} catch (Exception e) {
			System.out.println("Error: " + e.getMessage());
		}
		return null;
	}

	public static String dilateAndErode(Mat inputMat) {
		try {
			Mat grey = new Mat();
			cvtColor(inputMat, grey, COLOR_BGR2GRAY);
			imwrite(SRC_PATH + "grey.png", grey);
			Mat element = getStructuringElement(MORPH_RECT, new Size(2, 2), new org.opencv.core.Point(1, 1));
			dilate(grey, grey, element);
			erode(grey, grey, element);
			imwrite(SRC_PATH + "closeOpen.png", grey);
			element.release();
			return SRC_PATH + "closeOpen.png";
		} catch (Exception e) {
			e.printStackTrace();
		}
		return SRC_PATH + "closeOpen.png";
	}
}
