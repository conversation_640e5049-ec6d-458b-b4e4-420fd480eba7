Feature: Camel RBDS - Store Details
As a RJR user, The user will be able to view the camel store by list and Mapview

@CamelStoreListView
  Scenario Outline: Validate that user is able to select a coupons to choose a store and view the store details upto 20 stores for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Camel Offers Page
      And I click on redeem now button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
     Then I click on loadmore button and validate the list
  
  @CamelStoreListView_QA         	
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                          | Env | 
      #| Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204          | QA  | 
      | Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213 | QA  | 
      | Camel | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106    | QA  | 
      | Camel | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284        | QA  | 
  
  @CamelStoreListView_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env  | 
      | Camel | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204            | PROD | 
      | Camel | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213   | PROD | 
      | Camel | Sheetz               | 595324   | https://www.camel.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Camel | Speedway             | 529181   | https://www.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @CamelStoreListSearchByZip  	
  Scenario Outline: Validate the search By Zip and view the available stores in Store list page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
     Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Camel Offers Page
      And I click on redeem now button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I do a Search By zip with invalid zip code
     Then I validate the error message for invalid zipcode
      And I do a Search By zip with valid zip code <ZipCode>
      And I select a store from store list page
     Then I validate the error message on coupon redemption
  
  @CamelStoreListSearchByZip_QA 
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                          | Env | ZipCode | 
      | Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204          | QA  | 60404   | 
      #| Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213 | QA  | 60404   | 
      | Camel | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106    | QA  | 27101   | 
      | Camel | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284        | QA  | 60404   | 
  
  @CamelStoreListSearchByZip_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env  | ZipCode |
      | Camel | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204            | PROD | 60404   |
      | Camel | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213   | PROD | 60404   |
      | Camel | Sheetz               | 595324   | https://www.camel.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 27101   |
      | Camel | Speedway             | 529181   | https://www.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 60404   |
  
  @CamelStoreDetailsMapView      
  Scenario Outline: Validate the user is able to view the store details through the map view for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Camel Offers Page
      And I click on redeem now button
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I select a store from map view
     Then I validate the user is on Redeem now page
  
  @CamelStoreDetailsMapView_QA
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                          | Env | 
      | Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204          | QA  | 
      | Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213 | QA  | 
      | Camel | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106    | QA  | 
      | Camel | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284        | QA  | 
  
  @CamelStoreDetailsMapView_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env  | 
      | Camel | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204            | PROD | 
      | Camel | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213   | PROD | 
      | Camel | Sheetz               | 595324   | https://www.camel.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
      | Camel | Speedway             | 529181   | https://www.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 
  
  @CamelStoreMapViewbyZip
  Scenario Outline: Validate the Search By Zip and view the available stores in Store Map view page for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
     And I navigate to Camel Offers Page
      And I click on redeem now button
        And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I do a Search By zip with invalid zip code
     Then I validate the error message for invalid zipcode
      And I do a Search By zip with valid zip code <ZipCode>
      And I select a store from map view
      And I validate the error message on coupon redemption
  
  @CamelStoreMapViewbyZip_QA
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                         | Password  | Loc                                          | Env | ZipCode | 
      | Camel | 7-Eleven Corporation | 572896   | https://aem-stage.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204          | QA  | 60404   | 
      | Camel | Murphy               | 741592   | https://aem-stage.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213 | QA  | 60404   | 
      | Camel | Sheetz               | 595324   | https://aem-stage.camel.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106    | QA  | 27101   | 
      #| Camel | Speedway             | 529181   | https://aem-stage.camel.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284        | QA  | 60404   | 
  
  @CamelStoreMapViewbyZip_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env  | ZipCode | 
      | Camel | 7-Eleven Corporation | 572896   | https://www.camel.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2711 McKinney Ave, Dallas, TX 75204            | PROD | 60404   |
      | Camel | Murphy               | 741592   | https://www.camel.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1603 VANCE JACKSON RD, San Antonio, TX 78213   | PROD | 60404   |
      | Camel | Sheetz               | 595324   | https://www.camel.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 27101   |
      | Camel | Speedway             | 529181   | https://www.camel.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD | 60404   |
