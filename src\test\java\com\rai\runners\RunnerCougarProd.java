package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Cougar", glue = { "com.rai.steps" }, tags = "@CougarCouponHome_Prod or @CougarCouponSort_Prod or @CougarCouponvalidateRestrictions_Prod or "
		+ "@CougarValidateRedeemNotNow_Prod or @CougarValidatefavouriteStore_Prod or @CougarValidateErrorMessageNotnearbyStore_Prod or @CougarMapViewCouponRedemtion_Prod or "
		+ "@NavigateCougarMobilesiteHamburgerMenu_Prod or @CougarLogoutfromHamburgerMenu_Prod or @CougarValidateHamburgerMenu_Prod or "
		+ "@CougarSGWValidations_Prod or "
		+ "@CougarStoreListView_Prod or @CougarStoreListSearchByZip_Prod or @CougarStoreDetailsMapView_Prod or @CougarStoreMapViewbyZip_Prod",  monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerCougarProd extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
