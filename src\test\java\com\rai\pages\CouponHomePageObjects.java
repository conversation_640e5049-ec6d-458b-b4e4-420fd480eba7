package com.rai.pages;


import org.openqa.selenium.By;

public class CouponHomePageObjects {
	public static By personalizedCoupon 						= 		By.xpath("//*[@id='flxPersonalizedCoupon_flxOffer' or contains(@class,'_coupons-list-container_av0im_1')]");
	public static By personalizedCouponG 						= 		By.xpath("(//div[contains(@class,'_coupons-list-container_av0im_1')])[1]");
	
	public static By eitherOrCoupon     						= 		By.xpath("//div[@id='flxEitherOrPC_flxCollapse1Container']");
	public static By KodiakpersonalizedCoupon					= 		By.xpath("//*[@class='cmp-offers-mobile-only__greeting-text']");
	public static By brandImgCouponHomeHeader 					= 		By.xpath("//*[contains(@id,'PWA_imgBrandLogo') or contains(@class,'_navbar-brand-logoholder')]"); // Brand icon - Header - Coupon Home Page
																			   
	public static By couponCount 								= 		By.xpath("//div[@id='frmCouponsHome_CouponsHome_HeaderForPWA_lblOfferCount' or contains(@class,'_navbar-coupon-count')]"); // Coupon Home - Coupon Count icon	
	public static By welcomFirstNameTxt  						= 		By.xpath("//div[@id='frmCouponsHome_CouponsHome_flexWelcomeContent']//div[@id='frmCouponsHome_CouponsHome_rctxWelcomeFirstName']");  // Coupon Home - Welcome First name text
	public static By lblSortBy 									= 		By.xpath("//div[@class='kcell sknLblCHSortOffers']//div[@id='frmCouponsHome_CouponsHome_lblSortOffers']"); // Coupon Home Page - Sort By label
	public static By lblSortByG									= 		By.xpath("//p[contains(@class,'_couponsHome-sortby-text')]"); // Coupon Home Page - Sort By label
	
	public static By welcomFirstNameTxtG  						= 		By.xpath("//span[contains(@class,'_couponsHome-welcome-text-name')]");  // Coupon Home - Welcome First name text
	public static By btnSortOffers 								= 		By.xpath("//div[@class='kcell slImage']//span[@id='frmCouponsHome_CouponsHome_imgSortOffers_span']"); // Coupon Home Page - Sort Offers Button
	public static By btnSortOffersG 							= 		By.xpath("//img[contains(@class,'_couponsHome-sortBy-image')]"); // Coupon Home Page - Sort Offers Button
	public static By personalizedCouponTileExpiry 				= 		By.xpath("(//div[@kcontainerid='segmentoffers']//div[@id='flxPersonalizedCoupon_lblexpires'])[1]"); // Coupon Home - Personalized Coupon - Expiry
	public static By personalizedCouponTileExpiryG 				= 		By.xpath("(//*[contains(@class,'_coupons-list-header-expire-text_dmz')])[1]"); // Coupon Home - Personalized Coupon - Expiry
	public static By personalizedCouponOfferValue  				=		By.xpath("(//div[@id='flxPersonalizedCoupon_rctxOfferValue'])[1]"); // Coupon Home  - Personalized Coupon Offer Value
	public static By personalizedCouponOfferValueG  			=		By.xpath("(//p[contains(@class,'_coupons-list-price_dmz')])[1]"); // Coupon Home  - Personalized Coupon Offer Value
	
	public static By personalizedCouponOfferDescG 				= 		By.xpath("//p[contains(@class,'_coupons-list-description-text')]"); // Coupon Home  - Personalized Coupon Offer Descriptio
	public static By personalizedCouponOfferDesc 				= 		By.xpath("//div[@id='flxPersonalizedCoupon_rtLblOfferDesc']"); // Coupon Home  - Personalized Coupon Offer Description
	public static By warningMsgInLoginPage       				= 		By.xpath("//div[@class='cmp-surgeon-general-warning__warning' or contains(@id,'flxSGWContainer')]"); // Coupon Home  - Warning message in login page
	public static By personalizedCoupontype     				= 		By.id("flxPersonalizedCoupon_lblCouponType"); // Coupon Home  - Personalized Coupon Type
	public static By personalizedCouponLblGood 					= 		By.xpath("(//div[@id='flxPersonalizedCoupon_lblgood'])[1]"); // Coupon Home - Personalized Coupon Offer label - Good On Any Style
	public static By personalizedCouponLblGoodG 				= 		By.xpath("(//p[contains(@class,'_coupons-list-description-style')])[1]"); // Coupon Home - Personalized Coupon Offer label - Good On Any Style

//	public static By personalizedCouponLblGood 					= 		By.xpath("//div[@id='flxEitherOrPC_lblCollapse2Good']"); // Coupon Home - Personalized Coupon Offer label - Good On Any Style
//	public static By personalizedCouponImage 					= 		By.xpath("(//img[@id='flxPersonalizedCoupon_imgCigPack1'])[2]"); // Coupon Home - Personalized Coupon Offer - Image
	public static By personalizedCouponImage 					= 		By.xpath("(//img[@id='flxPersonalizedCoupon_imgCigPack1'])[1]"); // Coupon Home - Personalized Coupon Offer - Image
	public static By personalizedCouponImageG					= 		By.xpath("(//img[contains(@id,'couponsListChoiceImage')])[1]"); // Coupon Home - Personalized Coupon Offer - Image
	
	public static By personalizedCouponValidityTxt 				= 		By.xpath("//div[@id='flxPersonalizedCoupon_lblnextcoupon']"); // Coupon Home - Personalized Coupon Offer - Validity Text - This Coupon offer is valid for
	public static By personalizedCouponValidityRemaining 		= 		By.xpath("//div[@id='flxPersonalizedCoupon_lbltimeremaining']"); //Coupon Home - Personalized Coupon Offer - Validity Remaining
	public static By personalizedCouponValidityProgressBar 		= 		By.xpath("//div[@id='flxPersonalizedCoupon_flxscrollbar']"); //Coupon Home - Personalized Coupon Offer - Validity Progress Bar
	public static By btn_ChooseStore 							= 		By.xpath("(//*[@id='flxPersonalizedCoupon_btnchooseastore' or @id='flxEitherOrPC_btnExpand1ChooseStore' or contains(@class,'_coupons-list-offerDetails-store-button_dmz')] )[1]"); //Choose A Store button 
	public static By btn_ChooseCoupon 							= 		By.xpath("//*[@id='flxPersonalizedCoupon_flxOfferAndDesc']"); //Choose A Coupon button
	//*[contains(@class,'_coupons-list-offerDetails-store-button_dmz')])[1]
	public static By btn_NpChooseStore							=		By.xpath("//*[@id='flxOfferTileVelo_btnchooseastore']");	
	public static By couponHomeSGWHeader 						= 		By.xpath("//div[@id='frmCouponsHome_CouponsHome_lblsgwheader']"); // Coupon Home - SGW Header - Moist Snuff
	public static By couponHomeSGWTextContainer 				= 		By.id("frmCouponsHome_CouponsHome_flxSGWContainer"); // Coupon Home  - SGW Text Container
	public static By couponHomeSGWText  						= 		By.xpath("//div[@id='frmCouponsHome_CouponsHome_lblSGW']/span"); // Coupon Home - SGW Text
	public static By couponHomeLocationDisclaimer 				= 		By.xpath("//*[@id='frmCouponsHome_CouponsHome_lbllocationdisclaimer' or contains(@class,'_coupon-offers-redemtion-text')]"); // Coupon Home  - Location Disclaimer Text
	public static By lblCouponTerms 							= 		By.xpath("//*[@id='frmCouponsHome_CouponsHome_lblcouponterms' or contains(@class,'_coupon-terms-text-heading')]"); // Coupon Home - Coupon Terms Label
	public static By msgCouponTerms 							= 		By.xpath("//*[@id='frmCouponsHome_CouponsHome_lblcoupontermsmsg1' or contains(@class,'_coupon-void-text')]"); // Coupon Home  - Coupon Terms Message
	public static By descCouponTerms 							= 		By.xpath("//div[@id='frmCouponsHome_CouponsHome_rtCouponTerms' or contains(@class,'_coupon-terms-text')]"); // Coupon Home - Coupon Terms description
	public static By lnkCouonTermsReadMore 						= 		By.xpath("//*[text()='Read More' or text()='Read more']"); // Coupon Terms - Read More link
	public static By descFullCouponTerms 						= 		By.xpath("//div[@id='frmCouponsHome_CouponsHome_rtCouponTerms1']//div"); // Coupon Terms - Full Coupon Terms description
	public static By lnkCouponTermsReadLess 					= 		By.xpath("//*[text()='Read Less' or text()='Read less']"); // Coupon Terms  - Read Less Link
	public static By couponValuesTxt 							= 		By.xpath("//div[@id='flxPersonalizedCoupon_rctxOfferValue']");
	public static By btnSort 									= 		By.xpath("(//img[@id='frmCouponsHome_CouponsHome_imgSortOffers'])[1]");//Coupons page - Sort
	//public static By restrictedStateError 					= 		By.id("frmLoadingScreen_LoadingScreen_ErrorMsg_rtdesc"); // Coupon Page - Restricted State Error Message
	public static By restrictedStateError 						= 		By.xpath("//div[@id='frmLoadingScreen_LoadingScreen_ErrorMsg_rtdesc']");
	//public static By btnrestrictedStateErrorClose 			= 		By.id("frmLoadingScreen_LoadingScreen_ErrorMsg_btnok"); // Coupon Page - Restricted State error pop up close button
	public static By btnrestrictedStateErrorClose 				= 		By.xpath("//*[@id='frmLocInfoImportant_LocInfoImportant_ErrorMsg_btnok']");
	public static By btnrestrictedStateErrorClose_Grizzly       =       By.xpath("//input[@id='frmSGWLoading_SGWLoading_ErrorMsg_btnok']");
	public static By btnrestrictedStateErrorClose_Pallmall       =       By.xpath("//*[@id='frmLocInfoImportant_LocInfoImportant_ErrorMsg_btnok']");
	 public static By grizzly_btnrestrictedStateErrorClose		= 		By.xpath("//*[@class=\\\"kcell sknBtnPopUpGray\\\"]");
	public static By btnrestrictedStateErrorClose_Velo       =       By.xpath("//input[@id='frmLocInfoImportant_LocInfoImportant_ErrorMsg_btnok']");
	public static By restrictedStateErrorVelo 						= 		By.xpath("//*[@id='frmLocInfoImportant_LocInfoImportant_ErrorMsg_rtdesc']");

	public static By hamburgerMenu 								= 		By.xpath("//div[@id='frmCouponsHome_CouponsHome_HeaderForPWA_flxHamMenu' or contains(@class,'_navbar-hammenu-center')]"); // Coupon Home - Hamburger menu
	public static By lnkHMenu_products 							=		By.xpath("//*[@class=\"cmp-navigation__item-link\" and text()=\"Products\"]");
	public static By lnkHMenu_findstore							= 		By.xpath("//*[@class=\"cmp-navigation__item-link\" and text()=\"Find A Store\"]");
	public static By lnkHMenu_showyourWork						= 		By.xpath("//*[@class=\"cmp-navigation__item-link\" and text()=\"Show Your Work\"]");
	public static By lnkHMenu_profile							= 		By.xpath("//*[@class=\"cmp-navigation__group\"]//*[@class=\"cmp-header__profile-link\"]");
	public static By personalCoupon 							= 		By.xpath("//*[@class=\"cmp-offers__greeting-offers cmp-offers__greeting-offers--visible\"]");
	
	//KodiakHamburgerMenuelements
	public static By lnk_Kodiakproducts 						=    	By.xpath("//*[@class=\"cmp-navigation__item-link\" and text()=\"Our Products\"]");
	public static By lnk_Kodiakvalues							= 		By.xpath("//*[@class=\"cmp-navigation__item-link\" and text()=\"Our Values\"]");
	public static By lnk_Kodiakourcause							= 		By.xpath("//*[@class=\"cmp-navigation__item-link\" and text()=\"Our Cause\"]");
	public static By lnk_KodiakOffers							= 		By.xpath("//*[@class=\"cmp-navigation__item-link\" and text()=\"Offers\"]");
	public static By lnk_FindKodiak 							= 		By.xpath("//*[@class=\"cmp-navigation__item-link\" and text()=\"Find Kodiak\"]");
	public static By lnk_Kprofile								=		By.xpath("//*[@class=\"icon-profile\"]");
	public static By txt_Kprofilename							=		By.xpath("//*[text()=\"HI, CHALMERS THROWERRJZZNB\"]");
	public static By txt_KContatctinfo							=		By.xpath("//*[text()=\"Contact Info\"]");
	public static By txt_KEmail									=		By.xpath("//*[@id=\"tab-email-label\"]//*[text()=\"Email\"]");
	public static By txt_KSecurity								=		By.xpath("//*[text()=\"Security\"]");
	public static By txt_KTobaccopreferences					=		By.xpath("//*[text()=\"Tobacco Preferences\"]");
	public static By txt_KHmenuoffers							=		By.xpath("//h4[text()=\"Offers\"]");
	public static By txt_KHmenuLogout							=		By.xpath("//h4[text()=\"Logout\"]");
	
	//Profile page Elements
	public static By txtprofilename 	   						= 		By.xpath("//*[text()=\"HI, BRAND QUARTLY\"]");
	public static By lnkprofilelogout							= 		By.xpath("//a[text()=\"Logout\"] | //a[text()=\"logout\"]");
	public static By txtprofilecontactinfo						= 		By.xpath("//*[text()=\"Contact info\"]");
	public static By txtprofileupdate 							=		By.xpath("//*[text()=\"Update your name, mailing address and mobile number.\"]");
	public static By txtprofileEmail							= 		By.xpath("//*[text()=\"email\"]");
	public static By txtprofilesecurity							= 		By.xpath("//*[text()=\"security\"]");
	public static By profileImage								=       By.xpath("//*[text()=\"profile image\"]");
	public static By profileinfodesc							=       By.xpath("//*[text()=\"Choose a new profile from our product gallery.\"]");
	public static By profleEmaildesc							=       By.xpath("//*[text()=\"Update your email address and preferences.\"]");
	public static By txtSmsOptin								=       By.xpath("//*[text()=\"SMS opt in\"]");
	public static By SmsoptinDesc								=       By.xpath("//*[text()=\"Sign up for SMS\"]");
	public static By Securitydesc								=       By.xpath("//*[text()=\"Update your password and challenge question.\"]");
	
	
	public static By lnkmobilesite 								= 		By.xpath("//*[@id=\"frmCouponsHome_CouponsHome_HamMenu_flxBrand\"]//*[@class=\"kcell sknLblHamMenus\"]");
	public static By txtHomepage								= 		By.xpath("//*[@class=\"cmp-hero-banner__headline1\"]");
	
	public static By btnLoadmore 								= 		By.xpath("//*[@class=\"kcell sknBtnSLLoadMore\"]");
	public static By btnmapview									= 		By.xpath("//*[@id=\"frmStoreList_StoreList_imgmap\"]");
	
	//Content Page
	public static By btngreentick								= 		By.xpath("//*[@id=\"frmContentScreen_ContentScreen_imgTick\"]");
	public static By offermessage 								=		By.xpath("//*[@class=\"kcell sknLblCSOfeerAvali\"]");
	public static By lnkbacktocoupon							= 		By.xpath("//*[@class=\"kcell sknLblCSBckToCoupons\"]");
	
	public static By Nocouponleftmsg							= 		By.xpath("//*[@class=\"kcell sknLblCSOfeerAvali\"]");
	
	//Newport Coupon Homepage Elements
	
	public static By np_personalizedCoupon 						= 		By.xpath("//*[@class='kcell middlecenteralignsknFcTempOfferContentVelo']");
	public static By np_personalizedCouponOfferValue 			= 		By.id("flxOfferTileVelo_rctxOfferValue");	
	public static By np_personalizedCouponTileExpiry 			= 		By.xpath("//div[@id='flxOfferTileVelo_lblexpires']");
	public static By np_personalizedCouponOfferDesc				= 		By.xpath("//div[@id='flxOfferTileVelo_rtLblOfferDesc']");
	public static By np_personalizedCouponValidityRemaining 	= 		By.xpath("//div[@id='flxOfferTileVelo_lbltimeremaining']");
	public static By np_personalizedCouponValidityTxt 			= 		By.xpath("//div[@id='flxOfferTileVelo_lblnextcoupon']");
	public static By np_personalizedCouponValidityProgressBar 	= 		By.xpath("//div[@id='flxOfferTileVelo_flxscrollbar']");
  
	//Grizzly Profile page Elements
	public static By Grizzlyprofilecontactinfo	   				= 	   	By.xpath("//*[text()=\"Contact Info\"]");
	public static By grizzly_profileImage          				=       By.xpath("//*[text()=\"Profile Image\"]");
	public static By grizzly_profiledesc           				=      	By.xpath("//*[text()=\"Choose a new profile pic from our limited edition cans gallery\"]");
	public static By grizzly_contactinfodesc       				=       By.xpath("//*[text()=\"Update your name, mailing address and mobile number\"]");
	public static By grizzly_emailLink             				=      	By.xpath("//*[@id=\"tab-email-label\"]//*[text()=\"Email\"]");
	public static By grizzly_emaildesc            				=       By.xpath("//*[text()=\"Update your email address and preferences\"]");
	public static By grizzly_smsoptindesc          				=       By.xpath("//*[text()=\"SMS opt in description\"]");
	public static By grizzly_securitylnk           				=       By.xpath("//*[text()=\"Security\"]");
	public static By grizzly_securitydesc          				=       By.xpath("//*[text()=\"Update your password, pin and challenge question\"]");
	public static By grizzly_tobaccoPreferences    				=       By.xpath("//*[text()=\"Tobacco Preferences\"]");
	public static By grizzly_tobaccodesc          	 			=       By.xpath("//*[text()=\"Which styles do you use?\"]");
	public static By grizzly_couponslink           				=       By.xpath("//h4[text()=\"Coupons\"]");
	public static By grizzly_couponsdesc           				=       By.xpath("//*[text()=\"See your latest coupons and savings to date\"]");
	public static By grizzly_logut_lnk             				=       By.xpath("//h4[text()=\"Logout\"]");
	
	public static By grizzly_contentExploreproducts				=       By.xpath("//*[@class=\"kcell sknCSTile1Btn\"]");
	public static By grizzly_contentStartQuizz     				=       By.xpath("//*[@class=\"kcell sknCSTileBtn\"]");
	public static By grizzly_giveawayheader        				=       By.xpath("//*[@class=\"cmp-hero-banner__headline1\"]");
	public static By grizzly_btnStartQuizz         				=       By.xpath("//*[@class=\"cmp-hero-quiz__cta cmp-button\"]");
	 
	public static By grizzly_NoCouponsMsg          				=      	By.xpath("//*[@class=\"kcell sknLblCSOfeerAvali\"]");
	public static By GrizzMoistTab 				             	= 		By.xpath("//div[contains(@class,'_brand-tab-content-left')]"); //Grizzly coupons Tab Moist Snuff
	public static By GrizzSnussTab 				             	= 		By.xpath("//div[contains(@class,'_brand-tab-content-middle')]"); //Grizzly coupons Tab Snus
	public static By GrizzNicotineTab 				            = 		By.xpath("//div[contains(@class,'_brand-tab-content-right')]"); //Grizzly coupons Tab Nicotine pouch
	public static By GrizzNicotineTabSGW 				        = 		By.xpath("//p[contains(@class,'_fixed-warning-content')]"); //Grizzly coupons Tab Nicotine pouch SGW message


	public static By pallmall_emaildesc            				=       By.xpath("//*[text()=\"Update your email address and preferences\"]");
	public static By pallmall_securitydesc         				=       By.xpath("//*[text()=\"Update your password and challenge question\"]");
    public static By pallmall_tobaccopreferences   				=       By.xpath("//*[text()=\"Tobacco preferences\"]");
    
    public static By luckystrike_signup            				=       By.xpath("//*[text()=\"Sign up for Texts\"]");
    public static By luckystrike_signupdesc        				=       By.xpath("//*[text()=\"Join the Inner Circle\"]");
    public static By luckystrike_acctlnkg          				=       By.xpath("//*[text()=\"Account Linking\"]");
    public static By luckystrike_acctlnkgdec       				=	   	By.xpath("//*[text()=\"Have other accounts? Connect them here.\"]");   
    public static By luckystrike_Couponslink       				=       By.xpath("//*[text()=\"coupons\"]");
    public static By luckystrike_Couponslinkdesc   				=       By.xpath("//*[text()=\"See your latest coupons and savings to date.\"]");    
    public static By luckystrike_profiledesc       				=       By.xpath("//*[text()=\"Choose a new profile pic from our pack gallery. \"]");
    public static By luckystrike_contactdesc       				=       By.xpath("//*[text()=\"Update your name, mailing address and mobile phone number.\"]");    
    public static By luckystrike_tobaccopreferences				=       By.xpath("//*[text()=\"Tobacco preferences\"]");
    
    //AmericanSpa profile Elements
    
    public static By nas_contactinfo							=		By.xpath("//*[text()=\"CONTACT INFO\"]");
    public static By nas_contactinfoDesc						=		By.xpath("//*[text()=\"Update your name, mailing address and mobile number.\"]");
	public static By nas_email									=		By.xpath("//*[text()=\"EMAIL\"]");
	public static By nas_emaildesc								=		By.xpath("//*[text()=\"Update your email address and preferences\"]");
	public static By nas_signuptexts 							=		By.xpath("//*[text()=\"Sign up for Texts\"]");
	public static By nas_spiritcircle							=		By.xpath("//*[text()=\"Join The Spirit Circle\"]");
	public static By nas_security 								=		By.xpath("//*[text()=\"SECURITY\"]");
	public static By nas_securitydesc							=		By.xpath("//*[text()=\"Update your password and challenge question\"]");
	public static By nas_tobaccopreferences 					=		By.xpath("//*[text()=\"TOBACCO PREFERENCES\"]");
	public static By nas_tobaccodesc							=		By.xpath("//*[text()=\"Which styles do you use?\"]");
	public static By nas_GiftCertificates						=		By.xpath("//*[text()=\"GIFT CERTIFICATES\"]");
	public static By nas_giftcertificatesdesc					=		By.xpath("//*[text()=\"See your latest offers\"]");
	public static By nas_Logoutlink                         	=   	By.xpath("//*[text()=\"LOG OUT\"]");
	
	
	public static By welcome_coupon = By.xpath("//div[text()='WELCOME ']");
	public static By birthday_coupon = By.xpath("//div[text()='HAPPY BIRTHDAY! ']");
	public static By couponType = By.id("flxPersonalizedCoupon_lblCouponType");
	public static By couponInfo = By.id("flxPersonalizedCoupon_lblCouponInfo");
	
	public static By CamelCrossBrandCSnus						= 		By.xpath("//div[contains(text(),'CAMEL SNUS')]");
	public static By CamelCrossBrandCSnus1						= 		By.xpath("//div[contains(text(),'CAMEL SNUS')]//preceding::label[contains(text(),'$')][1]");
}   
