Feature: Velo RBDS- Coupon Redemption at different retailers using the RBDS url
As an ATC, I should be able to successfully use the RBDS url for a particular retailer and Redeem a coupon

@VeloRedeemAtAny0.1MileStore
  Scenario Outline: Validate the user is able to redeem the coupon from RBDS site when he is at a distance of .1 miles for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
      And I select a coupon and click on choose a store button
      And I select a store from store list page
      And I click on the Redeem Now button
     Then I validate the timer running
      And I click on the I'm Done button
  
  @VeloRedeemAtAny0.1MileStore_QA  
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                  | Password  | Loc                                            | Env | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL>  | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>  | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 

  @VeloValidateRedeemNotNow
  Scenario Outline: Validate that no coupon is redeemed when the user clicks on the Not Now button on the redemption page for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
      And I select a coupon and click on choose a store button
      And I select a store from store list page
      And I click on the Not Now button
     Then I validate I'm on the Coupons Home Page
  
  @VeloValidateRedeemNotNow_QA 		    
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                  | Password  | Loc                                            | Env | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL>  | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>  | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
  
  @VeloValidateRedeemNotNow_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env  | 
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Velo  | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
  
  @VeloValidatefavouriteStore
  Scenario Outline: Validate the favorite store selection in Redeem Now page and its update in Store list page  for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>     
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I select a coupon and click on choose a store button
      And I select a store from store list page
      And I mark the store as favorite
     Then I navigate to Store List and validate the favoritestore
  
  @VeloValidatefavouriteStore_QA      	
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                  | Password  | Loc                                            | Env | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL>   | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>  | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
  
  @VeloValidatefavouriteStore_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env  | 
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD | 
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD | 
      | Velo  | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD | 
  
  @VeloValidateErrorMessageNotnearbyStore
  Scenario Outline: Validate the error message when user tries to redeem a coupon when is not nearby the store for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>       
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I select a store from store list page
      #And I click on the Redeem Now button
     Then I validate the error message on coupon redemption
  
  @VeloValidateErrorMessageNotnearbyStore_QA     
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                  | Password  | Loc                                            | Env | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL>  | Password1 | 365 E Dalton Rd, King, NC 27021                | QA  | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1940 Darwick Rd, Winston-Salem, NC 27127				| QA  | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 | <EMAIL>  | Password1 | 2003 Reynolda Rd, Winston-Salem, NC 27106      | QA  | 
  
  @VeloValidateErrorMessageNotnearbyStore_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env  | 
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 365 E Dalton Rd, King, NC 27021                | PROD | 
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1940 Darwick Rd, Winston-Salem, NC 27127			 | PROD | 
      | Velo  | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2003 Reynolda Rd, Winston-Salem, NC 27106      | PROD | 
 
	@VeloMapViewCouponRedemtion     
   Scenario Outline: Validate the user is displayed with error message when he tries to access the coupon from the restricted states location for the retailer <Retailer> with RBDS code <RBDScode> for the Brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc> with restricted state
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I click on the Understood button
      And I select a coupon and click on choose a store button
      And I navigate to store list map view page
      And I select a store from map view
      #And I click on the Redeem Now button
     Then I validate the error message on coupon redemption
      
 @VeloMapViewCouponRedemtion_QA     
 Examples: 
      | Brand | Retailer             | RBDScode | URL                                          | Username                  | Password  | Loc                                            | Env | 
      | VELO  | 7-Eleven Corporation | 572896   | https://aem-stage.velo.com/?RBDSCode=572896 | <EMAIL>  | Password1 | 365 E Dalton Rd, King, NC 27021                | QA  | 
      | VELO  | Murphy               | 741592   | https://aem-stage.velo.com/?RBDSCode=741592 |  <EMAIL> | Password1 | 1940 Darwick Rd, Winston-Salem, NC 27127				| QA  | 
      | VELO  | Sheetz               | 595324   | https://aem-stage.velo.com/?RBDSCode=595324 |  <EMAIL>  | Password1 | 2003 Reynolda Rd, Winston-Salem, NC 27106      | QA  | 
  
 @VeloMapViewCouponRedemtion_Prod
    Examples: 
      | Brand | Retailer             | RBDScode | URL                                    | Username               | Password  | Loc                                            | Env  | 
      | Velo  | 7-Eleven Corporation | 572896   | https://www.velo.com/?RBDSCode=572896 | <EMAIL> | Password1 | 365 E Dalton Rd, King, NC 27021                | PROD | 
      | Velo  | Murphy               | 741592   | https://www.velo.com/?RBDSCode=741592 | <EMAIL> | Password1 | 1940 Darwick Rd, Winston-Salem, NC 27127			 | PROD | 
      | Velo  | Sheetz               | 595324   | https://www.velo.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2003 Reynolda Rd, Winston-Salem, NC 27106      | PROD | 
  
  