package com.rai.pages;


import org.openqa.selenium.By;

public class StoreListViewPageObjects {

	public static By txt_Zipcode = By.xpath("//input[contains(@id,'_txtzipcode')]"); //Zipcode textbox
	//public static By txt_Zipcode = By.xpath("//input[@id='frmStoreList_StoreList_txtzipcode'] | //input[@id='frmSelectStore_SelectStore_txtzipcode']");
	public static By lnk_Store =  By.xpath("//span[contains(@id ,'imgleftarrow')]"); //Store List link
	public static By btn_RedeemNow = By.xpath("//*[@id='frmSelectStore_SelectStore_btnredeemnow']"); //Redeem Now button

	
	
	public static By errormsg_Popup = By.xpath("//*[@id='frmSelectStore_SelectStore_ErrorMsg_rtdesc']");//Error popup
	//public static By errormsg_PopupStoreList = By.xpath("//*[contains(@id,'frmStoreList_StoreList_ErrorMsg')]");//Error popup
	public static By errormsg_PopupStoreList = By.xpath("//*[@id='frmStoreList_StoreList_ErrorMsg_rtdesc']");//Error popup

	public static By btn_Close = By.xpath("//*[contains(@id,'_ErrorMsg_btnok')]"); //Button - Close
	
	public static By btn_RedeemNowAEM = By.xpath("//*[@id='frmSelectStore_SelectStore_btnredeemnow']");//Button - Redeem Now
	public static By btn_NotNowAEM = By.xpath("//*[@id='frmSelectStore_SelectStore_btnnotnow']");//Button - Not Now
	public static By btn_ImDone = By.xpath("//*[@id='frmActiveCoupon_ActiveCoupon_btndone']");//I'm Done button
	//public static By btn_Close = By.xpath("//*[@id='frmActiveCoupon_ActiveCoupon_btndone']");//I'm Done button
	public static By txt_Timer = By.xpath("//*[@id='frmActiveCoupon_ActiveCoupon_lblminute']");//Timer minute
	public static By fullTimer = By.id("frmActiveCoupon_ActiveCoupon_flxtimer");
	public static By storeName =By.id("frmActiveCoupon_ActiveCoupon_lblstorename");
	public static By storeAddress = By.id("frmActiveCoupon_ActiveCoupon_lblstoreaddress");
	public static By couponExpiry = By.id("frmActiveCoupon_ActiveCoupon_lblexpires");
	public static By couponValue = By.id("frmActiveCoupon_ActiveCoupon_lblinfo1");
	public static By couponDesc = By.id("frmActiveCoupon_ActiveCoupon_lblinfo2");
	public static By couponBarcode =  By.id("frmActiveCoupon_ActiveCoupon_flxBarcode");
	public static By couponUPCCode= By.id("frmActiveCoupon_ActiveCoupon_lblcodenumber");
	public static By btn_ImDone_newport = By.xpath("//*[@id='frmActiveCoupon_ActiveCoupon_MobileSurveyIntegration_lblIMDone']");	
	public static By lnk_StoreDetailsMapView = By.xpath("//*[@id='frmSelectStore_SelectStore_btnmapvieiwcon']");//Store details Map View
	public static By lnk_MapIt = By.xpath("//div[@class='mapit']/a");//Map It link
	public static By btnShowDirections = By.xpath("//*[@id='frmMapPath_MapPath_btnShowDirectionDetails']");//Show Directions button
	public static By lnk_StoreListMapView = By.xpath("//*[@id='frmStoreList_StoreList_btnmapvieiwcon']");//Store List Map View "),
	//public static By lnk_MapStore = By.xpath("//img[@src='resources/common/map_pin.png'] |//div[@role='button'] ");//Map Icon store link
	public static By lnk_MapStore = By.xpath("//div[@role='button']");//Map Icon store link
    public static By lnk_MapStoreLink = By.xpath("//img[@src='resources/common/next.png'] | //img[@src='resources/common/next.svg']");
	//	Map_Icon_StoreLink("//img[@src='resources/common/next.png'] | //img[@src='resources/common/next.svg']",XPATH,"Offers - Map Icon store link"),
    public static By chckbx_favstore= By.xpath("//img[@id='frmSelectStore_SelectStore_imgcheckuncheck']");
    public static By lnk_storeDownarrow= By.xpath("//*[@id='frmSelectStore_SelectStore_imgdownarrow']");
    public static By GoldStar= By.xpath("//*[@id='flxsegstoreitemfav_flxmakefav']//*[@class='kcell middlecenteralignslImage']");
    public static By GreyStar=By.xpath("(//img[@id='flxsegstoreitem_imgfav'])[1]");

}