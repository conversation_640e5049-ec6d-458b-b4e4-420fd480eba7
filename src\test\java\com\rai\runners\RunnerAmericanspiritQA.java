package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Americanspirit", glue = { "com.rai.steps" }, tags = "@NASCIGSValidateContentPostRedemption_QA or "
		+ "@NASCIGSCouponHome_QAstage or @NASCIGSCouponSort_QAStage or  @NASCIGSCouponvalidateRestrictions_QAStage or "
		+ "@NASCIGSRedeemAtAny0.1MileStore_QA or @NASCIGSValidateRedeemNotNow_QA or @NASCIGSValidatefavouriteStore_QA or @NASCIGSValidateErrorMessageNotnearbyStore_QA or @NASCIGSMapViewCouponRedemtion_QA or "
		+ "@NASCIGSValidateHamburgerMenu_QA or @NASCIGSLogoutfromHamburgerMenu_QA or @NavigateNASCIGSMobilesiteHamburgerMenu_QA or "
		+ "@NASCIGSSGWValidations_QA or "
		+ "@NASCIGSStoreListView_QA or @NASCIGSStoreListSearchByZip_QA or @NASCIGSStoreDetailsMapView_QA or @NASCIGSStoreMapViewbyZip_QA", monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerAmericanspiritQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
