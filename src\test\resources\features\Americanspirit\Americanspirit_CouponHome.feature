Feature: NASCIGS RBDS - Coupon Home
As an ATC user, I should be able to successfully validate the coupon home page for the RBDS Flow for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment

@NASCIGSValidateCouponHome
  Scenario Outline: Validate the RBDS Coupon Home Page and availability of the coupons for the retailer <Retailer> with RBDS code <RBDScode> for the brand <Brand> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
          And I navigate to AmericanSpirit Offers Page
      And I click on get gift certificate button
      And I click on the Understood button
     Then I validate I'm on the Coupons Home Page
      And I validate the Coupons Home Page
  
  @NASCIGSCouponHome_QAstage        	
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                   | Username                         | Password  | Loc                                            | Env | 
      | NASCIGS | 7-Eleven Corporation | 572896   | https://aem-stage.americanspirit.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | NASCIGS | Murphy               | 741592   | https://aem-stage.americanspirit.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | NASCIGS | Sheetz               | 595324   | https://aem-stage.americanspirit.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | NASCIGS | Speedway             | 529181   | https://aem-stage.americanspirit.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @NASCIGSCouponHome_Prod
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                             | Username               | Password  | Loc                                            | Env   | 
      | NASCIGS | 7-Eleven Corporation | 572896   | https://www.americanspirit.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD  | 
      | NASCIGS | Murphy               | 741592   | https://www.americanspirit.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD  | 
      #| NASCIGS | Sheetz               | 595324   | https://www.americanspirit.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD  | 
      #| NASCIGS | Speedway             | 529181   | https://www.americanspirit.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD  | 
  
  @NASCIGSCouponSort
  Scenario Outline: Validate that the user is able to sort the coupons by value and expiration date for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
        And I navigate to AmericanSpirit Offers Page
      And I click on get gift certificate button
      And I click on the Understood button
      And I validate I'm on the Coupons Home Page
     Then I validate the default view of the coupons sorted by value
      And I sort the coupons by expiration date and validate the coupons
  
  @NASCIGSCouponSort_QAStage
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                   | Username                         | Password  | Loc                                            | Env | 
      #| NASCIGS | 7-Eleven Corporation | 572896   | https://aem-stage.americanspirit.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | QA  | 
      | NASCIGS | Murphy               | 741592   | https://aem-stage.americanspirit.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | QA  | 
      | NASCIGS | Sheetz               | 595324   | https://aem-stage.americanspirit.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | QA  | 
      | NASCIGS | Speedway             | 529181   | https://aem-stage.americanspirit.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 401 N Main St, Kernersville, NC 27284          | QA  | 
  
  @NASCIGSCouponSort_Prod
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                             | Username               | Password  | Loc                                            | Env   | 
      | NASCIGS | 7-Eleven Corporation | 572896   | https://www.americanspirit.com/?RBDSCode=572896 | <EMAIL> | Password1 | 713 S Main St, King, NC 27021                  | PROD  | 
      | NASCIGS | Murphy               | 741592   | https://www.americanspirit.com/?RBDSCode=741592 | <EMAIL> | Password1 | 3470 Pkwy Village Cir, Winston-Salem, NC 27127 | PROD  | 
      | NASCIGS | Sheetz               | 595324   | https://www.americanspirit.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2985 Fairlawn Dr, Winston-Salem, NC 27106      | PROD  | 
      | NASCIGS | Speedway             | 529181   | https://www.americanspirit.com/?RBDSCode=529181 | <EMAIL> | Password1 | 401 N Main St, Kernersville, NC 27284          | PROD  | 
  
  @NASCIGSCouponvalidateRestrictions      
  Scenario Outline: Validate the user is displayed with error message when he tries to access the americanspirit coupon from the restricted states location for the Brand <Brand> and the Retailer <Retailer> on <Env> Environment
    Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
      And I set the valid device location to <Loc>
     When The user login with valid UserId <Username> and Password <Password> for the brand <Brand> env <Env> and for the retailer <Retailer> with RBDS code <RBDScode>
      And I navigate to AmericanSpirit Offers Page
      And I click on get gift certificate button
      And I click on the Understood button
     Then I validate the error popup stating that coupons are not available for restricted location for the brand <Brand>
  
  @NASCIGSCouponvalidateRestrictions_QAStage 
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                                   | Username                         | Password  | Loc                               | Env | 
      | NASCIGS | 7-Eleven Corporation | 572896   | https://aem-stage.americanspirit.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | NASCIGS | Murphy               | 741592   | https://aem-stage.americanspirit.com/?RBDSCode=741592 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | NASCIGS | Sheetz               | 595324   | https://aem-stage.americanspirit.com/?RBDSCode=595324 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
      | NASCIGS | Speedway             | 529181   | https://aem-stage.americanspirit.com/?RBDSCode=529181 | <EMAIL>           | Password1 | 2 Ferry St, Newark, NJ 07105, USA | QA  | 
  
  @NASCIGSCouponvalidateRestrictions_Prod
    Examples: 
      | Brand   | Retailer             | RBDScode | URL                                             | Username               | Password  | Loc                               | Env   | 
      | NASCIGS | 7-Eleven Corporation | 572896   | https://www.americanspirit.com/?RBDSCode=572896 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
      | NASCIGS | Murphy               | 741592   | https://www.americanspirit.com/?RBDSCode=741592 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
      | NASCIGS | Sheetz               | 595324   | https://www.americanspirit.com/?RBDSCode=595324 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
      | NASCIGS | Speedway             | 529181   | https://www.americanspirit.com/?RBDSCode=529181 | <EMAIL> | Password1 | 2 Ferry St, Newark, NJ 07105, USA | PROD  | 
 
  @NAS_CopyrightTextinLoginPage
Scenario Outline: Validate the user is able to view the Copyright text in NAS login page 
	 Given I am on the login page for <Brand> with login <URL> for the retailer <Retailer> with RBDS code <RBDScode>
	 And I scroll down and validate copyright text <copyrightText> for <Brand>
	 
	 
	 @NAS_CopyrightTextinLoginPage_QA
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | NASCIGS | 7-Eleven Corporation | 572896   | https://aem-stage.americanspirit.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | NASCIGS | Murphy               | 741592   | https://aem-stage.americanspirit.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | NASCIGS | Sheetz               | 595324   | https://aem-stage.americanspirit.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | NASCIGS | Speedway             | 529181   | https://aem-stage.americanspirit.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
		
	 
	 @NAS_CopyrightTextinLoginPage_Prod
	 Examples: 
	 | Brand   | Retailer             | RBDScode | URL                                                   |		   copyrightText								  |
 	 | NASCIGS | 7-Eleven Corporation | 572896   | https://www.americanspirit.com/?RBDSCode=572896 |  ©2024 Santa Fe Natural Tobacco Co. |	
	 | NASCIGS | Murphy               | 741592   | https://www.americanspirit.com/?RBDSCode=741592 |   ©2024 Santa Fe Natural Tobacco Co. | 
	 | NASCIGS | Sheetz               | 595324   | https://www.americanspirit.com/?RBDSCode=595324 |   ©2024 Santa Fe Natural Tobacco Co. |
   | NASCIGS | Speedway             | 529181   | https://www.americanspirit.com/?RBDSCode=529181 |   ©2024 Santa Fe Natural Tobacco Co. |               
	