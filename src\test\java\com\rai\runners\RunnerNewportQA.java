package com.rai.runners;

import org.testng.annotations.DataProvider;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;

@CucumberOptions(features = "src/test/resources/features/Newport", glue = { "com.rai.steps" }, tags = 
		"@NewportValidateContentPostRedemption_QA or "
		+ "@NewportCouponHome_QAstage or @NewportCouponSort_QAStage or  @NewportCouponvalidateRestrictions_QAStage or "
		+ "@NewportRedeemAtAny0.1MileStore_QA or @NewportValidateRedeemNotNow_QA or @NewportValidatefavouriteStore_QA or @NewportValidateErrorMessageNotnearbyStore_QA or @NewportMapViewCouponRedemtion_QA or "
		+ "@NewportValidateHamburgerMenu_QA or @NewportLogoutfromHamburgerMenu_QA or @NavigateNewportMobilesiteHamburgerMenu_QA or "
		+ "@NewportSGWValidations_QA or "
		+ "@NewportStoreListView_QA or @NewportStoreListSearchByZip_QA or @NewportStoreDetailsMapView_QA or @NewportStoreMapViewbyZip_QA"
		, monochrome = true, plugin = { "pretty",
		"pretty:target/cucumber-report/pretty.txt", "html:target/cucumber-report/report.html/report.html",
		"json:target/cucumber-report/cucumber.json", "junit:target/cucumber-report/cucumber-junitreport.xml",
		"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:" })

public class RunnerNewportQA extends AbstractTestNGCucumberTests {

	/***
	 * Please enable parallel = true for executing scenarios in Parallel Also
	 * number of Parallel Threads can be controlled in suite-xml file with
	 * parameter data-provider-thread-count="1"
	 */

	@Override
	@DataProvider(parallel = true)
	public Object[][] scenarios() {
		return super.scenarios();
	}
}
